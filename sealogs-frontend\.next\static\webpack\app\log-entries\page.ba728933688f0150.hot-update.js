"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/crew.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/crew.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Crew; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../daily-checks/crew-welfare */ \"(app-pages-browser)/./src/app/ui/daily-checks/crew-welfare.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/crewDuty */ \"(app-pages-browser)/./src/app/offline/models/crewDuty.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/crewWelfare_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewWelfare_LogBookEntrySection.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _vessels_actions__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/actions */ \"(app-pages-browser)/./src/app/ui/vessels/actions.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/filter/components/crew-duty-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-duty-dropdown.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./src/app/ui/crew/types.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import types from separate file\n\n\n\n\n\n\nfunction Crew(param) {\n    let { crewSections = false, allCrew, logBookEntryID, locked, logBookConfig = false, setCrewMembers, crewWelfareCheck, updateCrewWelfare, vessel = false, masterID = 0, logEntrySections, offline = false, crewMembersList } = param;\n    var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _crewMember_data_trainingStatus, _crewMember_data, _crewMember_profile, _crewMember_profile1, _crewMember_profile2, _crewMember_profile3, _crewMember_data1, _crewMember_profile4, _crewMember_data2, _crewMember_profile5, _crewMember_data3, _crewMember_profile6, _crewMember_data4, _crewMember_profile7, _crewMember_data5, _crewMember_profile8, _crewMember_data6, _crewMember_data_trainingStatus1, _crewMember_data7, _crewMember_data_trainingStatus2, _crewMember_data8, _crewMember_data9, _crewMember_data10, _crewMember_data_trainingStatus_dues, _crewMember_data_trainingStatus3, _crewMember_data11, _crewMember_data12, _crewMember_data13;\n    _s();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const crewDutyModel = new _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const lbCrewModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const lbWelfareModel = new _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allDuties, setAllDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loaded, setLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMember, setCrewMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duty, setDuty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginTime, setLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Store logoutTime as a standard Date object or null\n    const [logoutTime, setLogoutTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(crewSections);\n    const [crewConfig, setCrewConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Field labels and status\n    const [punchInStatus, setPunchInStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchInLabel, setPunchInLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign In\");\n    const [punchOutStatus, setPunchOutStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchOutLabel, setPunchOutLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign Out\");\n    const [workDetailsStatus, setWorkDetailsStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [workDetailsLabel, setWorkDetailsLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Work Details\");\n    // const [editCrew, setEditCrew] = useState(false);\n    // const [editCrewMember, setEditCrewMember] = useState(null);\n    const [crewManifestEntry, setCrewManifestEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openAddCrewMemberDialog, setopenAddCrewMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Function removed as we're now directly using handleSave\n    const [openEditLogoutTimeDialog, setOpenEditLogoutTimeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCrewTrainingDueDialog, setOpenCrewTrainingDueDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openConfirmCrewDeleteDialog, setOpenConfirmCrewDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.hasPermission)(process.env.EDIT_LOGBOOKENTRY || \"EDIT_LOGBOOKENTRY\", permissions)) {\n            setEdit_logBookEntry(true);\n        } else {\n            setEdit_logBookEntry(false);\n        }\n    };\n    const createOfflineCrewWelfareCheck = async ()=>{\n        // I need to add a 2-second delay to fix ConstraintError: Key already exists in the object store.\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const id = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n        const data = await lbWelfareModel.save({\n            id: id,\n            logBookEntryID: logBookEntryID,\n            fitness: null,\n            imSafe: null,\n            safetyActions: null,\n            waterQuality: null,\n            __typename: \"CrewWelfare_LogBookEntrySection\"\n        });\n        updateCrewWelfare(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logEntrySections && Array.isArray(logEntrySections)) {\n            const hasCrewWelfare = logEntrySections.filter((section)=>section && section.className === \"SeaLogs\\\\CrewWelfare_LogBookEntrySection\").length;\n            if (hasCrewWelfare === 0 && !crewWelfareCheck && !loaded && !createCrewWelfareCheckLoading) {\n                setLoaded(true);\n                if (offline) {\n                    createOfflineCrewWelfareCheck();\n                } else {\n                    createCrewWelfareCheck({\n                        variables: {\n                            input: {\n                                logBookEntryID: +logBookEntryID\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        logEntrySections\n    ]);\n    const [queryCrewDetail] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DETAIL_WITH_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"GetCrewDetailError\", error);\n        }\n    });\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_LIST_WITHOUT_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                }) // filter out members who are already in the crew list\n                .filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDuties();\n            // handleSetCrewConfig()\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Group crew duties by crew member\n    const groupCrewDutiesByMember = (crewData)=>{\n        if (!crewData || !Array.isArray(crewData) || crewData.length === 0) return [];\n        const groupedCrew = {};\n        // First, sort the crew data by punchIn time to ensure consistent ordering\n        const sortedCrewData = [\n            ...crewData\n        ].sort((a, b)=>{\n            if (!a || !b) return 0;\n            const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n            const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n            return timeA - timeB // Ascending order (oldest first)\n            ;\n        });\n        // Filter out archived members first\n        const activeCrewData = sortedCrewData.filter((member)=>member && !member.archived);\n        activeCrewData.forEach((member)=>{\n            if (!member) return;\n            const crewMemberId = member.crewMemberID;\n            if (!crewMemberId) return;\n            // If this member already has duties array, preserve it\n            if (member.duties && Array.isArray(member.duties) && member.duties.length > 0) {\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the existing duties\n                    groupedCrew[crewMemberId] = {\n                        ...member\n                    };\n                } else {\n                    // Merge duties from this member with existing duties\n                    const existingDuties = groupedCrew[crewMemberId].duties || [];\n                    member.duties.forEach((duty)=>{\n                        if (!duty) return;\n                        // Check if this duty is already in the list (avoid duplicates by ID)\n                        const isDuplicateById = existingDuties.some((existingDuty)=>existingDuty && existingDuty.id === duty.id);\n                        // Only add if it's not a duplicate\n                        if (!isDuplicateById) {\n                            existingDuties.push(duty);\n                        }\n                    });\n                    groupedCrew[crewMemberId] = {\n                        ...groupedCrew[crewMemberId],\n                        duties: existingDuties\n                    };\n                }\n            } else {\n                // Handle members without a duties array\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the first duty\n                    groupedCrew[crewMemberId] = {\n                        ...member,\n                        duties: [\n                            {\n                                id: member.id,\n                                dutyPerformed: member.dutyPerformed,\n                                punchIn: member.punchIn,\n                                punchOut: member.punchOut,\n                                workDetails: member.workDetails,\n                                dutyPerformedID: member.dutyPerformedID,\n                                logBookEntryID: member.logBookEntryID\n                            }\n                        ]\n                    };\n                } else if (groupedCrew[crewMemberId].duties && Array.isArray(groupedCrew[crewMemberId].duties)) {\n                    // Check if this duty is already in the list (avoid duplicates by ID)\n                    const isDuplicateById = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.id === member.id);\n                    // Also check if this is a duplicate duty type with the same time (which would be redundant)\n                    const isDuplicateDutyType = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.dutyPerformedID === member.dutyPerformedID && existingDuty.punchIn === member.punchIn);\n                    // Only add if it's not a duplicate by ID or duty type\n                    if (!isDuplicateById && !isDuplicateDutyType) {\n                        groupedCrew[crewMemberId].duties.push({\n                            id: member.id,\n                            dutyPerformed: member.dutyPerformed,\n                            punchIn: member.punchIn,\n                            punchOut: member.punchOut,\n                            workDetails: member.workDetails,\n                            dutyPerformedID: member.dutyPerformedID,\n                            logBookEntryID: member.logBookEntryID\n                        });\n                    }\n                }\n            }\n        });\n        // Sort duties by punchIn time in ascending order for each crew member\n        Object.values(groupedCrew).forEach((crewMember)=>{\n            if (crewMember && crewMember.duties && Array.isArray(crewMember.duties) && crewMember.duties.length > 1) {\n                crewMember.duties.sort((a, b)=>{\n                    if (!a || !b) return 0;\n                    const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n                    const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n                    return timeA - timeB // Ascending order (oldest first)\n                    ;\n                });\n            }\n        });\n        return Object.values(groupedCrew);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewSections && Array.isArray(crewSections)) {\n            // Process each crew member's training status\n            const processedCrewSections = crewSections.map((section)=>{\n                if (section && section.crewMember) {\n                    // Apply GetCrewListWithTrainingStatus to the crewMember property\n                    const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        section.crewMember\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        ...section,\n                        crewMember: crewMemberWithTrainingStatus\n                    };\n                }\n                return section;\n            });\n            // Preserve existing duties if they exist\n            let updatedData = processedCrewSections;\n            if (crew && Array.isArray(crew) && crew.length > 0) {\n                // Create a map of existing crew members with their duties\n                const existingCrewMap = crew.reduce((map, member)=>{\n                    if (member && member.crewMemberID) {\n                        map[member.crewMemberID] = member;\n                    }\n                    return map;\n                }, {});\n                // Check if any existing crew members have duties that need to be preserved\n                const hasExistingDuties = Object.values(existingCrewMap).some((member)=>member && member.duties && Array.isArray(member.duties) && member.duties.length > 0);\n                if (hasExistingDuties) {\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedCrewSections.map((section)=>{\n                        if (!section || !section.crewMemberID) return section;\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && Array.isArray(existingMember.duties) && existingMember.duties.length > 0) {\n                            // Check if this section's ID is already in the existing duties\n                            const dutyExists = existingMember.duties.some((duty)=>duty && duty.id === section.id);\n                            if (dutyExists) {\n                                // This section is already in the duties, so return the section with duties\n                                return {\n                                    ...section,\n                                    duties: existingMember.duties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n            }\n            // Group crew duties by crew member\n            const groupedCrewSections = groupCrewDutiesByMember(updatedData);\n            setCrew(groupedCrewSections);\n        }\n    }, [\n        crewSections,\n        vessel\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (masterID > 0) {\n            loadVesselCrews();\n        }\n    }, [\n        masterID\n    ]);\n    const loadDuties = async ()=>{\n        if (offline) {\n            const data = await crewDutyModel.getAll();\n            setAllDuties(data);\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        } else {\n            await queryDuties();\n        }\n    };\n    const handleSetCrewConfig = ()=>{\n        if (logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMembersConfigs = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const length = crewMembersConfigs.length;\n            if (length === 1) {\n                const config = crewMembersConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            } else if (length > 1) {\n                const sortedConfigs = [\n                    ...crewMembersConfigs\n                ].sort((a, b)=>parseInt(b.id) - parseInt(a.id));\n                const config = sortedConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            }\n        } else {\n            setCrewConfig(false);\n        }\n    };\n    const handleSetStatus = ()=>{\n        if (Array.isArray(crewConfig) && crewConfig.length > 0 && logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMemberComponents = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const crewMemberComponent = crewMemberComponents.length > 0 ? crewMemberComponents[0] : null;\n            if (crewMemberComponent && crewMemberComponent.customisedComponentFields && crewMemberComponent.customisedComponentFields.nodes && Array.isArray(crewMemberComponent.customisedComponentFields.nodes)) {\n                // Crew Member\n                let title = \"CrewMemberID\";\n                const crewMemberField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (crewMemberField) {\n                // We don't need to set crew member label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Primary Duty\n                title = \"DutyPerformedID\";\n                const primaryDutyField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (primaryDutyField) {\n                // We don't need to set primary duty label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Punch in\n                title = \"PunchIn\";\n                const punchInConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchInStatus((punchInConfig === null || punchInConfig === void 0 ? void 0 : punchInConfig.status) || \"On\");\n                const punchInField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchInField) {\n                    const customTitle = punchInField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchInField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchInLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchInLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign In'\n                }\n                // Punch out\n                title = \"PunchOut\";\n                const punchOutConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchOutStatus((punchOutConfig === null || punchOutConfig === void 0 ? void 0 : punchOutConfig.status) || \"On\");\n                const punchOutField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchOutField) {\n                    const customTitle = punchOutField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchOutField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchOutLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchOutLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign Out'\n                }\n                // Work details\n                title = \"WorkDetails\";\n                const workDetailsConfig = crewConfig.find((config)=>config && config.title === title);\n                setWorkDetailsStatus((workDetailsConfig === null || workDetailsConfig === void 0 ? void 0 : workDetailsConfig.status) || \"On\");\n                const workDetailsField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (workDetailsField) {\n                    const customTitle = workDetailsField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(workDetailsField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setWorkDetailsLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setWorkDetailsLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Work Details'\n                }\n            }\n        } else {\n            // Set default values if crewConfig is not valid\n            setPunchInStatus(\"On\");\n            setPunchInLabel(\"Sign In\");\n            setPunchOutStatus(\"On\");\n            setPunchOutLabel(\"Sign Out\");\n            setWorkDetailsStatus(\"On\");\n            setWorkDetailsLabel(\"Work Details\");\n        }\n    };\n    const [queryDuties] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DUTY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewDuties.nodes;\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryDutiesEntry error\", error);\n        }\n    });\n    const handleLogin = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            const currentTime = new Date();\n            setLoginTime(currentTime);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime)\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogin:\", date);\n                return;\n            }\n            setLoginTime(validDate);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n            // If logout time is set and is before the new login time, reset it\n            if (logoutTime && validDate.getTime() > logoutTime.getTime()) {\n                setLogoutTime(null);\n                setCrewManifestEntry((prev)=>({\n                        ...prev,\n                        punchOut: null\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error in handleLogin:\", error);\n            toast({\n                title: \"Error\",\n                description: \"An error occurred while setting the sign in time\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLogout = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            setLogoutTime(null);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: null\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogout:\", date);\n                return;\n            }\n            // If the date doesn't have time set (hours and minutes are 0),\n            // set the current time\n            if (validDate.getHours() === 0 && validDate.getMinutes() === 0) {\n                const now = new Date();\n                validDate.setHours(now.getHours());\n                validDate.setMinutes(now.getMinutes());\n            }\n            // Convert to dayjs for easier comparison\n            const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(validDate);\n            // Ensure logout time is after login time\n            if (loginTime && dayjsDate.isBefore(loginTime)) {\n                toast({\n                    title: \"Invalid time\",\n                    description: \"Sign out time must be after sign in time\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Store the date as a standard Date object to avoid any issues with dayjs\n            setLogoutTime(validDate);\n            // Update crew manifest entry with formatted date string\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n        } catch (error) {\n            console.error(\"Error in handleLogout:\", error);\n            toast({\n                title: \"Error\",\n                description: \"An error occurred while setting the sign out time\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddManifest = ()=>{\n        // Check permissions first\n        if (!edit_logBookEntry) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to edit this log entry\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Filter crew member options to only show available crew members\n        if (allMembers && Array.isArray(allMembers)) {\n            // Get a list of crew members who are already signed in (without sign-out time)\n            const signedInCrewMemberIDs = new Set();\n            if (crew && Array.isArray(crew)) {\n                crew.forEach((member)=>{\n                    // Only consider members who are signed in without a sign-out time\n                    if (member && member.duties && Array.isArray(member.duties)) {\n                        // Check if any duty has no punch out time\n                        const hasActiveShift = member.duties.some((duty)=>duty && duty.punchOut === null);\n                        if (hasActiveShift) {\n                            signedInCrewMemberIDs.add(member.crewMemberID);\n                        }\n                    } else if (member && member.punchOut === null) {\n                        signedInCrewMemberIDs.add(member.crewMemberID);\n                    }\n                });\n            }\n            // Filter out crew members who are already signed in\n            const availableCrewMembers = allMembers.filter((member)=>{\n                if (!member) return false;\n                return !signedInCrewMemberIDs.has(member.value);\n            });\n            // Further filter out crew members who are in the crewMembersList (if applicable)\n            const filteredCrewOptions = availableCrewMembers.filter((member)=>!member || !crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n            setCrewMemberOptions(filteredCrewOptions);\n        } else {\n            // If allMembers is not valid, just proceed with empty options\n            setCrewMemberOptions([]);\n        }\n        // Set up the new crew manifest entry with current time\n        const currentTime = new Date();\n        const crewManifestEntry = {\n            id: 0,\n            logBookEntryID: +logBookEntryID,\n            crewMemberID: 0,\n            dutyPerformedID: 0,\n            punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime),\n            punchOut: null\n        };\n        setCrewManifestEntry(crewManifestEntry);\n        setLoginTime(currentTime);\n        setLogoutTime(null);\n        setCrewMember(null);\n        setDuty(null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleEditManifest = (memberData)=>{\n        if (!edit_logBookEntry) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to edit this log entry\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // If this is a grouped crew member with multiple duties, use the first duty\n        const dutyToEdit = memberData.duties && memberData.duties.length > 0 ? memberData.duties[0] : memberData;\n        setCrewManifestEntry({\n            id: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.id,\n            logBookEntryID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.logBookEntryID,\n            crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n            dutyPerformedID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID,\n            punchIn: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn,\n            punchOut: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut,\n            workDetails: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.workDetails\n        });\n        // Create a proper crew member object with profile details\n        const crewMemberWithProfile = {\n            label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n            value: memberData.crewMember.id,\n            data: memberData.crewMember,\n            profile: {\n                firstName: memberData.crewMember.firstName,\n                surname: memberData.crewMember.surname,\n                avatar: memberData.crewMember.profileImage\n            }\n        };\n        setCrewMember(crewMemberWithProfile);\n        // Find the correct duty in the duties array\n        const selectedDuty = duties.find((memberDuty)=>memberDuty.id === (dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID));\n        if (selectedDuty) {\n            setDuty({\n                label: selectedDuty.title,\n                value: selectedDuty.id\n            });\n        }\n        setLoginTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn) ? new Date(dutyToEdit.punchIn) : new Date());\n        setLogoutTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut) ? new Date(dutyToEdit.punchOut) : null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleSignOutTime = (memberData)=>{\n        if (!edit_logBookEntry) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to edit this log entry\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Determine if this is a nested duty or a main crew member\n        const isNestedDuty = !memberData.crewMember;\n        if (isNestedDuty) {\n            // This is a nested duty\n            // Find the parent crew member for this duty\n            const parentMember = crew && Array.isArray(crew) ? crew.find((c)=>{\n                return c && c.duties && Array.isArray(c.duties) && c.duties.some((d)=>d && d.id === memberData.id);\n            }) : null;\n            if (parentMember) {\n                var _memberData_dutyPerformed;\n                // Set crew manifest entry with the parent crew member ID\n                setCrewManifestEntry({\n                    id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                    logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                    crewMemberID: parentMember.crewMemberID,\n                    dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id,\n                    punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                    punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                    workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n                });\n                // Create a proper crew member object with profile details\n                const crewMemberWithProfile = {\n                    label: \"\".concat(parentMember.crewMember.firstName || \"\", \" \").concat(parentMember.crewMember.surname !== null ? parentMember.crewMember.surname : \"\").trim(),\n                    value: parentMember.crewMember.id,\n                    data: parentMember.crewMember,\n                    profile: {\n                        firstName: parentMember.crewMember.firstName,\n                        surname: parentMember.crewMember.surname,\n                        avatar: parentMember.crewMember.profileImage\n                    }\n                };\n                setCrewMember(crewMemberWithProfile);\n                // Set duty\n                if (memberData.dutyPerformed) {\n                    const selectedDuty = duties.find((memberDuty)=>{\n                        var _memberData_dutyPerformed;\n                        return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                    });\n                    if (selectedDuty) {\n                        setDuty({\n                            label: selectedDuty.title,\n                            value: selectedDuty.id\n                        });\n                    }\n                }\n            } else {\n                // If parent member not found, show an error\n                toast({\n                    title: \"Error\",\n                    description: \"Could not find the associated crew member\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        } else {\n            var _memberData_dutyPerformed1;\n            // This is a main crew member\n            setCrewManifestEntry({\n                id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n                dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed1 = memberData.dutyPerformed) === null || _memberData_dutyPerformed1 === void 0 ? void 0 : _memberData_dutyPerformed1.id,\n                punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n            });\n            // Create a proper crew member object with profile details\n            const crewMemberWithProfile = {\n                label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n                value: memberData.crewMember.id,\n                data: memberData.crewMember,\n                profile: {\n                    firstName: memberData.crewMember.firstName,\n                    surname: memberData.crewMember.surname,\n                    avatar: memberData.crewMember.profileImage\n                }\n            };\n            setCrewMember(crewMemberWithProfile);\n            // Set duty\n            if (memberData.dutyPerformed) {\n                const selectedDuty = duties.find((memberDuty)=>{\n                    var _memberData_dutyPerformed;\n                    return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                });\n                if (selectedDuty) {\n                    setDuty({\n                        label: selectedDuty.title,\n                        value: selectedDuty.id\n                    });\n                }\n            }\n        }\n        // Set times\n        setLoginTime(memberData.punchIn ? new Date(memberData.punchIn) : new Date());\n        setLogoutTime(memberData.punchOut ? new Date(memberData.punchOut) : null);\n        setOpenEditLogoutTimeDialog(true);\n    };\n    const handleCrewMember = async (selected)=>{\n        if (!selected) return;\n        setDuty({\n            label: \"-- Select Duty --\",\n            value: 0\n        });\n        const { data } = await queryCrewDetail({\n            variables: {\n                crewMemberID: selected.value\n            }\n        });\n        const member = data.readOneSeaLogsMember;\n        const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            member\n        ], [\n            vessel\n        ])[0];\n        const value = {\n            ...selected,\n            data: crewWithTraining\n        };\n        setCrewMember(value);\n        // Check if the crew has a training due\n        if (value.data && value.data.trainingStatus && value.data.trainingStatus.label !== \"Good\") {\n            setOpenCrewTrainingDueDialog(true);\n        }\n        // Set default duty\n        if (allCrew && Array.isArray(allCrew)) {\n            const crewMember = allCrew.find((member)=>member && member.id === value.value);\n            if (crewMember && crewMember.primaryDutyID) {\n                if (duties && Array.isArray(duties)) {\n                    const crewDuty = duties.find((d)=>d && d.id === crewMember.primaryDutyID);\n                    if (crewDuty) {\n                        const newDuty = {\n                            label: crewDuty.title,\n                            value: crewDuty.id\n                        };\n                        setDuty(newDuty);\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id,\n                            dutyPerformedID: crewDuty.id\n                        });\n                    } else {\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id\n                        });\n                    }\n                } else {\n                    setCrewManifestEntry({\n                        ...crewManifestEntry,\n                        crewMemberID: crewMember.id\n                    });\n                }\n            } else if (crewMember) {\n                setCrewManifestEntry({\n                    ...crewManifestEntry,\n                    crewMemberID: crewMember.id\n                });\n            }\n        }\n    };\n    const handleDuty = (value)=>{\n        setDuty(value);\n        setCrewManifestEntry({\n            ...crewManifestEntry,\n            dutyPerformedID: (value === null || value === void 0 ? void 0 : value.value) || 0\n        });\n    };\n    const handleCancel = ()=>{\n        setCrewManifestEntry({});\n        setCrewMember(null);\n        setDuty(null);\n        setLoginTime(new Date());\n        setLogoutTime(null);\n        setopenAddCrewMemberDialog(false);\n        setOpenEditLogoutTimeDialog(false);\n    };\n    const handleSave = async (callBy)=>{\n        // Validate required fields\n        if (!crewManifestEntry.crewMemberID || crewManifestEntry.crewMemberID === 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a crew member\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!crewManifestEntry.dutyPerformedID || crewManifestEntry.dutyPerformedID === 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a duty\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Get work details from the textarea\n        const workDetailsElement = document.getElementById(\"work-details\");\n        const workDetails = (workDetailsElement === null || workDetailsElement === void 0 ? void 0 : workDetailsElement.value) || \"\";\n        const variables = {\n            id: crewManifestEntry.id,\n            crewMemberID: crewManifestEntry.crewMemberID,\n            dutyPerformedID: +(crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.dutyPerformedID),\n            logBookEntryID: +logBookEntryID,\n            punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n            punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n            workDetails: workDetails\n        };\n        try {\n            // Case 1: Updating an existing crew entry\n            if (crewManifestEntry.id > 0) {\n                if (offline) {\n                    // Save the updated crew member to the database\n                    await lbCrewModel.save(variables);\n                    // Get all crew IDs to fetch updated data\n                    const crewIds = crew && Array.isArray(crew) ? crew.map((c)=>c.id).filter(Boolean) : [];\n                    // Reset the form\n                    setCrewManifestEntry({});\n                    // Fetch the updated crew data\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedData = groupCrewDutiesByMember(processedData);\n                        // Update state\n                        setCrew(groupedData);\n                        setCrewMembers(groupedData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    updateCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialogs\n                setopenAddCrewMemberDialog(false);\n                if (callBy === \"update\") {\n                    setOpenEditLogoutTimeDialog(false);\n                }\n            } else if (crewManifestEntry.crewMemberID > 0) {\n                if (offline) {\n                    // Generate a unique ID for the new entry\n                    const uniqueId = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n                    const data = {\n                        ...variables,\n                        id: uniqueId\n                    };\n                    // Save the new crew member to the database\n                    await lbCrewModel.save(data);\n                    // Get the selected crew member and duty information\n                    const selectedMember = allVesselCrews.find((c)=>c.id === crewManifestEntry.crewMemberID);\n                    const selectedDuty = allDuties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n                    if (!selectedMember || !selectedDuty) {\n                        toast({\n                            title: \"Error\",\n                            description: \"Could not find crew member or duty information\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    // Create a new crew entry with the necessary data for immediate display\n                    const newCrewEntry = {\n                        ...data,\n                        crewMember: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            selectedMember\n                        ], [\n                            vessel\n                        ])[0],\n                        dutyPerformed: selectedDuty\n                    };\n                    // Get existing crew data or initialize empty array\n                    const existingCrew = Array.isArray(crew) ? [\n                        ...crew\n                    ] : [];\n                    // Check if this crew member already exists in the list\n                    const existingCrewMemberIndex = existingCrew.findIndex((c)=>c && c.crewMemberID === data.crewMemberID);\n                    let updatedCrewData = [\n                        ...existingCrew\n                    ];\n                    if (existingCrewMemberIndex !== -1) {\n                        // If the crew member already exists, add this duty to their duties array\n                        const existingCrewMember = {\n                            ...updatedCrewData[existingCrewMemberIndex]\n                        };\n                        if (existingCrewMember.duties && Array.isArray(existingCrewMember.duties)) {\n                            // Add the new duty to the existing duties array\n                            existingCrewMember.duties.push({\n                                id: data.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: data.punchIn,\n                                punchOut: data.punchOut,\n                                workDetails: data.workDetails,\n                                dutyPerformedID: data.dutyPerformedID,\n                                logBookEntryID: data.logBookEntryID\n                            });\n                        } else {\n                            // Create a duties array if it doesn't exist\n                            existingCrewMember.duties = [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ];\n                        }\n                        // Update the crew member in the array\n                        updatedCrewData[existingCrewMemberIndex] = existingCrewMember;\n                    } else {\n                        // If this is a new crew member, add them to the list with their first duty\n                        updatedCrewData.push({\n                            ...newCrewEntry,\n                            duties: [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ]\n                        });\n                    }\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                    setCrewManifestEntry({});\n                    // Also fetch the latest data from the database to ensure consistency\n                    const crewIds = updatedCrewData.map((c)=>c && c.id).filter(Boolean).concat([\n                        uniqueId\n                    ]);\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedDbData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedDbData = groupCrewDutiesByMember(processedDbData);\n                        // Update with the database data to ensure consistency\n                        setCrew(groupedDbData);\n                        setCrewMembers(groupedDbData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    createCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialog\n                setopenAddCrewMemberDialog(false);\n            } else {\n                // No valid crew member selected, just cancel\n                handleCancel();\n            }\n        } catch (error) {\n            console.error(\"Error saving crew member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save crew member. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const [updateCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateCrewMembers_LogBookEntrySection, {\n        onCompleted: ()=>{\n            // First, update the UI immediately with the updated data\n            if (crewManifestEntry.id > 0 && crew && Array.isArray(crew)) {\n                // Create a deep copy of the current crew data\n                let updatedCrewData = JSON.parse(JSON.stringify(crew));\n                // Find the crew member and duty that was updated\n                let foundAndUpdated = false;\n                // Loop through all crew members\n                for(let i = 0; i < updatedCrewData.length; i++){\n                    const member = updatedCrewData[i];\n                    // Check if this is the main duty that was updated\n                    if (member.id === crewManifestEntry.id) {\n                        // Update the main duty\n                        updatedCrewData[i] = {\n                            ...member,\n                            punchOut: crewManifestEntry.punchOut\n                        };\n                        foundAndUpdated = true;\n                        break;\n                    }\n                    // Check if this is a nested duty that was updated\n                    if (member.duties && Array.isArray(member.duties)) {\n                        for(let j = 0; j < member.duties.length; j++){\n                            const duty = member.duties[j];\n                            if (duty.id === crewManifestEntry.id) {\n                                // Update the nested duty\n                                member.duties[j] = {\n                                    ...duty,\n                                    punchOut: crewManifestEntry.punchOut\n                                };\n                                foundAndUpdated = true;\n                                break;\n                            }\n                        }\n                        if (foundAndUpdated) {\n                            break;\n                        }\n                    }\n                }\n                if (foundAndUpdated) {\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            searchFilter.archived = {\n                eq: false\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"updateCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [createCrewWelfareCheck, { loading: createCrewWelfareCheckLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewWelfare_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createCrewWelfare_LogBookEntrySection;\n            updateCrewWelfare(data);\n        },\n        onError: (error)=>{\n            console.error(\"createCrewWelfareCheck\", error);\n        }\n    });\n    const [createCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewMembers_LogBookEntrySection, {\n        onCompleted: (data)=>{\n            var _allMembers_find;\n            // First, update the UI immediately with the new crew member\n            // Get the selected crew member and duty information\n            const selectedMember = (_allMembers_find = allMembers.find((m)=>m.value === crewManifestEntry.crewMemberID)) === null || _allMembers_find === void 0 ? void 0 : _allMembers_find.data;\n            const selectedDuty = duties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n            if (selectedMember && selectedDuty) {\n                var _document_getElementById;\n                // Create a new crew entry with the necessary data for immediate display\n                const newCrewEntry = {\n                    id: data.createCrewMembers_LogBookEntrySection.id,\n                    crewMemberID: crewManifestEntry.crewMemberID,\n                    dutyPerformedID: crewManifestEntry.dutyPerformedID,\n                    logBookEntryID: +logBookEntryID,\n                    punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n                    punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n                    workDetails: ((_document_getElementById = document.getElementById(\"work-details\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value) || \"\",\n                    crewMember: selectedMember,\n                    dutyPerformed: selectedDuty\n                };\n                // Create a new array with existing crew data plus the new entry\n                let updatedCrewData = crew ? [\n                    ...crew\n                ] : [];\n                // Check if this crew member already exists in the list\n                const existingCrewMemberIndex = updatedCrewData.findIndex((c)=>c.crewMemberID === crewManifestEntry.crewMemberID);\n                if (existingCrewMemberIndex >= 0) {\n                    // If the crew member already exists, add this duty to their duties array\n                    const existingCrewMember = updatedCrewData[existingCrewMemberIndex];\n                    const updatedDuties = existingCrewMember.duties ? [\n                        ...existingCrewMember.duties\n                    ] : [];\n                    updatedDuties.push({\n                        id: data.createCrewMembers_LogBookEntrySection.id,\n                        dutyPerformed: selectedDuty,\n                        punchIn: newCrewEntry.punchIn,\n                        punchOut: newCrewEntry.punchOut,\n                        workDetails: newCrewEntry.workDetails,\n                        dutyPerformedID: newCrewEntry.dutyPerformedID,\n                        logBookEntryID: newCrewEntry.logBookEntryID\n                    });\n                    // Update the crew member with the new duties array\n                    updatedCrewData[existingCrewMemberIndex] = {\n                        ...existingCrewMember,\n                        duties: updatedDuties\n                    };\n                } else {\n                    // If this is a new crew member, add them to the list with their first duty\n                    updatedCrewData.push({\n                        ...newCrewEntry,\n                        duties: [\n                            {\n                                id: data.createCrewMembers_LogBookEntrySection.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: newCrewEntry.punchIn,\n                                punchOut: newCrewEntry.punchOut,\n                                workDetails: newCrewEntry.workDetails,\n                                dutyPerformedID: newCrewEntry.dutyPerformedID,\n                                logBookEntryID: newCrewEntry.logBookEntryID\n                            }\n                        ]\n                    });\n                }\n                // Group the updated crew data\n                const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                // Update state with the new grouped data\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = crew ? [\n                ...crew === null || crew === void 0 ? void 0 : crew.map((c)=>c.id),\n                data.createCrewMembers_LogBookEntrySection.id\n            ] : [\n                data.createCrewMembers_LogBookEntrySection.id\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"createCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewMembers_LogBookEntrySections.nodes;\n            if (data) {\n                // Process crew members with training status\n                const processedData = data.map((section)=>{\n                    if (section.crewMember) {\n                        const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            section.crewMember\n                        ], [\n                            vessel\n                        ])[0];\n                        return {\n                            ...section,\n                            crewMember: crewMemberWithTrainingStatus\n                        };\n                    }\n                    return section;\n                });\n                // Preserve existing duties if they exist\n                let updatedData = processedData;\n                if (crew && crew.length > 0) {\n                    // Create a map of existing crew members with their duties\n                    const existingCrewMap = crew.reduce((map, member)=>{\n                        if (member.crewMemberID) {\n                            map[member.crewMemberID] = member;\n                        }\n                        return map;\n                    }, {});\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedData.map((section)=>{\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && existingMember.duties.length > 0) {\n                            // Find the matching duty in the existing duties array\n                            const existingDutyIndex = existingMember.duties.findIndex((duty)=>duty.id === section.id);\n                            if (existingDutyIndex >= 0) {\n                                // This section is already in the duties, update it with the latest data\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties[existingDutyIndex] = {\n                                    ...updatedDuties[existingDutyIndex],\n                                    // Update with the latest data from the server\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails\n                                };\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n                // Group crew duties by crew member\n                const groupedData = groupCrewDutiesByMember(updatedData);\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n                const members = allMembers.filter((member)=>{\n                    if (!data) {\n                        return true;\n                    }\n                    return !data.some((section)=>section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members.filter((member)=>!crewMembersList || !crewMembersList.includes(+member.value)));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getSectionCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const handleArchive = async ()=>{\n        setOpenConfirmCrewDeleteDialog(false);\n        if (!crewManifestEntry.id) {\n            toast({\n                title: \"Error\",\n                description: \"No crew member selected to delete\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            try {\n                // First try to delete the record\n                const result = await lbCrewModel.delete({\n                    id: crewManifestEntry.id\n                });\n                if (!result) {\n                    // If delete fails, mark as archived\n                    await lbCrewModel.save({\n                        id: crewManifestEntry.id,\n                        archived: true\n                    });\n                }\n                const appendData = [\n                    ...crew.map((c)=>c.id)\n                ];\n                setCrewManifestEntry({});\n                const data = await lbCrewModel.getByIds(appendData);\n                if (data) {\n                    // Process crew members with training status\n                    const processedData = data.map((section)=>{\n                        if (section.crewMember) {\n                            const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                section.crewMember\n                            ], [\n                                vessel\n                            ])[0];\n                            return {\n                                ...section,\n                                crewMember: crewMemberWithTrainingStatus\n                            };\n                        }\n                        return section;\n                    });\n                    // Group crew duties by crew member\n                    const groupedData = groupCrewDutiesByMember(processedData);\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to delete crew member\",\n                    variant: \"destructive\"\n                });\n            }\n        } else {\n            try {\n                // Use the delete mutation instead of update\n                await deleteCrewMembersLogBookEntrySections({\n                    variables: {\n                        ids: [\n                            crewManifestEntry.id\n                        ]\n                    }\n                });\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to delete crew member\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n        setopenAddCrewMemberDialog(false);\n    };\n    // Function removed as we're directly using setOpenConfirmCrewDeleteDialog\n    const [deleteCrewMembersLogBookEntrySections] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.DeleteCrewMembers_LogBookEntrySections, {\n        onCompleted: ()=>{\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n            setOpenConfirmCrewDeleteDialog(false);\n            setopenAddCrewMemberDialog(false);\n        },\n        onError: (error)=>{\n            console.error(\"deleteCrewMembersLogBookEntrySections error:\", error);\n        }\n    });\n    // Function removed as we're using handleArchive instead\n    const crewCount = ()=>{\n        if (!crew || !Array.isArray(crew)) return 0;\n        const count = crew.filter((member)=>member && member.crewMemberID > 0 && member.punchOut === null).length;\n        return count;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewConfig) {\n            handleSetStatus();\n        }\n    }, [\n        crewConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default()(logBookConfig)) {\n            handleSetCrewConfig();\n        }\n    }, [\n        logBookConfig\n    ]);\n    var _vessel_maxPOB, _vessel_maxPOB1;\n    // Removed unused overdueTextWarning variable\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-8 gap-36 lg:gap-6 xl:gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-5 space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H2, {\n                                children: \"Crew\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2064,\n                                columnNumber: 21\n                            }, this),\n                            crew ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"pl-2.5 text-left align-bottom standard:align-top\",\n                                                            children: \"Crew\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2070,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"px-[5px] text-left align-bottom standard:align-top\",\n                                                            children: \"Duty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2073,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"px-[5px] text-right\",\n                                                                    children: punchInLabel || \"Sign In\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2080,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"pl-[5px] pr-2.5 text-right\",\n                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2087,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"text-wrap standard:text-nowrap pr-0 text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    punchInStatus !== \"Off\" ? punchInLabel || \"Sign In\" : \"\",\n                                                                    \"/\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                        className: \"standard:hidden\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2101,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    punchOutStatus !== \"Off\" ? punchOutLabel || \"Sign Out\" : \"\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2094,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2069,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2068,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                                                children: crew.filter((member)=>+member.crewMemberID > 0 && member.archived === false).map((member)=>{\n                                                    var _member_crewMember_trainingStatus;\n                                                    // Check if member has multiple duties\n                                                    const hasMultipleDuties = member.duties && Array.isArray(member.duties) && member.duties.length > 1;\n                                                    // Get additional duties (if any)\n                                                    const additionalDuties = hasMultipleDuties ? member.duties.slice(1).filter((duty)=>{\n                                                        // Get the first duty's title\n                                                        const firstDutyTitle = member.duties[0].dutyPerformed && member.duties[0].dutyPerformed.title;\n                                                        // Get current duty's title\n                                                        const currentDutyTitle = duty.dutyPerformed && duty.dutyPerformed.title;\n                                                        // Only include duties with different titles\n                                                        return currentDutyTitle && firstDutyTitle !== currentDutyTitle;\n                                                    }) : [];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                \"aria-disabled\": locked,\n                                                                className: \"group \".concat(hasMultipleDuties ? \"border-b-0\" : \"\"),\n                                                                onClick: (e)=>{\n                                                                    // Don't do anything if locked\n                                                                    if (locked) return;\n                                                                    // Prevent row click if the event originated from a button\n                                                                    if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                        return;\n                                                                    }\n                                                                    handleEditManifest(member);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left\", additionalDuties.length > 0 && \" text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                                                                    size: \"sm\",\n                                                                                    variant: ((_member_crewMember_trainingStatus = member.crewMember.trainingStatus) === null || _member_crewMember_trainingStatus === void 0 ? void 0 : _member_crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                                                                        children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(member.crewMember.firstName, member.crewMember.surname)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2208,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2197,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"hidden leading-none sm:flex flex-col justify-center ml-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"flex gap-2.5 items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-foreground\",\n                                                                                                children: [\n                                                                                                    member.crewMember.firstName,\n                                                                                                    \" \",\n                                                                                                    member.crewMember.surname\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2221,\n                                                                                                columnNumber: 73\n                                                                                            }, this),\n                                                                                            member.workDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.Popover, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverTrigger, {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                        },\n                                                                                                        className: \"p-0 text-muted-foreground\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                                            className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2242,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2235,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverContent, {\n                                                                                                        className: \"w-80\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: member.workDetails\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2250,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2249,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2234,\n                                                                                                columnNumber: 77\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2220,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2219,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2196,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2189,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left grid items-center\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"truncate\",\n                                                                            children: member.dutyPerformed && member.dutyPerformed.title ? member.dutyPerformed.title : \"Not assigned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2270,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2263,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                                children: punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2284,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right phablet:pr-2.5 relaive\", additionalDuties.length > 0 && \"text-input\"),\n                                                                                children: punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2310,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2330,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2300,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right relaive flex flex-col phablet:pr-2.5 justify-center items-end\", additionalDuties.length > 0 && \"text-input\"),\n                                                                            children: [\n                                                                                punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\"),\n                                                                                punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2361,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2381,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2343,\n                                                                            columnNumber: 65\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2342,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                ]\n                                                            }, member.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                lineNumber: 2164,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            additionalDuties.map((duty, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                    \"aria-disabled\": locked,\n                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"group\", index === additionalDuties.length - 1 ? \"\" : \"border-b-0\"),\n                                                                    onClick: (e)=>{\n                                                                        // Don't do anything if locked\n                                                                        if (locked) return;\n                                                                        // Prevent row click if the event originated from a button\n                                                                        if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                            return;\n                                                                        }\n                                                                        handleEditManifest(member);\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input py-2 text-left relative\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col absolute -top-[42%] items-center w-8 h-full\",\n                                                                                children: [\n                                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-full h-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2445,\n                                                                                        columnNumber: 77\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-[1px] flex-1 border-l border-dashed border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2447,\n                                                                                        columnNumber: 73\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"size-[5px] rounded-full bg-background border border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2448,\n                                                                                        columnNumber: 73\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2442,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2438,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input grid items-center text-left\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"truncate\",\n                                                                                children: duty.dutyPerformed && duty.dutyPerformed.title ? duty.dutyPerformed.title : \"Not assigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2456,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2452,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input py-2 text-right\"),\n                                                                                    children: punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2470,\n                                                                                    columnNumber: 73\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input phablet:pr-2.5 py-2 text-right\"),\n                                                                                    children: punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2489,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2509,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2482,\n                                                                                    columnNumber: 73\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right phablet:pr-2.5 relaive flex flex-col justify-center items-end\",\n                                                                                children: [\n                                                                                    punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\"),\n                                                                                    punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2533,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2553,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2522,\n                                                                                columnNumber: 73\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2521,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, \"duty-\".concat(duty.id, \"-\").concat(index), true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2401,\n                                                                    columnNumber: 61\n                                                                }, this))\n                                                        ]\n                                                    }, \"crew-\".concat(member.id), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2161,\n                                                        columnNumber: 49\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2111,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2067,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                        className: \"mb-0 font-semibold\",\n                                                        children: \"Minimum crew:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2576,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_30__.Badge, {\n                                                        variant: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB !== void 0 ? _vessel_maxPOB : 0) ? \"destructive\" : \"success\",\n                                                        className: \"rounded-full flex items-center justify-center size-[25px]\",\n                                                        children: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) ? vessel.minCrew : 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2579,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB1 = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB1 !== void 0 ? _vessel_maxPOB1 : 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"You have more people on board than your vessel is configured to carry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2591,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2575,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                className: \"w-full tiny:w-fit px-2.5\",\n                                                disabled: locked,\n                                                onClick: handleAddManifest,\n                                                children: \"Add crew\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2599,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2574,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2066,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                    disabled: locked,\n                                    onClick: handleAddManifest,\n                                    children: \"Add crew members to this trip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2609,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2608,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2063,\n                        columnNumber: 17\n                    }, this),\n                    crew && logBookConfig && (logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.find((config)=>config.title === \"Crew Welfare\" && config.active === true)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-3 space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_logBookEntry,\n                            crewWelfareCheck: crewWelfareCheck,\n                            updateCrewWelfare: updateCrewWelfare\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2626,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2625,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2062,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openAddCrewMemberDialog,\n                setOpenDialog: setopenAddCrewMemberDialog,\n                handleCreate: handleSave,\n                handleCancel: handleCancel,\n                handleDestructiveAction: crewManifestEntry.id > 0 ? ()=>setOpenConfirmCrewDeleteDialog(true) : undefined,\n                showDestructiveAction: crewManifestEntry.id > 0,\n                destructiveActionText: \"Delete\",\n                title: crewManifestEntry.id > 0 ? \"Update crew member\" : \"Add crew member\",\n                actionText: crewManifestEntry.id > 0 ? \"Update\" : \"Add\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-top gap-4 mb-4\",\n                            children: [\n                                crewMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                    variant: ((_crewMember_data = crewMember.data) === null || _crewMember_data === void 0 ? void 0 : (_crewMember_data_trainingStatus = _crewMember_data.trainingStatus) === null || _crewMember_data_trainingStatus === void 0 ? void 0 : _crewMember_data_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    className: \"size-12 border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarImage, {\n                                            src: (_crewMember_profile = crewMember.profile) === null || _crewMember_profile === void 0 ? void 0 : _crewMember_profile.avatar,\n                                            alt: \"\".concat(((_crewMember_profile1 = crewMember.profile) === null || _crewMember_profile1 === void 0 ? void 0 : _crewMember_profile1.firstName) || \"\", \" \").concat(((_crewMember_profile2 = crewMember.profile) === null || _crewMember_profile2 === void 0 ? void 0 : _crewMember_profile2.surname) || \"\").trim()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2668,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                            children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(((_crewMember_profile3 = crewMember.profile) === null || _crewMember_profile3 === void 0 ? void 0 : _crewMember_profile3.firstName) || ((_crewMember_data1 = crewMember.data) === null || _crewMember_data1 === void 0 ? void 0 : _crewMember_data1.firstName), ((_crewMember_profile4 = crewMember.profile) === null || _crewMember_profile4 === void 0 ? void 0 : _crewMember_profile4.surname) || ((_crewMember_data2 = crewMember.data) === null || _crewMember_data2 === void 0 ? void 0 : _crewMember_data2.surname))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2672,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2660,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H3, {\n                                            className: \"text-lg\",\n                                            children: (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile5 = crewMember.profile) === null || _crewMember_profile5 === void 0 ? void 0 : _crewMember_profile5.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data3 = crewMember.data) === null || _crewMember_data3 === void 0 ? void 0 : _crewMember_data3.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile6 = crewMember.profile) === null || _crewMember_profile6 === void 0 ? void 0 : _crewMember_profile6.surname) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data4 = crewMember.data) === null || _crewMember_data4 === void 0 ? void 0 : _crewMember_data4.surname) ? \"\".concat(((_crewMember_profile7 = crewMember.profile) === null || _crewMember_profile7 === void 0 ? void 0 : _crewMember_profile7.firstName) || ((_crewMember_data5 = crewMember.data) === null || _crewMember_data5 === void 0 ? void 0 : _crewMember_data5.firstName) || \"\", \" \").concat(((_crewMember_profile8 = crewMember.profile) === null || _crewMember_profile8 === void 0 ? void 0 : _crewMember_profile8.surname) || ((_crewMember_data6 = crewMember.data) === null || _crewMember_data6 === void 0 ? void 0 : _crewMember_data6.surname) || \"\").trim() : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2683,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMember && ((crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data7 = crewMember.data) === null || _crewMember_data7 === void 0 ? void 0 : (_crewMember_data_trainingStatus1 = _crewMember_data7.trainingStatus) === null || _crewMember_data_trainingStatus1 === void 0 ? void 0 : _crewMember_data_trainingStatus1.label) !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: \"Training is overdue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2695,\n                                                    columnNumber: 41\n                                                }, this),\n                                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data8 = crewMember.data) === null || _crewMember_data8 === void 0 ? void 0 : (_crewMember_data_trainingStatus2 = _crewMember_data8.trainingStatus) === null || _crewMember_data_trainingStatus2 === void 0 ? void 0 : _crewMember_data_trainingStatus2.dues) && crewMember.data.trainingStatus.dues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: crewMember.data.trainingStatus.dues.map((due, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-destructive text-lg\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2711,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        due.trainingType.title,\n                                                                        \" \",\n                                                                        \"-\",\n                                                                        \" \",\n                                                                        due.status.label\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2714,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2708,\n                                                            columnNumber: 61\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2702,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2694,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-bright-turquoise-600\",\n                                            children: \"Training up to date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2734,\n                                            columnNumber: 37\n                                        }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2682,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2658,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-[31px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__.Combobox, {\n                                    label: \"Crew member\",\n                                    modal: true,\n                                    buttonClassName: \"w-full\",\n                                    options: crewMemberOptions.map((option)=>({\n                                            ...option,\n                                            value: String(option.value)\n                                        })),\n                                    value: crewMember,\n                                    onChange: handleCrewMember\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2741,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    label: \"Primary duty\",\n                                    crewDutyID: Number(duty === null || duty === void 0 ? void 0 : duty.value) || 0,\n                                    onChange: handleDuty,\n                                    multi: false,\n                                    modal: true,\n                                    offline: offline,\n                                    hideCreateOption: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2754,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2740,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xs:grid-cols-2 pr-px gap-[31px]\",\n                            children: [\n                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signin-date\",\n                                    modal: true,\n                                    value: loginTime,\n                                    onChange: handleLogin,\n                                    label: punchInLabel || \"Sign In\",\n                                    dateFormat: \"dd MMM,\",\n                                    placeholder: \"\".concat(punchInLabel || \"Sign In\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\",\n                                    closeOnSelect: false,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2767,\n                                    columnNumber: 29\n                                }, this),\n                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signout-date\",\n                                    modal: true,\n                                    value: logoutTime || undefined,\n                                    onChange: handleLogout,\n                                    label: punchOutLabel || \"Sign Out\",\n                                    placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\" // Keep datetime to include time picker\n                                    ,\n                                    dateFormat: \"dd MMM,\",\n                                    timeFormat: \"HH:mm\" // Explicitly set time format\n                                    ,\n                                    closeOnSelect: false,\n                                    clearable: true,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2784,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2765,\n                            columnNumber: 21\n                        }, this),\n                        workDetailsStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                            htmlFor: \"work-details\",\n                            label: workDetailsLabel || \"Work Details\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__.Textarea, {\n                                id: \"work-details\",\n                                rows: 4,\n                                className: \"w-full resize-none\",\n                                placeholder: \"Enter work details\",\n                                defaultValue: crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.workDetails\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2807,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2804,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2657,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2637,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openEditLogoutTimeDialog,\n                setOpenDialog: setOpenEditLogoutTimeDialog,\n                handleCreate: ()=>handleSave(\"update\"),\n                handleCancel: handleCancel,\n                actionText: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__.useResponsiveLabel)(\"Update Time\"),\n                cancelText: \"Cancel\",\n                size: \"sm\",\n                title: \"Update sign out time\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        modal: true,\n                        id: \"signout-date\",\n                        name: \"signout-date\",\n                        label: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        value: logoutTime || undefined,\n                        mode: \"single\",\n                        type: \"datetime\" // Keep datetime to include time picker\n                        ,\n                        onChange: handleLogout,\n                        dateFormat: \"dd MMM,\",\n                        timeFormat: \"HH:mm\" // Explicitly set time format\n                        ,\n                        placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        closeOnSelect: false,\n                        clearable: true,\n                        icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2830,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2829,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2819,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openCrewTrainingDueDialog,\n                setOpenDialog: setOpenCrewTrainingDueDialog,\n                contentClassName: \"max-w-xl\",\n                className: \"space-y-4\",\n                cancelText: \"Cancel\",\n                actionText: \"Yes, Continue\",\n                handleCreate: ()=>setOpenCrewTrainingDueDialog(false),\n                handleCancel: ()=>{\n                    setOpenCrewTrainingDueDialog(false);\n                    setCrewMember(null);\n                    setDuty(null);\n                },\n                title: \"Crew member training status\",\n                variant: \"warning\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data9 = crewMember.data) === null || _crewMember_data9 === void 0 ? void 0 : _crewMember_data9.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data10 = crewMember.data) === null || _crewMember_data10 === void 0 ? void 0 : _crewMember_data10.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"This crew member\",\n                                \" \",\n                                \"has overdue training sessions on this vessel. These sessions are:\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2867,\n                            columnNumber: 21\n                        }, this),\n                        crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data11 = crewMember.data) === null || _crewMember_data11 === void 0 ? void 0 : (_crewMember_data_trainingStatus3 = _crewMember_data11.trainingStatus) === null || _crewMember_data_trainingStatus3 === void 0 ? void 0 : (_crewMember_data_trainingStatus_dues = _crewMember_data_trainingStatus3.dues) === null || _crewMember_data_trainingStatus_dues === void 0 ? void 0 : _crewMember_data_trainingStatus_dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2879,\n                                    columnNumber: 33\n                                }, this)\n                            }, dueIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2878,\n                                columnNumber: 29\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Do you still want to add this crew member to this vessel?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2886,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2866,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2850,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openConfirmCrewDeleteDialog,\n                setOpenDialog: setOpenConfirmCrewDeleteDialog,\n                handleCreate: handleArchive,\n                handleCancel: ()=>{\n                    setOpenConfirmCrewDeleteDialog(false);\n                // Don't reset crew member here as it's needed for the parent dialog\n                },\n                actionText: \"Remove\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-md\",\n                variant: \"warning\",\n                showIcon: true,\n                title: \"Remove crew member\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Are you sure you want to remove\",\n                        \" \",\n                        (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data12 = crewMember.data) === null || _crewMember_data12 === void 0 ? void 0 : _crewMember_data12.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data13 = crewMember.data) === null || _crewMember_data13 === void 0 ? void 0 : _crewMember_data13.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"this crew member\",\n                        \" \",\n                        \"from this trip manifest?\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2907,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2893,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Crew, \"k134ZhWDAdu4BmCUE/d0H0tVzGk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__.useResponsiveLabel\n    ];\n});\n_c = Crew;\nvar _c;\n$RefreshReg$(_c, \"Crew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/crew.tsx\n"));

/***/ })

});