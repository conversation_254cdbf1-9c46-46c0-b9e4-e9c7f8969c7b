"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/crew.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/crew.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Crew; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../daily-checks/crew-welfare */ \"(app-pages-browser)/./src/app/ui/daily-checks/crew-welfare.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/crewDuty */ \"(app-pages-browser)/./src/app/offline/models/crewDuty.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/crewWelfare_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewWelfare_LogBookEntrySection.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _vessels_actions__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/actions */ \"(app-pages-browser)/./src/app/ui/vessels/actions.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/filter/components/crew-duty-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-duty-dropdown.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./src/app/ui/crew/types.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import types from separate file\n\n\n\n\n\n\nfunction Crew(param) {\n    let { crewSections = false, allCrew, logBookEntryID, locked, logBookConfig = false, setCrewMembers, crewWelfareCheck, updateCrewWelfare, vessel = false, masterID = 0, logEntrySections, offline = false, crewMembersList } = param;\n    var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _crewMember_data_trainingStatus, _crewMember_data, _crewMember_profile, _crewMember_profile1, _crewMember_profile2, _crewMember_profile3, _crewMember_data1, _crewMember_profile4, _crewMember_data2, _crewMember_profile5, _crewMember_data3, _crewMember_profile6, _crewMember_data4, _crewMember_profile7, _crewMember_data5, _crewMember_profile8, _crewMember_data6, _crewMember_data_trainingStatus1, _crewMember_data7, _crewMember_data_trainingStatus2, _crewMember_data8, _crewMember_data9, _crewMember_data10, _crewMember_data_trainingStatus_dues, _crewMember_data_trainingStatus3, _crewMember_data11, _crewMember_data12, _crewMember_data13;\n    _s();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const crewDutyModel = new _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const lbCrewModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const lbWelfareModel = new _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allDuties, setAllDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loaded, setLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMember, setCrewMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duty, setDuty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginTime, setLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Store logoutTime as a standard Date object or null\n    const [logoutTime, setLogoutTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(crewSections);\n    const [crewConfig, setCrewConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Field labels and status\n    const [punchInStatus, setPunchInStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchInLabel, setPunchInLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign In\");\n    const [punchOutStatus, setPunchOutStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchOutLabel, setPunchOutLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign Out\");\n    const [workDetailsStatus, setWorkDetailsStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [workDetailsLabel, setWorkDetailsLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Work Details\");\n    // const [editCrew, setEditCrew] = useState(false);\n    // const [editCrewMember, setEditCrewMember] = useState(null);\n    const [crewManifestEntry, setCrewManifestEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openAddCrewMemberDialog, setopenAddCrewMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Function removed as we're now directly using handleSave\n    const [openEditLogoutTimeDialog, setOpenEditLogoutTimeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCrewTrainingDueDialog, setOpenCrewTrainingDueDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openConfirmCrewDeleteDialog, setOpenConfirmCrewDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.hasPermission)(process.env.EDIT_LOGBOOKENTRY || \"EDIT_LOGBOOKENTRY\", permissions)) {\n            setEdit_logBookEntry(true);\n        } else {\n            setEdit_logBookEntry(false);\n        }\n    };\n    const createOfflineCrewWelfareCheck = async ()=>{\n        // I need to add a 2-second delay to fix ConstraintError: Key already exists in the object store.\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const id = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n        const data = await lbWelfareModel.save({\n            id: id,\n            logBookEntryID: logBookEntryID,\n            fitness: null,\n            imSafe: null,\n            safetyActions: null,\n            waterQuality: null,\n            __typename: \"CrewWelfare_LogBookEntrySection\"\n        });\n        updateCrewWelfare(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logEntrySections && Array.isArray(logEntrySections)) {\n            const hasCrewWelfare = logEntrySections.filter((section)=>section && section.className === \"SeaLogs\\\\CrewWelfare_LogBookEntrySection\").length;\n            if (hasCrewWelfare === 0 && !crewWelfareCheck && !loaded && !createCrewWelfareCheckLoading) {\n                setLoaded(true);\n                if (offline) {\n                    createOfflineCrewWelfareCheck();\n                } else {\n                    createCrewWelfareCheck({\n                        variables: {\n                            input: {\n                                logBookEntryID: +logBookEntryID\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        logEntrySections\n    ]);\n    const [queryCrewDetail] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DETAIL_WITH_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"GetCrewDetailError\", error);\n        }\n    });\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_LIST_WITHOUT_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                }) // filter out members who are already in the crew list\n                .filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDuties();\n            // handleSetCrewConfig()\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Group crew duties by crew member\n    const groupCrewDutiesByMember = (crewData)=>{\n        if (!crewData || !Array.isArray(crewData) || crewData.length === 0) return [];\n        const groupedCrew = {};\n        // First, sort the crew data by punchIn time to ensure consistent ordering\n        const sortedCrewData = [\n            ...crewData\n        ].sort((a, b)=>{\n            if (!a || !b) return 0;\n            const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n            const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n            return timeA - timeB // Ascending order (oldest first)\n            ;\n        });\n        // Filter out archived members first\n        const activeCrewData = sortedCrewData.filter((member)=>member && !member.archived);\n        activeCrewData.forEach((member)=>{\n            if (!member) return;\n            const crewMemberId = member.crewMemberID;\n            if (!crewMemberId) return;\n            // If this member already has duties array, preserve it\n            if (member.duties && Array.isArray(member.duties) && member.duties.length > 0) {\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the existing duties\n                    groupedCrew[crewMemberId] = {\n                        ...member\n                    };\n                } else {\n                    // Merge duties from this member with existing duties\n                    const existingDuties = groupedCrew[crewMemberId].duties || [];\n                    member.duties.forEach((duty)=>{\n                        if (!duty) return;\n                        // Check if this duty is already in the list (avoid duplicates by ID)\n                        const isDuplicateById = existingDuties.some((existingDuty)=>existingDuty && existingDuty.id === duty.id);\n                        // Only add if it's not a duplicate\n                        if (!isDuplicateById) {\n                            existingDuties.push(duty);\n                        }\n                    });\n                    groupedCrew[crewMemberId] = {\n                        ...groupedCrew[crewMemberId],\n                        duties: existingDuties\n                    };\n                }\n            } else {\n                // Handle members without a duties array\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the first duty\n                    groupedCrew[crewMemberId] = {\n                        ...member,\n                        duties: [\n                            {\n                                id: member.id,\n                                dutyPerformed: member.dutyPerformed,\n                                punchIn: member.punchIn,\n                                punchOut: member.punchOut,\n                                workDetails: member.workDetails,\n                                dutyPerformedID: member.dutyPerformedID,\n                                logBookEntryID: member.logBookEntryID\n                            }\n                        ]\n                    };\n                } else if (groupedCrew[crewMemberId].duties && Array.isArray(groupedCrew[crewMemberId].duties)) {\n                    // Check if this duty is already in the list (avoid duplicates by ID)\n                    const isDuplicateById = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.id === member.id);\n                    // Also check if this is a duplicate duty type with the same time (which would be redundant)\n                    const isDuplicateDutyType = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.dutyPerformedID === member.dutyPerformedID && existingDuty.punchIn === member.punchIn);\n                    // Only add if it's not a duplicate by ID or duty type\n                    if (!isDuplicateById && !isDuplicateDutyType) {\n                        groupedCrew[crewMemberId].duties.push({\n                            id: member.id,\n                            dutyPerformed: member.dutyPerformed,\n                            punchIn: member.punchIn,\n                            punchOut: member.punchOut,\n                            workDetails: member.workDetails,\n                            dutyPerformedID: member.dutyPerformedID,\n                            logBookEntryID: member.logBookEntryID\n                        });\n                    }\n                }\n            }\n        });\n        // Sort duties by punchIn time in ascending order for each crew member\n        Object.values(groupedCrew).forEach((crewMember)=>{\n            if (crewMember && crewMember.duties && Array.isArray(crewMember.duties) && crewMember.duties.length > 1) {\n                crewMember.duties.sort((a, b)=>{\n                    if (!a || !b) return 0;\n                    const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n                    const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n                    return timeA - timeB // Ascending order (oldest first)\n                    ;\n                });\n            }\n        });\n        return Object.values(groupedCrew);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewSections && Array.isArray(crewSections)) {\n            // Process each crew member's training status\n            const processedCrewSections = crewSections.map((section)=>{\n                if (section && section.crewMember) {\n                    // Apply GetCrewListWithTrainingStatus to the crewMember property\n                    const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        section.crewMember\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        ...section,\n                        crewMember: crewMemberWithTrainingStatus\n                    };\n                }\n                return section;\n            });\n            // Preserve existing duties if they exist\n            let updatedData = processedCrewSections;\n            if (crew && Array.isArray(crew) && crew.length > 0) {\n                // Create a map of existing crew members with their duties\n                const existingCrewMap = crew.reduce((map, member)=>{\n                    if (member && member.crewMemberID) {\n                        map[member.crewMemberID] = member;\n                    }\n                    return map;\n                }, {});\n                // Check if any existing crew members have duties that need to be preserved\n                const hasExistingDuties = Object.values(existingCrewMap).some((member)=>member && member.duties && Array.isArray(member.duties) && member.duties.length > 0);\n                if (hasExistingDuties) {\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedCrewSections.map((section)=>{\n                        if (!section || !section.crewMemberID) return section;\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && Array.isArray(existingMember.duties) && existingMember.duties.length > 0) {\n                            // Check if this section's ID is already in the existing duties\n                            const dutyExists = existingMember.duties.some((duty)=>duty && duty.id === section.id);\n                            if (dutyExists) {\n                                // This section is already in the duties, so return the section with duties\n                                return {\n                                    ...section,\n                                    duties: existingMember.duties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n            }\n            // Group crew duties by crew member\n            const groupedCrewSections = groupCrewDutiesByMember(updatedData);\n            setCrew(groupedCrewSections);\n        }\n    }, [\n        crewSections,\n        vessel\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (masterID > 0) {\n            loadVesselCrews();\n        }\n    }, [\n        masterID\n    ]);\n    const loadDuties = async ()=>{\n        if (offline) {\n            const data = await crewDutyModel.getAll();\n            setAllDuties(data);\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        } else {\n            await queryDuties();\n        }\n    };\n    const handleSetCrewConfig = ()=>{\n        if (logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMembersConfigs = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const length = crewMembersConfigs.length;\n            if (length === 1) {\n                const config = crewMembersConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            } else if (length > 1) {\n                const sortedConfigs = [\n                    ...crewMembersConfigs\n                ].sort((a, b)=>parseInt(b.id) - parseInt(a.id));\n                const config = sortedConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            }\n        } else {\n            setCrewConfig(false);\n        }\n    };\n    const handleSetStatus = ()=>{\n        if (Array.isArray(crewConfig) && crewConfig.length > 0 && logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMemberComponents = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const crewMemberComponent = crewMemberComponents.length > 0 ? crewMemberComponents[0] : null;\n            if (crewMemberComponent && crewMemberComponent.customisedComponentFields && crewMemberComponent.customisedComponentFields.nodes && Array.isArray(crewMemberComponent.customisedComponentFields.nodes)) {\n                // Crew Member\n                let title = \"CrewMemberID\";\n                const crewMemberField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (crewMemberField) {\n                // We don't need to set crew member label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Primary Duty\n                title = \"DutyPerformedID\";\n                const primaryDutyField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (primaryDutyField) {\n                // We don't need to set primary duty label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Punch in\n                title = \"PunchIn\";\n                const punchInConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchInStatus((punchInConfig === null || punchInConfig === void 0 ? void 0 : punchInConfig.status) || \"On\");\n                const punchInField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchInField) {\n                    const customTitle = punchInField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchInField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchInLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchInLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign In'\n                }\n                // Punch out\n                title = \"PunchOut\";\n                const punchOutConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchOutStatus((punchOutConfig === null || punchOutConfig === void 0 ? void 0 : punchOutConfig.status) || \"On\");\n                const punchOutField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchOutField) {\n                    const customTitle = punchOutField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchOutField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchOutLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchOutLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign Out'\n                }\n                // Work details\n                title = \"WorkDetails\";\n                const workDetailsConfig = crewConfig.find((config)=>config && config.title === title);\n                setWorkDetailsStatus((workDetailsConfig === null || workDetailsConfig === void 0 ? void 0 : workDetailsConfig.status) || \"On\");\n                const workDetailsField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (workDetailsField) {\n                    const customTitle = workDetailsField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(workDetailsField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setWorkDetailsLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setWorkDetailsLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Work Details'\n                }\n            }\n        } else {\n            // Set default values if crewConfig is not valid\n            setPunchInStatus(\"On\");\n            setPunchInLabel(\"Sign In\");\n            setPunchOutStatus(\"On\");\n            setPunchOutLabel(\"Sign Out\");\n            setWorkDetailsStatus(\"On\");\n            setWorkDetailsLabel(\"Work Details\");\n        }\n    };\n    const [queryDuties] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DUTY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewDuties.nodes;\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryDutiesEntry error\", error);\n        }\n    });\n    const handleLogin = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            const currentTime = new Date();\n            setLoginTime(currentTime);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime)\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogin:\", date);\n                return;\n            }\n            setLoginTime(validDate);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n            // If logout time is set and is before the new login time, reset it\n            if (logoutTime && validDate.getTime() > logoutTime.getTime()) {\n                setLogoutTime(null);\n                setCrewManifestEntry((prev)=>({\n                        ...prev,\n                        punchOut: null\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error in handleLogin:\", error);\n            toast({\n                title: \"Error\",\n                description: \"An error occurred while setting the sign in time\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLogout = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            setLogoutTime(null);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: null\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogout:\", date);\n                return;\n            }\n            // If the date doesn't have time set (hours and minutes are 0),\n            // set the current time\n            if (validDate.getHours() === 0 && validDate.getMinutes() === 0) {\n                const now = new Date();\n                validDate.setHours(now.getHours());\n                validDate.setMinutes(now.getMinutes());\n            }\n            // Convert to dayjs for easier comparison\n            const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(validDate);\n            // Ensure logout time is after login time\n            if (loginTime && dayjsDate.isBefore(loginTime)) {\n                toast({\n                    title: \"Invalid time\",\n                    description: \"Sign out time must be after sign in time\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Store the date as a standard Date object to avoid any issues with dayjs\n            setLogoutTime(validDate);\n            // Update crew manifest entry with formatted date string\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n        } catch (error) {\n            console.error(\"Error in handleLogout:\", error);\n            toast({\n                title: \"Error\",\n                description: \"An error occurred while setting the sign out time\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddManifest = ()=>{\n        // Check permissions first\n        if (!edit_logBookEntry) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to edit this log entry\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Filter crew member options to only show available crew members\n        if (allMembers && Array.isArray(allMembers)) {\n            // Get a list of crew members who are already signed in (without sign-out time)\n            const signedInCrewMemberIDs = new Set();\n            if (crew && Array.isArray(crew)) {\n                crew.forEach((member)=>{\n                    // Only consider members who are signed in without a sign-out time\n                    if (member && member.duties && Array.isArray(member.duties)) {\n                        // Check if any duty has no punch out time\n                        const hasActiveShift = member.duties.some((duty)=>duty && duty.punchOut === null);\n                        if (hasActiveShift) {\n                            signedInCrewMemberIDs.add(member.crewMemberID);\n                        }\n                    } else if (member && member.punchOut === null) {\n                        signedInCrewMemberIDs.add(member.crewMemberID);\n                    }\n                });\n            }\n            // Filter out crew members who are already signed in\n            const availableCrewMembers = allMembers.filter((member)=>{\n                if (!member) return false;\n                return !signedInCrewMemberIDs.has(member.value);\n            });\n            // Further filter out crew members who are in the crewMembersList (if applicable)\n            const filteredCrewOptions = availableCrewMembers.filter((member)=>!member || !crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n            setCrewMemberOptions(filteredCrewOptions);\n        } else {\n            // If allMembers is not valid, just proceed with empty options\n            setCrewMemberOptions([]);\n        }\n        // Set up the new crew manifest entry with current time\n        const currentTime = new Date();\n        const crewManifestEntry = {\n            id: 0,\n            logBookEntryID: +logBookEntryID,\n            crewMemberID: 0,\n            dutyPerformedID: 0,\n            punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime),\n            punchOut: null\n        };\n        setCrewManifestEntry(crewManifestEntry);\n        setLoginTime(currentTime);\n        setLogoutTime(null);\n        setCrewMember(null);\n        setDuty(null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleEditManifest = (memberData)=>{\n        if (!edit_logBookEntry) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to edit this log entry\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // If this is a grouped crew member with multiple duties, use the first duty\n        const dutyToEdit = memberData.duties && memberData.duties.length > 0 ? memberData.duties[0] : memberData;\n        setCrewManifestEntry({\n            id: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.id,\n            logBookEntryID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.logBookEntryID,\n            crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n            dutyPerformedID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID,\n            punchIn: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn,\n            punchOut: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut,\n            workDetails: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.workDetails\n        });\n        // Create a proper crew member object with profile details\n        const crewMemberWithProfile = {\n            label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n            value: memberData.crewMember.id,\n            data: memberData.crewMember,\n            profile: {\n                firstName: memberData.crewMember.firstName,\n                surname: memberData.crewMember.surname,\n                avatar: memberData.crewMember.profileImage\n            }\n        };\n        setCrewMember(crewMemberWithProfile);\n        // Find the correct duty in the duties array\n        const selectedDuty = duties.find((memberDuty)=>memberDuty.id === (dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID));\n        if (selectedDuty) {\n            setDuty({\n                label: selectedDuty.title,\n                value: selectedDuty.id\n            });\n        }\n        setLoginTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn) ? new Date(dutyToEdit.punchIn) : new Date());\n        setLogoutTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut) ? new Date(dutyToEdit.punchOut) : null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleSignOutTime = (memberData)=>{\n        if (!edit_logBookEntry) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to edit this log entry\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Determine if this is a nested duty or a main crew member\n        const isNestedDuty = !memberData.crewMember;\n        if (isNestedDuty) {\n            // This is a nested duty\n            // Find the parent crew member for this duty\n            const parentMember = crew && Array.isArray(crew) ? crew.find((c)=>{\n                return c && c.duties && Array.isArray(c.duties) && c.duties.some((d)=>d && d.id === memberData.id);\n            }) : null;\n            if (parentMember) {\n                var _memberData_dutyPerformed;\n                // Set crew manifest entry with the parent crew member ID\n                setCrewManifestEntry({\n                    id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                    logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                    crewMemberID: parentMember.crewMemberID,\n                    dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id,\n                    punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                    punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                    workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n                });\n                // Create a proper crew member object with profile details\n                const crewMemberWithProfile = {\n                    label: \"\".concat(parentMember.crewMember.firstName || \"\", \" \").concat(parentMember.crewMember.surname !== null ? parentMember.crewMember.surname : \"\").trim(),\n                    value: parentMember.crewMember.id,\n                    data: parentMember.crewMember,\n                    profile: {\n                        firstName: parentMember.crewMember.firstName,\n                        surname: parentMember.crewMember.surname,\n                        avatar: parentMember.crewMember.profileImage\n                    }\n                };\n                setCrewMember(crewMemberWithProfile);\n                // Set duty\n                if (memberData.dutyPerformed) {\n                    const selectedDuty = duties.find((memberDuty)=>{\n                        var _memberData_dutyPerformed;\n                        return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                    });\n                    if (selectedDuty) {\n                        setDuty({\n                            label: selectedDuty.title,\n                            value: selectedDuty.id\n                        });\n                    }\n                }\n            } else {\n                // If parent member not found, show an error\n                toast({\n                    title: \"Error\",\n                    description: \"Could not find the associated crew member\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        } else {\n            var _memberData_dutyPerformed1;\n            // This is a main crew member\n            setCrewManifestEntry({\n                id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n                dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed1 = memberData.dutyPerformed) === null || _memberData_dutyPerformed1 === void 0 ? void 0 : _memberData_dutyPerformed1.id,\n                punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n            });\n            // Create a proper crew member object with profile details\n            const crewMemberWithProfile = {\n                label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n                value: memberData.crewMember.id,\n                data: memberData.crewMember,\n                profile: {\n                    firstName: memberData.crewMember.firstName,\n                    surname: memberData.crewMember.surname,\n                    avatar: memberData.crewMember.profileImage\n                }\n            };\n            setCrewMember(crewMemberWithProfile);\n            // Set duty\n            if (memberData.dutyPerformed) {\n                const selectedDuty = duties.find((memberDuty)=>{\n                    var _memberData_dutyPerformed;\n                    return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                });\n                if (selectedDuty) {\n                    setDuty({\n                        label: selectedDuty.title,\n                        value: selectedDuty.id\n                    });\n                }\n            }\n        }\n        // Set times\n        setLoginTime(memberData.punchIn ? new Date(memberData.punchIn) : new Date());\n        setLogoutTime(memberData.punchOut ? new Date(memberData.punchOut) : null);\n        setOpenEditLogoutTimeDialog(true);\n    };\n    const handleCrewMember = async (selected)=>{\n        if (!selected) return;\n        setDuty({\n            label: \"-- Select Duty --\",\n            value: 0\n        });\n        const { data } = await queryCrewDetail({\n            variables: {\n                crewMemberID: selected.value\n            }\n        });\n        const member = data.readOneSeaLogsMember;\n        const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            member\n        ], [\n            vessel\n        ])[0];\n        const value = {\n            ...selected,\n            data: crewWithTraining\n        };\n        setCrewMember(value);\n        // Check if the crew has a training due\n        if (value.data && value.data.trainingStatus && value.data.trainingStatus.label !== \"Good\") {\n            setOpenCrewTrainingDueDialog(true);\n        }\n        // Set default duty\n        if (allCrew && Array.isArray(allCrew)) {\n            const crewMember = allCrew.find((member)=>member && member.id === value.value);\n            if (crewMember && crewMember.primaryDutyID) {\n                if (duties && Array.isArray(duties)) {\n                    const crewDuty = duties.find((d)=>d && d.id === crewMember.primaryDutyID);\n                    if (crewDuty) {\n                        const newDuty = {\n                            label: crewDuty.title,\n                            value: crewDuty.id\n                        };\n                        setDuty(newDuty);\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id,\n                            dutyPerformedID: crewDuty.id\n                        });\n                    } else {\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id\n                        });\n                    }\n                } else {\n                    setCrewManifestEntry({\n                        ...crewManifestEntry,\n                        crewMemberID: crewMember.id\n                    });\n                }\n            } else if (crewMember) {\n                setCrewManifestEntry({\n                    ...crewManifestEntry,\n                    crewMemberID: crewMember.id\n                });\n            }\n        }\n    };\n    const handleDuty = (value)=>{\n        setDuty(value);\n        setCrewManifestEntry({\n            ...crewManifestEntry,\n            dutyPerformedID: (value === null || value === void 0 ? void 0 : value.value) || 0\n        });\n    };\n    const handleCancel = ()=>{\n        setCrewManifestEntry({});\n        setCrewMember(null);\n        setDuty(null);\n        setLoginTime(new Date());\n        setLogoutTime(null);\n        setopenAddCrewMemberDialog(false);\n        setOpenEditLogoutTimeDialog(false);\n    };\n    const handleSave = async (callBy)=>{\n        // Validate required fields\n        if (!crewManifestEntry.crewMemberID || crewManifestEntry.crewMemberID === 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a crew member\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!crewManifestEntry.dutyPerformedID || crewManifestEntry.dutyPerformedID === 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a duty\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Get work details from the textarea\n        const workDetailsElement = document.getElementById(\"work-details\");\n        const workDetails = (workDetailsElement === null || workDetailsElement === void 0 ? void 0 : workDetailsElement.value) || \"\";\n        const variables = {\n            id: crewManifestEntry.id,\n            crewMemberID: crewManifestEntry.crewMemberID,\n            dutyPerformedID: +(crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.dutyPerformedID),\n            logBookEntryID: +logBookEntryID,\n            punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n            punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n            workDetails: workDetails\n        };\n        try {\n            // Case 1: Updating an existing crew entry\n            if (crewManifestEntry.id > 0) {\n                if (offline) {\n                    // Save the updated crew member to the database\n                    await lbCrewModel.save(variables);\n                    // Get all crew IDs to fetch updated data\n                    const crewIds = crew && Array.isArray(crew) ? crew.map((c)=>c.id).filter(Boolean) : [];\n                    // Reset the form\n                    setCrewManifestEntry({});\n                    // Fetch the updated crew data\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedData = groupCrewDutiesByMember(processedData);\n                        // Update state\n                        setCrew(groupedData);\n                        setCrewMembers(groupedData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    updateCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialogs\n                setopenAddCrewMemberDialog(false);\n                if (callBy === \"update\") {\n                    setOpenEditLogoutTimeDialog(false);\n                }\n            } else if (crewManifestEntry.crewMemberID > 0) {\n                if (offline) {\n                    // Generate a unique ID for the new entry\n                    const uniqueId = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n                    const data = {\n                        ...variables,\n                        id: uniqueId\n                    };\n                    // Save the new crew member to the database\n                    await lbCrewModel.save(data);\n                    // Get the selected crew member and duty information\n                    const selectedMember = allVesselCrews.find((c)=>c.id === crewManifestEntry.crewMemberID);\n                    const selectedDuty = allDuties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n                    if (!selectedMember || !selectedDuty) {\n                        toast({\n                            title: \"Error\",\n                            description: \"Could not find crew member or duty information\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    // Create a new crew entry with the necessary data for immediate display\n                    const newCrewEntry = {\n                        ...data,\n                        crewMember: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            selectedMember\n                        ], [\n                            vessel\n                        ])[0],\n                        dutyPerformed: selectedDuty\n                    };\n                    // Get existing crew data or initialize empty array\n                    const existingCrew = Array.isArray(crew) ? [\n                        ...crew\n                    ] : [];\n                    // Check if this crew member already exists in the list\n                    const existingCrewMemberIndex = existingCrew.findIndex((c)=>c && c.crewMemberID === data.crewMemberID);\n                    let updatedCrewData = [\n                        ...existingCrew\n                    ];\n                    if (existingCrewMemberIndex !== -1) {\n                        // If the crew member already exists, add this duty to their duties array\n                        const existingCrewMember = {\n                            ...updatedCrewData[existingCrewMemberIndex]\n                        };\n                        if (existingCrewMember.duties && Array.isArray(existingCrewMember.duties)) {\n                            // Add the new duty to the existing duties array\n                            existingCrewMember.duties.push({\n                                id: data.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: data.punchIn,\n                                punchOut: data.punchOut,\n                                workDetails: data.workDetails,\n                                dutyPerformedID: data.dutyPerformedID,\n                                logBookEntryID: data.logBookEntryID\n                            });\n                        } else {\n                            // Create a duties array if it doesn't exist\n                            existingCrewMember.duties = [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ];\n                        }\n                        // Update the crew member in the array\n                        updatedCrewData[existingCrewMemberIndex] = existingCrewMember;\n                    } else {\n                        // If this is a new crew member, add them to the list with their first duty\n                        updatedCrewData.push({\n                            ...newCrewEntry,\n                            duties: [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ]\n                        });\n                    }\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                    setCrewManifestEntry({});\n                    // Also fetch the latest data from the database to ensure consistency\n                    const crewIds = updatedCrewData.map((c)=>c && c.id).filter(Boolean).concat([\n                        uniqueId\n                    ]);\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedDbData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedDbData = groupCrewDutiesByMember(processedDbData);\n                        // Update with the database data to ensure consistency\n                        setCrew(groupedDbData);\n                        setCrewMembers(groupedDbData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    createCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialog\n                setopenAddCrewMemberDialog(false);\n            } else {\n                // No valid crew member selected, just cancel\n                handleCancel();\n            }\n        } catch (error) {\n            console.error(\"Error saving crew member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save crew member. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const [updateCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateCrewMembers_LogBookEntrySection, {\n        onCompleted: ()=>{\n            // First, update the UI immediately with the updated data\n            if (crewManifestEntry.id > 0 && crew && Array.isArray(crew)) {\n                // Create a deep copy of the current crew data\n                let updatedCrewData = JSON.parse(JSON.stringify(crew));\n                // Find the crew member and duty that was updated\n                let foundAndUpdated = false;\n                // Loop through all crew members\n                for(let i = 0; i < updatedCrewData.length; i++){\n                    const member = updatedCrewData[i];\n                    // Check if this is the main duty that was updated\n                    if (member.id === crewManifestEntry.id) {\n                        // Update the main duty\n                        updatedCrewData[i] = {\n                            ...member,\n                            punchOut: crewManifestEntry.punchOut\n                        };\n                        foundAndUpdated = true;\n                        break;\n                    }\n                    // Check if this is a nested duty that was updated\n                    if (member.duties && Array.isArray(member.duties)) {\n                        for(let j = 0; j < member.duties.length; j++){\n                            const duty = member.duties[j];\n                            if (duty.id === crewManifestEntry.id) {\n                                // Update the nested duty\n                                member.duties[j] = {\n                                    ...duty,\n                                    punchOut: crewManifestEntry.punchOut\n                                };\n                                foundAndUpdated = true;\n                                break;\n                            }\n                        }\n                        if (foundAndUpdated) {\n                            break;\n                        }\n                    }\n                }\n                if (foundAndUpdated) {\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            searchFilter.archived = {\n                eq: false\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"updateCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [createCrewWelfareCheck, { loading: createCrewWelfareCheckLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewWelfare_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createCrewWelfare_LogBookEntrySection;\n            updateCrewWelfare(data);\n        },\n        onError: (error)=>{\n            console.error(\"createCrewWelfareCheck\", error);\n        }\n    });\n    const [createCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewMembers_LogBookEntrySection, {\n        onCompleted: (data)=>{\n            var _allMembers_find;\n            // First, update the UI immediately with the new crew member\n            // Get the selected crew member and duty information\n            const selectedMember = (_allMembers_find = allMembers.find((m)=>m.value === crewManifestEntry.crewMemberID)) === null || _allMembers_find === void 0 ? void 0 : _allMembers_find.data;\n            const selectedDuty = duties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n            if (selectedMember && selectedDuty) {\n                var _document_getElementById;\n                // Create a new crew entry with the necessary data for immediate display\n                const newCrewEntry = {\n                    id: data.createCrewMembers_LogBookEntrySection.id,\n                    crewMemberID: crewManifestEntry.crewMemberID,\n                    dutyPerformedID: crewManifestEntry.dutyPerformedID,\n                    logBookEntryID: +logBookEntryID,\n                    punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n                    punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n                    workDetails: ((_document_getElementById = document.getElementById(\"work-details\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value) || \"\",\n                    crewMember: selectedMember,\n                    dutyPerformed: selectedDuty\n                };\n                // Create a new array with existing crew data plus the new entry\n                let updatedCrewData = crew ? [\n                    ...crew\n                ] : [];\n                // Check if this crew member already exists in the list\n                const existingCrewMemberIndex = updatedCrewData.findIndex((c)=>c.crewMemberID === crewManifestEntry.crewMemberID);\n                if (existingCrewMemberIndex >= 0) {\n                    // If the crew member already exists, add this duty to their duties array\n                    const existingCrewMember = updatedCrewData[existingCrewMemberIndex];\n                    const updatedDuties = existingCrewMember.duties ? [\n                        ...existingCrewMember.duties\n                    ] : [];\n                    updatedDuties.push({\n                        id: data.createCrewMembers_LogBookEntrySection.id,\n                        dutyPerformed: selectedDuty,\n                        punchIn: newCrewEntry.punchIn,\n                        punchOut: newCrewEntry.punchOut,\n                        workDetails: newCrewEntry.workDetails,\n                        dutyPerformedID: newCrewEntry.dutyPerformedID,\n                        logBookEntryID: newCrewEntry.logBookEntryID\n                    });\n                    // Update the crew member with the new duties array\n                    updatedCrewData[existingCrewMemberIndex] = {\n                        ...existingCrewMember,\n                        duties: updatedDuties\n                    };\n                } else {\n                    // If this is a new crew member, add them to the list with their first duty\n                    updatedCrewData.push({\n                        ...newCrewEntry,\n                        duties: [\n                            {\n                                id: data.createCrewMembers_LogBookEntrySection.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: newCrewEntry.punchIn,\n                                punchOut: newCrewEntry.punchOut,\n                                workDetails: newCrewEntry.workDetails,\n                                dutyPerformedID: newCrewEntry.dutyPerformedID,\n                                logBookEntryID: newCrewEntry.logBookEntryID\n                            }\n                        ]\n                    });\n                }\n                // Group the updated crew data\n                const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                // Update state with the new grouped data\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = crew ? [\n                ...crew === null || crew === void 0 ? void 0 : crew.map((c)=>c.id),\n                data.createCrewMembers_LogBookEntrySection.id\n            ] : [\n                data.createCrewMembers_LogBookEntrySection.id\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"createCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewMembers_LogBookEntrySections.nodes;\n            if (data) {\n                // Process crew members with training status\n                const processedData = data.map((section)=>{\n                    if (section.crewMember) {\n                        const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            section.crewMember\n                        ], [\n                            vessel\n                        ])[0];\n                        return {\n                            ...section,\n                            crewMember: crewMemberWithTrainingStatus\n                        };\n                    }\n                    return section;\n                });\n                // Preserve existing duties if they exist\n                let updatedData = processedData;\n                if (crew && crew.length > 0) {\n                    // Create a map of existing crew members with their duties\n                    const existingCrewMap = crew.reduce((map, member)=>{\n                        if (member.crewMemberID) {\n                            map[member.crewMemberID] = member;\n                        }\n                        return map;\n                    }, {});\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedData.map((section)=>{\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && existingMember.duties.length > 0) {\n                            // Find the matching duty in the existing duties array\n                            const existingDutyIndex = existingMember.duties.findIndex((duty)=>duty.id === section.id);\n                            if (existingDutyIndex >= 0) {\n                                // This section is already in the duties, update it with the latest data\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties[existingDutyIndex] = {\n                                    ...updatedDuties[existingDutyIndex],\n                                    // Update with the latest data from the server\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails\n                                };\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n                // Group crew duties by crew member\n                const groupedData = groupCrewDutiesByMember(updatedData);\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n                const members = allMembers.filter((member)=>{\n                    if (!data) {\n                        return true;\n                    }\n                    return !data.some((section)=>section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members.filter((member)=>!crewMembersList || !crewMembersList.includes(+member.value)));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getSectionCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const handleArchive = async ()=>{\n        setOpenConfirmCrewDeleteDialog(false);\n        if (!crewManifestEntry.id) {\n            toast({\n                title: \"Error\",\n                description: \"No crew member selected to delete\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            try {\n                // First try to delete the record\n                const result = await lbCrewModel.delete({\n                    id: crewManifestEntry.id\n                });\n                if (!result) {\n                    // If delete fails, mark as archived\n                    await lbCrewModel.save({\n                        id: crewManifestEntry.id,\n                        archived: true\n                    });\n                }\n                const appendData = [\n                    ...crew.map((c)=>c.id)\n                ];\n                setCrewManifestEntry({});\n                const data = await lbCrewModel.getByIds(appendData);\n                if (data) {\n                    // Process crew members with training status\n                    const processedData = data.map((section)=>{\n                        if (section.crewMember) {\n                            const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                section.crewMember\n                            ], [\n                                vessel\n                            ])[0];\n                            return {\n                                ...section,\n                                crewMember: crewMemberWithTrainingStatus\n                            };\n                        }\n                        return section;\n                    });\n                    // Group crew duties by crew member\n                    const groupedData = groupCrewDutiesByMember(processedData);\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to delete crew member\",\n                    variant: \"destructive\"\n                });\n            }\n        } else {\n            try {\n                // Use the delete mutation instead of update\n                await deleteCrewMembersLogBookEntrySections({\n                    variables: {\n                        ids: [\n                            crewManifestEntry.id\n                        ]\n                    }\n                });\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to delete crew member\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n        setopenAddCrewMemberDialog(false);\n    };\n    // Function removed as we're directly using setOpenConfirmCrewDeleteDialog\n    const [deleteCrewMembersLogBookEntrySections] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.DeleteCrewMembers_LogBookEntrySections, {\n        onCompleted: ()=>{\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n            setOpenConfirmCrewDeleteDialog(false);\n            setopenAddCrewMemberDialog(false);\n        },\n        onError: (error)=>{\n            console.error(\"deleteCrewMembersLogBookEntrySections error:\", error);\n        }\n    });\n    // Function removed as we're using handleArchive instead\n    const crewCount = ()=>{\n        if (!crew || !Array.isArray(crew)) return 0;\n        const count = crew.filter((member)=>member && member.crewMemberID > 0 && member.punchOut === null).length;\n        return count;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewConfig) {\n            handleSetStatus();\n        }\n    }, [\n        crewConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default()(logBookConfig)) {\n            handleSetCrewConfig();\n        }\n    }, [\n        logBookConfig\n    ]);\n    var _vessel_maxPOB, _vessel_maxPOB1;\n    // Removed unused overdueTextWarning variable\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-8 gap-36 lg:gap-6 xl:gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-5 space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H2, {\n                                children: \"Crew\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2066,\n                                columnNumber: 21\n                            }, this),\n                            crew ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"pl-2.5 text-left align-bottom standard:align-top\",\n                                                            children: \"Crew\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2072,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"px-[5px] text-left align-bottom standard:align-top\",\n                                                            children: \"Duty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2075,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"px-[5px] text-right\",\n                                                                    children: punchInLabel || \"Sign In\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2082,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"pl-[5px] pr-2.5 text-right\",\n                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2089,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"text-wrap standard:text-nowrap pr-0 text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    punchInStatus !== \"Off\" ? punchInLabel || \"Sign In\" : \"\",\n                                                                    \"/\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                        className: \"standard:hidden\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2103,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    punchOutStatus !== \"Off\" ? punchOutLabel || \"Sign Out\" : \"\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2096,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2071,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2070,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                                                children: crew.filter((member)=>+member.crewMemberID > 0 && member.archived === false).map((member)=>{\n                                                    var _member_crewMember_trainingStatus;\n                                                    // Check if member has multiple duties\n                                                    const hasMultipleDuties = member.duties && Array.isArray(member.duties) && member.duties.length > 1;\n                                                    // Get additional duties (if any)\n                                                    const additionalDuties = hasMultipleDuties ? member.duties.slice(1).filter((duty)=>{\n                                                        // Get the first duty's title\n                                                        const firstDutyTitle = member.duties[0].dutyPerformed && member.duties[0].dutyPerformed.title;\n                                                        // Get current duty's title\n                                                        const currentDutyTitle = duty.dutyPerformed && duty.dutyPerformed.title;\n                                                        // Only include duties with different titles\n                                                        return currentDutyTitle && firstDutyTitle !== currentDutyTitle;\n                                                    }) : [];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                \"aria-disabled\": locked,\n                                                                className: \"group \".concat(hasMultipleDuties ? \"border-b-0\" : \"\"),\n                                                                onClick: (e)=>{\n                                                                    // Don't do anything if locked\n                                                                    if (locked) return;\n                                                                    // Prevent row click if the event originated from a button\n                                                                    if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                        return;\n                                                                    }\n                                                                    handleEditManifest(member);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left\", additionalDuties.length > 0 && \" text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                                                                    size: \"sm\",\n                                                                                    variant: ((_member_crewMember_trainingStatus = member.crewMember.trainingStatus) === null || _member_crewMember_trainingStatus === void 0 ? void 0 : _member_crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                                                                        children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(member.crewMember.firstName, member.crewMember.surname)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2210,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2199,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"hidden leading-none sm:flex flex-col justify-center ml-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"flex gap-2.5 items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-foreground\",\n                                                                                                children: [\n                                                                                                    member.crewMember.firstName,\n                                                                                                    \" \",\n                                                                                                    member.crewMember.surname\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2223,\n                                                                                                columnNumber: 73\n                                                                                            }, this),\n                                                                                            member.workDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.Popover, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverTrigger, {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                        },\n                                                                                                        className: \"p-0 text-muted-foreground\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                                            className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2244,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2237,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverContent, {\n                                                                                                        className: \"w-80\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: member.workDetails\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2252,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2251,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2236,\n                                                                                                columnNumber: 77\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2222,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2221,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2198,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2191,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left grid items-center\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"truncate\",\n                                                                            children: member.dutyPerformed && member.dutyPerformed.title ? member.dutyPerformed.title : \"Not assigned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2272,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2265,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                                children: punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2286,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right phablet:pr-2.5 relaive\", additionalDuties.length > 0 && \"text-input\"),\n                                                                                children: punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2312,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2332,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2302,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right relaive flex flex-col phablet:pr-2.5 justify-center items-end\", additionalDuties.length > 0 && \"text-input\"),\n                                                                            children: [\n                                                                                punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\"),\n                                                                                punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2363,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2383,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2345,\n                                                                            columnNumber: 65\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2344,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                ]\n                                                            }, member.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                lineNumber: 2166,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            additionalDuties.map((duty, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                    \"aria-disabled\": locked,\n                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"group\", index === additionalDuties.length - 1 ? \"\" : \"border-b-0\"),\n                                                                    onClick: (e)=>{\n                                                                        // Don't do anything if locked\n                                                                        if (locked) return;\n                                                                        // Prevent row click if the event originated from a button\n                                                                        if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                            return;\n                                                                        }\n                                                                        handleEditManifest(member);\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input py-2 text-left relative\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col absolute -top-[42%] items-center w-8 h-full\",\n                                                                                children: [\n                                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-full h-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2447,\n                                                                                        columnNumber: 77\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-[1px] flex-1 border-l border-dashed border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2449,\n                                                                                        columnNumber: 73\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"size-[5px] rounded-full bg-background border border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2450,\n                                                                                        columnNumber: 73\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2444,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2440,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input grid items-center text-left\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"truncate\",\n                                                                                children: duty.dutyPerformed && duty.dutyPerformed.title ? duty.dutyPerformed.title : \"Not assigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2458,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2454,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input py-2 text-right\"),\n                                                                                    children: punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2472,\n                                                                                    columnNumber: 73\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input phablet:pr-2.5 py-2 text-right\"),\n                                                                                    children: punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2491,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2511,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2484,\n                                                                                    columnNumber: 73\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right phablet:pr-2.5 relaive flex flex-col justify-center items-end\",\n                                                                                children: [\n                                                                                    punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\"),\n                                                                                    punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2535,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2555,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2524,\n                                                                                columnNumber: 73\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2523,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, \"duty-\".concat(duty.id, \"-\").concat(index), true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2403,\n                                                                    columnNumber: 61\n                                                                }, this))\n                                                        ]\n                                                    }, \"crew-\".concat(member.id), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2163,\n                                                        columnNumber: 49\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2113,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2069,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                        className: \"mb-0 font-semibold\",\n                                                        children: \"Minimum crew:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2578,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_30__.Badge, {\n                                                        variant: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB !== void 0 ? _vessel_maxPOB : 0) ? \"destructive\" : \"success\",\n                                                        className: \"rounded-full flex items-center justify-center size-[25px]\",\n                                                        children: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) ? vessel.minCrew : 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2581,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB1 = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB1 !== void 0 ? _vessel_maxPOB1 : 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"You have more people on board than your vessel is configured to carry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2593,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2577,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                className: \"w-full tiny:w-fit px-2.5\",\n                                                disabled: locked,\n                                                onClick: handleAddManifest,\n                                                children: \"Add crew\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2601,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2576,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2068,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                    disabled: locked,\n                                    onClick: handleAddManifest,\n                                    children: \"Add crew members to this trip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2611,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2610,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2065,\n                        columnNumber: 17\n                    }, this),\n                    crew && logBookConfig && (logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.find((config)=>config.title === \"Crew Welfare\" && config.active === true)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-3 space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_logBookEntry,\n                            crewWelfareCheck: crewWelfareCheck,\n                            updateCrewWelfare: updateCrewWelfare\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2628,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2627,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2064,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openAddCrewMemberDialog,\n                setOpenDialog: setopenAddCrewMemberDialog,\n                handleCreate: handleSave,\n                handleCancel: handleCancel,\n                handleDestructiveAction: crewManifestEntry.id > 0 ? ()=>setOpenConfirmCrewDeleteDialog(true) : undefined,\n                showDestructiveAction: crewManifestEntry.id > 0,\n                destructiveActionText: \"Delete\",\n                title: crewManifestEntry.id > 0 ? \"Update crew member\" : \"Add crew member\",\n                actionText: crewManifestEntry.id > 0 ? \"Update\" : \"Add\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-top gap-4 mb-4\",\n                            children: [\n                                crewMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                    variant: ((_crewMember_data = crewMember.data) === null || _crewMember_data === void 0 ? void 0 : (_crewMember_data_trainingStatus = _crewMember_data.trainingStatus) === null || _crewMember_data_trainingStatus === void 0 ? void 0 : _crewMember_data_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    className: \"size-12 border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarImage, {\n                                            src: (_crewMember_profile = crewMember.profile) === null || _crewMember_profile === void 0 ? void 0 : _crewMember_profile.avatar,\n                                            alt: \"\".concat(((_crewMember_profile1 = crewMember.profile) === null || _crewMember_profile1 === void 0 ? void 0 : _crewMember_profile1.firstName) || \"\", \" \").concat(((_crewMember_profile2 = crewMember.profile) === null || _crewMember_profile2 === void 0 ? void 0 : _crewMember_profile2.surname) || \"\").trim()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2670,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                            children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(((_crewMember_profile3 = crewMember.profile) === null || _crewMember_profile3 === void 0 ? void 0 : _crewMember_profile3.firstName) || ((_crewMember_data1 = crewMember.data) === null || _crewMember_data1 === void 0 ? void 0 : _crewMember_data1.firstName), ((_crewMember_profile4 = crewMember.profile) === null || _crewMember_profile4 === void 0 ? void 0 : _crewMember_profile4.surname) || ((_crewMember_data2 = crewMember.data) === null || _crewMember_data2 === void 0 ? void 0 : _crewMember_data2.surname))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2674,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2662,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H3, {\n                                            className: \"text-lg\",\n                                            children: (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile5 = crewMember.profile) === null || _crewMember_profile5 === void 0 ? void 0 : _crewMember_profile5.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data3 = crewMember.data) === null || _crewMember_data3 === void 0 ? void 0 : _crewMember_data3.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile6 = crewMember.profile) === null || _crewMember_profile6 === void 0 ? void 0 : _crewMember_profile6.surname) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data4 = crewMember.data) === null || _crewMember_data4 === void 0 ? void 0 : _crewMember_data4.surname) ? \"\".concat(((_crewMember_profile7 = crewMember.profile) === null || _crewMember_profile7 === void 0 ? void 0 : _crewMember_profile7.firstName) || ((_crewMember_data5 = crewMember.data) === null || _crewMember_data5 === void 0 ? void 0 : _crewMember_data5.firstName) || \"\", \" \").concat(((_crewMember_profile8 = crewMember.profile) === null || _crewMember_profile8 === void 0 ? void 0 : _crewMember_profile8.surname) || ((_crewMember_data6 = crewMember.data) === null || _crewMember_data6 === void 0 ? void 0 : _crewMember_data6.surname) || \"\").trim() : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2685,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMember && ((crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data7 = crewMember.data) === null || _crewMember_data7 === void 0 ? void 0 : (_crewMember_data_trainingStatus1 = _crewMember_data7.trainingStatus) === null || _crewMember_data_trainingStatus1 === void 0 ? void 0 : _crewMember_data_trainingStatus1.label) !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: \"Training is overdue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2697,\n                                                    columnNumber: 41\n                                                }, this),\n                                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data8 = crewMember.data) === null || _crewMember_data8 === void 0 ? void 0 : (_crewMember_data_trainingStatus2 = _crewMember_data8.trainingStatus) === null || _crewMember_data_trainingStatus2 === void 0 ? void 0 : _crewMember_data_trainingStatus2.dues) && crewMember.data.trainingStatus.dues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: crewMember.data.trainingStatus.dues.map((due, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-destructive text-lg\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2713,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        due.trainingType.title,\n                                                                        \" \",\n                                                                        \"-\",\n                                                                        \" \",\n                                                                        due.status.label\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2716,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2710,\n                                                            columnNumber: 61\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2704,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2696,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-bright-turquoise-600\",\n                                            children: \"Training up to date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2736,\n                                            columnNumber: 37\n                                        }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2684,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2660,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-[31px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__.Combobox, {\n                                    label: \"Crew member\",\n                                    modal: true,\n                                    buttonClassName: \"w-full\",\n                                    options: crewMemberOptions.map((option)=>({\n                                            ...option,\n                                            value: String(option.value)\n                                        })),\n                                    value: crewMember,\n                                    onChange: handleCrewMember\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2743,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    label: \"Primary duty\",\n                                    crewDutyID: Number(duty === null || duty === void 0 ? void 0 : duty.value) || 0,\n                                    onChange: handleDuty,\n                                    multi: false,\n                                    modal: true,\n                                    offline: offline,\n                                    hideCreateOption: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2756,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2742,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xs:grid-cols-2 pr-px gap-[31px]\",\n                            children: [\n                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signin-date\",\n                                    modal: true,\n                                    value: loginTime,\n                                    onChange: handleLogin,\n                                    label: punchInLabel || \"Sign In\",\n                                    dateFormat: \"dd MMM,\",\n                                    placeholder: \"\".concat(punchInLabel || \"Sign In\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\",\n                                    closeOnSelect: false,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2769,\n                                    columnNumber: 29\n                                }, this),\n                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signout-date\",\n                                    modal: true,\n                                    value: logoutTime || undefined,\n                                    onChange: handleLogout,\n                                    label: punchOutLabel || \"Sign Out\",\n                                    placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\" // Keep datetime to include time picker\n                                    ,\n                                    dateFormat: \"dd MMM,\",\n                                    timeFormat: \"HH:mm\" // Explicitly set time format\n                                    ,\n                                    closeOnSelect: false,\n                                    clearable: true,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2786,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2767,\n                            columnNumber: 21\n                        }, this),\n                        workDetailsStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                            htmlFor: \"work-details\",\n                            label: workDetailsLabel || \"Work Details\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__.Textarea, {\n                                id: \"work-details\",\n                                rows: 4,\n                                className: \"w-full resize-none\",\n                                placeholder: \"Enter work details\",\n                                defaultValue: crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.workDetails\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2809,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2806,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2659,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2639,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openEditLogoutTimeDialog,\n                setOpenDialog: setOpenEditLogoutTimeDialog,\n                handleCreate: ()=>handleSave(\"update\"),\n                handleCancel: handleCancel,\n                actionText: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(\"Update\", \"Update Time\"),\n                cancelText: \"Cancel\",\n                contentClassName: \"top-[38svh]\",\n                size: \"sm\",\n                title: \"Update sign out time\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        modal: true,\n                        id: \"signout-date\",\n                        name: \"signout-date\",\n                        label: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        value: logoutTime || undefined,\n                        mode: \"single\",\n                        type: \"datetime\" // Keep datetime to include time picker\n                        ,\n                        onChange: handleLogout,\n                        dateFormat: \"dd MMM,\",\n                        timeFormat: \"HH:mm\" // Explicitly set time format\n                        ,\n                        placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        closeOnSelect: false,\n                        clearable: true,\n                        icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2833,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2832,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2821,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openCrewTrainingDueDialog,\n                setOpenDialog: setOpenCrewTrainingDueDialog,\n                contentClassName: \"max-w-xl\",\n                className: \"space-y-4\",\n                cancelText: \"Cancel\",\n                actionText: \"Yes, Continue\",\n                handleCreate: ()=>setOpenCrewTrainingDueDialog(false),\n                handleCancel: ()=>{\n                    setOpenCrewTrainingDueDialog(false);\n                    setCrewMember(null);\n                    setDuty(null);\n                },\n                title: \"Crew member training status\",\n                variant: \"warning\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data9 = crewMember.data) === null || _crewMember_data9 === void 0 ? void 0 : _crewMember_data9.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data10 = crewMember.data) === null || _crewMember_data10 === void 0 ? void 0 : _crewMember_data10.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"This crew member\",\n                                \" \",\n                                \"has overdue training sessions on this vessel. These sessions are:\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2870,\n                            columnNumber: 21\n                        }, this),\n                        crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data11 = crewMember.data) === null || _crewMember_data11 === void 0 ? void 0 : (_crewMember_data_trainingStatus3 = _crewMember_data11.trainingStatus) === null || _crewMember_data_trainingStatus3 === void 0 ? void 0 : (_crewMember_data_trainingStatus_dues = _crewMember_data_trainingStatus3.dues) === null || _crewMember_data_trainingStatus_dues === void 0 ? void 0 : _crewMember_data_trainingStatus_dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2882,\n                                    columnNumber: 33\n                                }, this)\n                            }, dueIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2881,\n                                columnNumber: 29\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Do you still want to add this crew member to this vessel?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2889,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2869,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2853,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openConfirmCrewDeleteDialog,\n                setOpenDialog: setOpenConfirmCrewDeleteDialog,\n                handleCreate: handleArchive,\n                handleCancel: ()=>{\n                    setOpenConfirmCrewDeleteDialog(false);\n                // Don't reset crew member here as it's needed for the parent dialog\n                },\n                actionText: \"Remove\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-md\",\n                variant: \"warning\",\n                showIcon: true,\n                title: \"Remove crew member\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Are you sure you want to remove\",\n                        \" \",\n                        (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data12 = crewMember.data) === null || _crewMember_data12 === void 0 ? void 0 : _crewMember_data12.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data13 = crewMember.data) === null || _crewMember_data13 === void 0 ? void 0 : _crewMember_data13.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"this crew member\",\n                        \" \",\n                        \"from this trip manifest?\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2910,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2896,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Crew, \"X05O/yHUdJ7N5zSu93hbrsNcN9s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation\n    ];\n});\n_c = Crew;\nvar _c;\n$RefreshReg$(_c, \"Crew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy9jcmV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDUTtBQU12QjtBQVFMO0FBQ0w7QUFNTztBQUdzQjtBQUNMO0FBQ2dCO0FBRXJCO0FBQ2U7QUFDUTtBQUNWO0FBQzhDO0FBQ3JDO0FBQ3FDO0FBQy9CO0FBQ3ZCO0FBQ29CO0FBQ3JDO0FBQ2E7QUFDTTtBQUNKO0FBQ0k7QUFDNkI7QUFDbEM7QUFLZDtBQUNlO0FBQ2tCO0FBQ2Q7QUFNcEI7QUFDYztBQUNUO0FBRXBDLGtDQUFrQztBQVFsQjtBQUMyQjtBQUV1QjtBQUNUO0FBRWI7QUFHRjtBQUUzQixTQUFTK0QsS0FBSyxLQWNqQjtRQWRpQixFQUN6QkMsZUFBZSxLQUFLLEVBQ3BCQyxPQUFPLEVBQ1BDLGNBQWMsRUFDZEMsTUFBTSxFQUNOQyxnQkFBZ0IsS0FBSyxFQUNyQkMsY0FBYyxFQUNkQyxnQkFBZ0IsRUFDaEJDLGlCQUFpQixFQUNqQkMsU0FBUyxLQUFLLEVBQ2RDLFdBQVcsQ0FBQyxFQUNaQyxnQkFBZ0IsRUFDaEJDLFVBQVUsS0FBSyxFQUNmQyxlQUFlLEVBQ1AsR0FkaUI7UUEwK0VUUixrREFBQUEsNENBMENnQlMsaUNBQUFBLGtCQU9LQSxxQkFDR0Esc0JBQXVDQSxzQkFJM0NBLHNCQUNJQSxtQkFDSkEsc0JBQ0lBLG1CQU9YQSxzQkFDREEsbUJBQ0FBLHNCQUNBQSxtQkFDU0Esc0JBQWlDQSxtQkFBb0NBLHNCQUErQkEsbUJBSTVHQSxrQ0FBQUEsbUJBTVFBLGtDQUFBQSxtQkEyS2hCQSxtQkFDREEsb0JBT0hBLHNDQUFBQSxrQ0FBQUEsb0JBaUNBQSxvQkFBK0JBOztJQTd2RmhELE1BQU1DLHFCQUFxQixJQUFJbkQsMEVBQWtCQTtJQUNqRCxNQUFNb0QsZ0JBQWdCLElBQUluRCxxRUFBYUE7SUFDdkMsTUFBTW9ELGNBQWMsSUFBSW5ELDRGQUFvQ0E7SUFDNUQsTUFBTW9ELGlCQUFpQixJQUFJbEQsNEZBQW9DQTtJQUMvRCxNQUFNLENBQUNtRCxnQkFBZ0JDLGtCQUFrQixHQUFHakYsK0NBQVFBLENBQWUsRUFBRTtJQUNyRSxNQUFNLENBQUNrRixXQUFXQyxhQUFhLEdBQUduRiwrQ0FBUUEsQ0FBYSxFQUFFO0lBQ3pELE1BQU1vRixlQUFlL0QsZ0VBQWVBO1FBQ25CK0Q7SUFBakIsTUFBTUMsV0FBV0QsQ0FBQUEsb0JBQUFBLGFBQWFFLEdBQUcsQ0FBQyx5QkFBakJGLCtCQUFBQSxvQkFBZ0M7SUFDakQsTUFBTSxDQUFDRyxXQUFXQyxhQUFhLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN5RixRQUFRQyxVQUFVLEdBQUcxRiwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUMyRSxZQUFZZ0IsY0FBYyxHQUFHM0YsK0NBQVFBLENBQW9CO0lBQ2hFLE1BQU0sQ0FBQzRGLE1BQU1DLFFBQVEsR0FBRzdGLCtDQUFRQSxDQUd0QjtJQUNWLE1BQU0sQ0FBQzhGLFdBQVdDLGFBQWEsR0FBRy9GLCtDQUFRQSxDQUFDLElBQUlnRztJQUMvQyxxREFBcUQ7SUFDckQsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdsRywrQ0FBUUEsQ0FBYztJQUMxRCxNQUFNLENBQUNtRyxRQUFRQyxVQUFVLEdBQUdwRywrQ0FBUUEsQ0FBYSxFQUFFO0lBQ25ELE1BQU0sQ0FBQ3FHLE1BQU1DLFFBQVEsR0FBR3RHLCtDQUFRQSxDQUFNOEQ7SUFDdEMsTUFBTSxDQUFDeUMsWUFBWUMsY0FBYyxHQUFHeEcsK0NBQVFBLENBQU07SUFDbEQsMEJBQTBCO0lBQzFCLE1BQU0sQ0FBQ3lHLGVBQWVDLGlCQUFpQixHQUFHMUcsK0NBQVFBO0lBQ2xELE1BQU0sQ0FBQzJHLGNBQWNDLGdCQUFnQixHQUFHNUcsK0NBQVFBLENBQVM7SUFDekQsTUFBTSxDQUFDNkcsZ0JBQWdCQyxrQkFBa0IsR0FBRzlHLCtDQUFRQTtJQUNwRCxNQUFNLENBQUMrRyxlQUFlQyxpQkFBaUIsR0FBR2hILCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQ2lILG1CQUFtQkMscUJBQXFCLEdBQUdsSCwrQ0FBUUE7SUFDMUQsTUFBTSxDQUFDbUgsa0JBQWtCQyxvQkFBb0IsR0FDekNwSCwrQ0FBUUEsQ0FBUztJQUNyQixtREFBbUQ7SUFDbkQsOERBQThEO0lBQzlELE1BQU0sQ0FBQ3FILG1CQUFtQkMscUJBQXFCLEdBQUd0SCwrQ0FBUUEsQ0FBTSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ3VILHlCQUF5QkMsMkJBQTJCLEdBQ3ZEeEgsK0NBQVFBLENBQUM7SUFFYixNQUFNLEVBQUV5SCxLQUFLLEVBQUUsR0FBR2xHLDBEQUFRQTtJQUUxQiwwREFBMEQ7SUFDMUQsTUFBTSxDQUFDbUcsMEJBQTBCQyw0QkFBNEIsR0FDekQzSCwrQ0FBUUEsQ0FBQztJQUNiLE1BQU0sQ0FBQzRILG1CQUFtQkMscUJBQXFCLEdBQUc3SCwrQ0FBUUEsQ0FBTSxFQUFFO0lBQ2xFLE1BQU0sQ0FBQzhILDJCQUEyQkMsNkJBQTZCLEdBQzNEL0gsK0NBQVFBLENBQUM7SUFDYixNQUFNLENBQUNnSSw2QkFBNkJDLCtCQUErQixHQUMvRGpJLCtDQUFRQSxDQUFDO0lBQ2IsTUFBTSxDQUFDa0ksYUFBYUMsZUFBZSxHQUFHbkksK0NBQVFBLENBQU07SUFDcEQsTUFBTSxDQUFDb0ksbUJBQW1CQyxxQkFBcUIsR0FBR3JJLCtDQUFRQSxDQUFNO0lBQ2hFLE1BQU0sQ0FBQ3NJLFlBQVlDLGNBQWMsR0FBR3ZJLCtDQUFRQSxDQUFNLEVBQUU7SUFFcEQsTUFBTXdJLEtBQUsvRSxpRkFBY0E7SUFFekIsTUFBTWdGLG1CQUFtQjtRQUNyQixJQUNJUCxlQUNBbkcsdUVBQWFBLENBQ1QyRyxPQUFPQSxDQUFDQyxHQUFHLENBQUNDLGlCQUFpQixJQUFJLHFCQUNqQ1YsY0FFTjtZQUNFRyxxQkFBcUI7UUFDekIsT0FBTztZQUNIQSxxQkFBcUI7UUFDekI7SUFDSjtJQUVBLE1BQU1RLGdDQUFnQztRQUNsQyxpR0FBaUc7UUFDakcsTUFBTUMsUUFBUSxDQUFDQyxLQUNYLElBQUlDLFFBQVEsQ0FBQ0MsVUFBWUMsV0FBV0QsU0FBU0Y7UUFDakQsTUFBTUQsTUFBTTtRQUNaLE1BQU1LLEtBQUt2SCxpRkFBZ0JBO1FBQzNCLE1BQU13SCxPQUFPLE1BQU1yRSxlQUFlc0UsSUFBSSxDQUFDO1lBQ25DRixJQUFJQTtZQUNKbkYsZ0JBQWdCQTtZQUNoQnNGLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxlQUFlO1lBQ2ZDLGNBQWM7WUFDZEMsWUFBWTtRQUNoQjtRQUNBckYsa0JBQWtCK0U7SUFDdEI7SUFFQXJKLGdEQUFTQSxDQUFDO1FBQ05vSSxlQUFlckcsb0VBQWNBO1FBQzdCMkc7SUFDSixHQUFHLEVBQUU7SUFFTDFJLGdEQUFTQSxDQUFDO1FBQ04wSTtJQUNKLEdBQUc7UUFBQ1A7S0FBWTtJQUVoQm5JLGdEQUFTQSxDQUFDO1FBQ04sSUFBSXlFLG9CQUFvQm1GLE1BQU1DLE9BQU8sQ0FBQ3BGLG1CQUFtQjtZQUNyRCxNQUFNcUYsaUJBQWlCckYsaUJBQWlCc0YsTUFBTSxDQUMxQyxDQUFDQyxVQUNHQSxXQUNBQSxRQUFRQyxTQUFTLEtBQ2IsNENBQ1ZDLE1BQU07WUFDUixJQUNJSixtQkFBbUIsS0FDbkIsQ0FBQ3pGLG9CQUNELENBQUNxQixVQUNELENBQUN5RSwrQkFDSDtnQkFDRXhFLFVBQVU7Z0JBQ1YsSUFBSWpCLFNBQVM7b0JBQ1RvRTtnQkFDSixPQUFPO29CQUNIc0IsdUJBQXVCO3dCQUNuQkMsV0FBVzs0QkFDUEMsT0FBTztnQ0FDSHJHLGdCQUFnQixDQUFDQTs0QkFDckI7d0JBQ0o7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0osR0FBRztRQUFDUTtLQUFpQjtJQUVyQixNQUFNLENBQUM4RixnQkFBZ0IsR0FBR3JLLDZEQUFZQSxDQUFDaUIsb0ZBQWlCQSxFQUFFO1FBQ3REcUosYUFBYTtRQUNiQyxhQUFhLENBQUNDLFlBQWM7UUFDNUJDLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLHNCQUFzQkE7UUFDeEM7SUFDSjtJQUVBLE1BQU0sQ0FBQ0UsaUJBQWlCLEdBQUc1Syw2REFBWUEsQ0FBQ2UscUZBQVNBLEVBQUU7UUFDL0N1SixhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNckIsT0FBT3FCLFNBQVNLLGtCQUFrQjtZQUN4QyxJQUFJMUIsTUFBTTtnQkFDTixNQUFNZCxhQUFhYyxLQUFLMkIsS0FBSyxDQUN4QmpCLE1BQU0sQ0FBQyxDQUFDa0I7b0JBQ0wsT0FBTyxDQUFDQSxLQUFLN0IsRUFBRSxLQUFLLENBQUM1RTtnQkFDekIsR0FDQzBHLEdBQUcsQ0FBQyxDQUFDQztvQkFDRiwwREFBMEQ7b0JBQzFELGdCQUFnQjtvQkFDaEIsZ0JBQWdCO29CQUNoQixPQUFPO29CQUNQLE9BQU87d0JBQ0hDLE9BQU8sR0FBNkJELE9BQTFCQSxPQUFPRSxTQUFTLElBQUksSUFBRyxLQUF3QixPQUFyQkYsT0FBT0csT0FBTyxJQUFJLElBQUtDLElBQUk7d0JBQy9EQyxPQUFPTCxPQUFPL0IsRUFBRTt3QkFDaEIsMEJBQTBCO3dCQUMxQnFDLFNBQVM7NEJBQ0xKLFdBQVdGLE9BQU9FLFNBQVM7NEJBQzNCQyxTQUFTSCxPQUFPRyxPQUFPOzRCQUN2QkksUUFBUVAsT0FBT1EsWUFBWTt3QkFDL0I7b0JBQ0o7Z0JBQ0o7Z0JBRUpuRCxjQUFjRDtnQkFFZCxNQUFNcUQsVUFBVXJELFdBQVd3QixNQUFNLENBQUMsQ0FBQ29CO29CQUMvQixJQUFJLENBQUNwSCxjQUFjO3dCQUNmLE9BQU87b0JBQ1g7b0JBQ0EsT0FDSSxDQUFDNkYsTUFBTUMsT0FBTyxDQUFDOUYsaUJBQ2YsQ0FBQ0EsYUFBYThILElBQUksQ0FDZCxDQUFDN0IsVUFDR0EsV0FDQUEsUUFBUXBGLFVBQVUsSUFDbEJvRixRQUFRcEYsVUFBVSxDQUFDd0UsRUFBRSxLQUFLK0IsT0FBT0ssS0FBSyxJQUN0Q3hCLFFBQVE4QixRQUFRLEtBQUs7Z0JBR3JDO2dCQUVBLE1BQU1DLGdCQUFnQkgsUUFBUTdCLE1BQU0sQ0FDaEMsQ0FBQ29CLFNBQ0csQ0FBQ3hHLG1CQUNELENBQUNpRixNQUFNQyxPQUFPLENBQUNsRixvQkFDZixDQUFDQSxnQkFBZ0JxSCxRQUFRLENBQUMsQ0FBQ2IsT0FBT0ssS0FBSztnQkFHL0MxRCxxQkFBcUJpRTtZQUN6QjtRQUNKO1FBQ0FwQixTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQzVDO0lBQ0o7SUFFQSxNQUFNcUIsa0JBQWtCO1FBQ3BCLElBQUl2SCxTQUFTO1lBQ1QsTUFBTTJFLE9BQU8sTUFBTXhFLG1CQUFtQnFILGFBQWEsQ0FBQzVHO1lBQ3BESixrQkFBa0JtRTtZQUNsQixJQUFJQSxNQUFNO2dCQUNOLE1BQU11QyxVQUFVdkMsS0FDWFUsTUFBTSxDQUFDLENBQUNrQjtvQkFDTCxPQUFPLENBQUNBLEtBQUs3QixFQUFFLEtBQUssQ0FBQzVFO2dCQUN6QixHQUNDMEcsR0FBRyxDQUFDLENBQUNDO29CQUNGLE1BQU1nQixtQkFBbUI1SywrRUFBNkJBLENBQ2xEO3dCQUFDNEo7cUJBQU8sRUFDUjt3QkFBQzVHO3FCQUFPLENBQ1gsQ0FBQyxFQUFFO29CQUNKLE9BQU87d0JBQ0g2RyxPQUFPLEdBQTZCRCxPQUExQkEsT0FBT0UsU0FBUyxJQUFJLElBQUcsS0FBd0IsT0FBckJGLE9BQU9HLE9BQU8sSUFBSSxJQUFLQyxJQUFJO3dCQUMvREMsT0FBT0wsT0FBTy9CLEVBQUU7d0JBQ2hCQyxNQUFNOEM7d0JBQ05WLFNBQVM7NEJBQ0xKLFdBQVdGLE9BQU9FLFNBQVM7NEJBQzNCQyxTQUFTSCxPQUFPRyxPQUFPOzRCQUN2QkksUUFBUVAsT0FBT1EsWUFBWTt3QkFDL0I7b0JBQ0o7Z0JBQ0osR0FBRyxzREFBc0Q7aUJBQ3hENUIsTUFBTSxDQUFDLENBQUNvQjtvQkFDTCxJQUFJLENBQUNwSCxjQUFjO3dCQUNmLE9BQU87b0JBQ1g7b0JBQ0EsT0FDSSxDQUFDNkYsTUFBTUMsT0FBTyxDQUFDOUYsaUJBQ2YsQ0FBQ0EsYUFBYThILElBQUksQ0FDZCxDQUFDN0IsVUFDR0EsV0FDQUEsUUFBUXBGLFVBQVUsSUFDbEJvRixRQUFRcEYsVUFBVSxDQUFDd0UsRUFBRSxLQUFLK0IsT0FBT0ssS0FBSyxJQUN0Q3hCLFFBQVE4QixRQUFRLEtBQUs7Z0JBR3JDO2dCQUNKaEUscUJBQXFCOEQ7WUFDekI7UUFDSixPQUFPO1lBQ0gsTUFBTWQsaUJBQWlCO2dCQUNuQlQsV0FBVztvQkFDUE4sUUFBUTt3QkFDSnFDLFVBQVU7NEJBQUVoRCxJQUFJO2dDQUFFaUQsSUFBSS9HOzRCQUFTO3dCQUFFO3dCQUNqQ2dILFlBQVk7NEJBQUVELElBQUk7d0JBQU07b0JBQzVCO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBRUFyTSxnREFBU0EsQ0FBQztRQUNOLElBQUl3RixXQUFXO1lBQ1grRztZQUNBLHdCQUF3QjtZQUN4Qk47WUFDQXhHLGFBQWE7UUFDakI7SUFDSixHQUFHO1FBQUNEO0tBQVU7SUFFZCxtQ0FBbUM7SUFDbkMsTUFBTWdILDBCQUEwQixDQUFDQztRQUM3QixJQUFJLENBQUNBLFlBQVksQ0FBQzdDLE1BQU1DLE9BQU8sQ0FBQzRDLGFBQWFBLFNBQVN2QyxNQUFNLEtBQUssR0FDN0QsT0FBTyxFQUFFO1FBRWIsTUFBTXdDLGNBQW1DLENBQUM7UUFFMUMsMEVBQTBFO1FBQzFFLE1BQU1DLGlCQUFpQjtlQUFJRjtTQUFTLENBQUNHLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUMxQyxJQUFJLENBQUNELEtBQUssQ0FBQ0MsR0FBRyxPQUFPO1lBQ3JCLE1BQU1DLFFBQVFGLEVBQUVHLE9BQU8sR0FBRyxJQUFJL0csS0FBSzRHLEVBQUVHLE9BQU8sRUFBRUMsT0FBTyxLQUFLO1lBQzFELE1BQU1DLFFBQVFKLEVBQUVFLE9BQU8sR0FBRyxJQUFJL0csS0FBSzZHLEVBQUVFLE9BQU8sRUFBRUMsT0FBTyxLQUFLO1lBQzFELE9BQU9GLFFBQVFHLE1BQU0saUNBQWlDOztRQUMxRDtRQUVBLG9DQUFvQztRQUNwQyxNQUFNQyxpQkFBaUJSLGVBQWU1QyxNQUFNLENBQ3hDLENBQUNvQixTQUFXQSxVQUFVLENBQUNBLE9BQU9pQyxRQUFRO1FBRzFDRCxlQUFlRSxPQUFPLENBQUMsQ0FBQ2xDO1lBQ3BCLElBQUksQ0FBQ0EsUUFBUTtZQUViLE1BQU1tQyxlQUFlbkMsT0FBT29DLFlBQVk7WUFDeEMsSUFBSSxDQUFDRCxjQUFjO1lBRW5CLHVEQUF1RDtZQUN2RCxJQUNJbkMsT0FBTy9FLE1BQU0sSUFDYndELE1BQU1DLE9BQU8sQ0FBQ3NCLE9BQU8vRSxNQUFNLEtBQzNCK0UsT0FBTy9FLE1BQU0sQ0FBQzhELE1BQU0sR0FBRyxHQUN6QjtnQkFDRSxJQUFJLENBQUN3QyxXQUFXLENBQUNZLGFBQWEsRUFBRTtvQkFDNUIsc0NBQXNDO29CQUN0Q1osV0FBVyxDQUFDWSxhQUFhLEdBQUc7d0JBQUUsR0FBR25DLE1BQU07b0JBQUM7Z0JBQzVDLE9BQU87b0JBQ0gscURBQXFEO29CQUNyRCxNQUFNcUMsaUJBQ0ZkLFdBQVcsQ0FBQ1ksYUFBYSxDQUFDbEgsTUFBTSxJQUFJLEVBQUU7b0JBRTFDK0UsT0FBTy9FLE1BQU0sQ0FBQ2lILE9BQU8sQ0FBQyxDQUFDeEg7d0JBQ25CLElBQUksQ0FBQ0EsTUFBTTt3QkFDWCxxRUFBcUU7d0JBQ3JFLE1BQU00SCxrQkFBa0JELGVBQWUzQixJQUFJLENBQ3ZDLENBQUM2QixlQUNHQSxnQkFBZ0JBLGFBQWF0RSxFQUFFLEtBQUt2RCxLQUFLdUQsRUFBRTt3QkFHbkQsbUNBQW1DO3dCQUNuQyxJQUFJLENBQUNxRSxpQkFBaUI7NEJBQ2xCRCxlQUFlRyxJQUFJLENBQUM5SDt3QkFDeEI7b0JBQ0o7b0JBRUE2RyxXQUFXLENBQUNZLGFBQWEsR0FBRzt3QkFDeEIsR0FBR1osV0FBVyxDQUFDWSxhQUFhO3dCQUM1QmxILFFBQVFvSDtvQkFDWjtnQkFDSjtZQUNKLE9BQU87Z0JBQ0gsd0NBQXdDO2dCQUN4QyxJQUFJLENBQUNkLFdBQVcsQ0FBQ1ksYUFBYSxFQUFFO29CQUM1QixpQ0FBaUM7b0JBQ2pDWixXQUFXLENBQUNZLGFBQWEsR0FBRzt3QkFDeEIsR0FBR25DLE1BQU07d0JBQ1QvRSxRQUFROzRCQUNKO2dDQUNJZ0QsSUFBSStCLE9BQU8vQixFQUFFO2dDQUNid0UsZUFBZXpDLE9BQU95QyxhQUFhO2dDQUNuQ1osU0FBUzdCLE9BQU82QixPQUFPO2dDQUN2QmxCLFVBQVVYLE9BQU9XLFFBQVE7Z0NBQ3pCK0IsYUFBYTFDLE9BQU8wQyxXQUFXO2dDQUMvQkMsaUJBQWlCM0MsT0FBTzJDLGVBQWU7Z0NBQ3ZDN0osZ0JBQWdCa0gsT0FBT2xILGNBQWM7NEJBQ3pDO3lCQUNIO29CQUNMO2dCQUNKLE9BQU8sSUFDSHlJLFdBQVcsQ0FBQ1ksYUFBYSxDQUFDbEgsTUFBTSxJQUNoQ3dELE1BQU1DLE9BQU8sQ0FBQzZDLFdBQVcsQ0FBQ1ksYUFBYSxDQUFDbEgsTUFBTSxHQUNoRDtvQkFDRSxxRUFBcUU7b0JBQ3JFLE1BQU1xSCxrQkFBa0JmLFdBQVcsQ0FDL0JZLGFBQ0gsQ0FBQ2xILE1BQU0sQ0FBQ3lGLElBQUksQ0FDVCxDQUFDNkIsZUFDR0EsZ0JBQWdCQSxhQUFhdEUsRUFBRSxLQUFLK0IsT0FBTy9CLEVBQUU7b0JBR3JELDRGQUE0RjtvQkFDNUYsTUFBTTJFLHNCQUFzQnJCLFdBQVcsQ0FDbkNZLGFBQ0gsQ0FBQ2xILE1BQU0sQ0FBQ3lGLElBQUksQ0FDVCxDQUFDNkIsZUFDR0EsZ0JBQ0FBLGFBQWFJLGVBQWUsS0FDeEIzQyxPQUFPMkMsZUFBZSxJQUMxQkosYUFBYVYsT0FBTyxLQUFLN0IsT0FBTzZCLE9BQU87b0JBRy9DLHNEQUFzRDtvQkFDdEQsSUFBSSxDQUFDUyxtQkFBbUIsQ0FBQ00scUJBQXFCO3dCQUMxQ3JCLFdBQVcsQ0FBQ1ksYUFBYSxDQUFDbEgsTUFBTSxDQUFDdUgsSUFBSSxDQUFDOzRCQUNsQ3ZFLElBQUkrQixPQUFPL0IsRUFBRTs0QkFDYndFLGVBQWV6QyxPQUFPeUMsYUFBYTs0QkFDbkNaLFNBQVM3QixPQUFPNkIsT0FBTzs0QkFDdkJsQixVQUFVWCxPQUFPVyxRQUFROzRCQUN6QitCLGFBQWExQyxPQUFPMEMsV0FBVzs0QkFDL0JDLGlCQUFpQjNDLE9BQU8yQyxlQUFlOzRCQUN2QzdKLGdCQUFnQmtILE9BQU9sSCxjQUFjO3dCQUN6QztvQkFDSjtnQkFDSjtZQUNKO1FBQ0o7UUFFQSxzRUFBc0U7UUFDdEUrSixPQUFPQyxNQUFNLENBQUN2QixhQUFhVyxPQUFPLENBQUMsQ0FBQ3pJO1lBQ2hDLElBQ0lBLGNBQ0FBLFdBQVd3QixNQUFNLElBQ2pCd0QsTUFBTUMsT0FBTyxDQUFDakYsV0FBV3dCLE1BQU0sS0FDL0J4QixXQUFXd0IsTUFBTSxDQUFDOEQsTUFBTSxHQUFHLEdBQzdCO2dCQUNFdEYsV0FBV3dCLE1BQU0sQ0FBQ3dHLElBQUksQ0FBQyxDQUFDQyxHQUFRQztvQkFDNUIsSUFBSSxDQUFDRCxLQUFLLENBQUNDLEdBQUcsT0FBTztvQkFDckIsTUFBTUMsUUFBUUYsRUFBRUcsT0FBTyxHQUFHLElBQUkvRyxLQUFLNEcsRUFBRUcsT0FBTyxFQUFFQyxPQUFPLEtBQUs7b0JBQzFELE1BQU1DLFFBQVFKLEVBQUVFLE9BQU8sR0FBRyxJQUFJL0csS0FBSzZHLEVBQUVFLE9BQU8sRUFBRUMsT0FBTyxLQUFLO29CQUMxRCxPQUFPRixRQUFRRyxNQUFNLGlDQUFpQzs7Z0JBQzFEO1lBQ0o7UUFDSjtRQUVBLE9BQU9jLE9BQU9DLE1BQU0sQ0FBQ3ZCO0lBQ3pCO0lBRUExTSxnREFBU0EsQ0FBQztRQUNOLElBQUkrRCxnQkFBZ0I2RixNQUFNQyxPQUFPLENBQUM5RixlQUFlO1lBQzdDLDZDQUE2QztZQUM3QyxNQUFNbUssd0JBQXdCbkssYUFBYW1ILEdBQUcsQ0FBQyxDQUFDbEI7Z0JBQzVDLElBQUlBLFdBQVdBLFFBQVFwRixVQUFVLEVBQUU7b0JBQy9CLGlFQUFpRTtvQkFDakUsTUFBTXVKLCtCQUNGNU0sK0VBQTZCQSxDQUN6Qjt3QkFBQ3lJLFFBQVFwRixVQUFVO3FCQUFDLEVBQ3BCO3dCQUFDTDtxQkFBTyxDQUNYLENBQUMsRUFBRTtvQkFFUixPQUFPO3dCQUNILEdBQUd5RixPQUFPO3dCQUNWcEYsWUFBWXVKO29CQUNoQjtnQkFDSjtnQkFDQSxPQUFPbkU7WUFDWDtZQUVBLHlDQUF5QztZQUN6QyxJQUFJb0UsY0FBY0Y7WUFDbEIsSUFBSTVILFFBQVFzRCxNQUFNQyxPQUFPLENBQUN2RCxTQUFTQSxLQUFLNEQsTUFBTSxHQUFHLEdBQUc7Z0JBQ2hELDBEQUEwRDtnQkFDMUQsTUFBTW1FLGtCQUFrQi9ILEtBQUtnSSxNQUFNLENBQy9CLENBQUNwRCxLQUEwQkM7b0JBQ3ZCLElBQUlBLFVBQVVBLE9BQU9vQyxZQUFZLEVBQUU7d0JBQy9CckMsR0FBRyxDQUFDQyxPQUFPb0MsWUFBWSxDQUFDLEdBQUdwQztvQkFDL0I7b0JBQ0EsT0FBT0Q7Z0JBQ1gsR0FDQSxDQUFDO2dCQUdMLDJFQUEyRTtnQkFDM0UsTUFBTXFELG9CQUFvQlAsT0FBT0MsTUFBTSxDQUFDSSxpQkFBaUJ4QyxJQUFJLENBQ3pELENBQUNWLFNBQ0dBLFVBQ0FBLE9BQU8vRSxNQUFNLElBQ2J3RCxNQUFNQyxPQUFPLENBQUNzQixPQUFPL0UsTUFBTSxLQUMzQitFLE9BQU8vRSxNQUFNLENBQUM4RCxNQUFNLEdBQUc7Z0JBRy9CLElBQUlxRSxtQkFBbUI7b0JBQ25CLDhEQUE4RDtvQkFDOURILGNBQWNGLHNCQUFzQmhELEdBQUcsQ0FBQyxDQUFDbEI7d0JBQ3JDLElBQUksQ0FBQ0EsV0FBVyxDQUFDQSxRQUFRdUQsWUFBWSxFQUFFLE9BQU92RDt3QkFFOUMsTUFBTXdFLGlCQUNGSCxlQUFlLENBQUNyRSxRQUFRdUQsWUFBWSxDQUFDO3dCQUN6QyxJQUNJaUIsa0JBQ0FBLGVBQWVwSSxNQUFNLElBQ3JCd0QsTUFBTUMsT0FBTyxDQUFDMkUsZUFBZXBJLE1BQU0sS0FDbkNvSSxlQUFlcEksTUFBTSxDQUFDOEQsTUFBTSxHQUFHLEdBQ2pDOzRCQUNFLCtEQUErRDs0QkFDL0QsTUFBTXVFLGFBQWFELGVBQWVwSSxNQUFNLENBQUN5RixJQUFJLENBQ3pDLENBQUNoRyxPQUFjQSxRQUFRQSxLQUFLdUQsRUFBRSxLQUFLWSxRQUFRWixFQUFFOzRCQUdqRCxJQUFJcUYsWUFBWTtnQ0FDWiwyRUFBMkU7Z0NBQzNFLE9BQU87b0NBQ0gsR0FBR3pFLE9BQU87b0NBQ1Y1RCxRQUFRb0ksZUFBZXBJLE1BQU07Z0NBQ2pDOzRCQUNKLE9BQU87Z0NBQ0gsa0VBQWtFO2dDQUNsRSxNQUFNc0ksZ0JBQWdCO3VDQUFJRixlQUFlcEksTUFBTTtpQ0FBQztnQ0FDaERzSSxjQUFjZixJQUFJLENBQUM7b0NBQ2Z2RSxJQUFJWSxRQUFRWixFQUFFO29DQUNkd0UsZUFBZTVELFFBQVE0RCxhQUFhO29DQUNwQ1osU0FBU2hELFFBQVFnRCxPQUFPO29DQUN4QmxCLFVBQVU5QixRQUFROEIsUUFBUTtvQ0FDMUIrQixhQUFhN0QsUUFBUTZELFdBQVc7b0NBQ2hDQyxpQkFBaUI5RCxRQUFROEQsZUFBZTtvQ0FDeEM3SixnQkFBZ0IrRixRQUFRL0YsY0FBYztnQ0FDMUM7Z0NBRUEsT0FBTztvQ0FDSCxHQUFHK0YsT0FBTztvQ0FDVjVELFFBQVFzSTtnQ0FDWjs0QkFDSjt3QkFDSjt3QkFFQSxxRUFBcUU7d0JBQ3JFLE9BQU87NEJBQ0gsR0FBRzFFLE9BQU87NEJBQ1Y1RCxRQUFRO2dDQUNKO29DQUNJZ0QsSUFBSVksUUFBUVosRUFBRTtvQ0FDZHdFLGVBQWU1RCxRQUFRNEQsYUFBYTtvQ0FDcENaLFNBQVNoRCxRQUFRZ0QsT0FBTztvQ0FDeEJsQixVQUFVOUIsUUFBUThCLFFBQVE7b0NBQzFCK0IsYUFBYTdELFFBQVE2RCxXQUFXO29DQUNoQ0MsaUJBQWlCOUQsUUFBUThELGVBQWU7b0NBQ3hDN0osZ0JBQWdCK0YsUUFBUS9GLGNBQWM7Z0NBQzFDOzZCQUNIO3dCQUNMO29CQUNKO2dCQUNKO1lBQ0o7WUFFQSxtQ0FBbUM7WUFDbkMsTUFBTTBLLHNCQUFzQm5DLHdCQUF3QjRCO1lBQ3BEN0gsUUFBUW9JO1FBQ1o7SUFDSixHQUFHO1FBQUM1SztRQUFjUTtLQUFPO0lBRXpCdkUsZ0RBQVNBLENBQUM7UUFDTixJQUFJd0UsV0FBVyxHQUFHO1lBQ2R5SDtRQUNKO0lBQ0osR0FBRztRQUFDekg7S0FBUztJQUViLE1BQU0rSCxhQUFhO1FBQ2YsSUFBSTdILFNBQVM7WUFDVCxNQUFNMkUsT0FBTyxNQUFNdkUsY0FBYzhKLE1BQU07WUFDdkN4SixhQUFhaUU7WUFDYixJQUFJQSxNQUFNO2dCQUNOLE1BQU13RixlQUFleEYsS0FBS1UsTUFBTSxDQUFDLENBQUNsRSxPQUFjLENBQUNBLEtBQUt1SCxRQUFRO2dCQUM5RC9HLFVBQVV3STtZQUNkO1FBQ0osT0FBTztZQUNILE1BQU1DO1FBQ1Y7SUFDSjtJQUVBLE1BQU1DLHNCQUFzQjtRQUN4QixJQUNJNUssaUJBQ0FBLGNBQWM2SywyQkFBMkIsSUFDekM3SyxjQUFjNkssMkJBQTJCLENBQUNoRSxLQUFLLElBQy9DcEIsTUFBTUMsT0FBTyxDQUFDMUYsY0FBYzZLLDJCQUEyQixDQUFDaEUsS0FBSyxHQUMvRDtZQUNFLE1BQU1pRSxxQkFDRjlLLGNBQWM2SywyQkFBMkIsQ0FBQ2hFLEtBQUssQ0FBQ2pCLE1BQU0sQ0FDbEQsQ0FBQ21GLFNBQWdCQSxVQUFVQSxPQUFPQyxLQUFLLEtBQUs7WUFHcEQsTUFBTWpGLFNBQVMrRSxtQkFBbUIvRSxNQUFNO1lBRXhDLElBQUlBLFdBQVcsR0FBRztnQkFDZCxNQUFNZ0YsU0FBU0Qsa0JBQWtCLENBQUMsRUFBRTtnQkFDcEMsSUFDSUMsVUFDQUEsT0FBT0UseUJBQXlCLElBQ2hDRixPQUFPRSx5QkFBeUIsQ0FBQ3BFLEtBQUssSUFDdENwQixNQUFNQyxPQUFPLENBQUNxRixPQUFPRSx5QkFBeUIsQ0FBQ3BFLEtBQUssR0FDdEQ7b0JBQ0V2RSxjQUNJeUksT0FBT0UseUJBQXlCLENBQUNwRSxLQUFLLENBQUNFLEdBQUcsQ0FDdEMsQ0FBQ21FLFFBQWdCOzRCQUNiRixPQUFPRSxNQUFNQyxTQUFTOzRCQUN0QkMsUUFBUUYsTUFBTUUsTUFBTTt3QkFDeEI7Z0JBR1o7WUFDSixPQUFPLElBQUlyRixTQUFTLEdBQUc7Z0JBQ25CLE1BQU1zRixnQkFBZ0I7dUJBQUlQO2lCQUFtQixDQUFDckMsSUFBSSxDQUM5QyxDQUFDQyxHQUFRQyxJQUFXMkMsU0FBUzNDLEVBQUUxRCxFQUFFLElBQUlxRyxTQUFTNUMsRUFBRXpELEVBQUU7Z0JBR3RELE1BQU04RixTQUFTTSxhQUFhLENBQUMsRUFBRTtnQkFDL0IsSUFDSU4sVUFDQUEsT0FBT0UseUJBQXlCLElBQ2hDRixPQUFPRSx5QkFBeUIsQ0FBQ3BFLEtBQUssSUFDdENwQixNQUFNQyxPQUFPLENBQUNxRixPQUFPRSx5QkFBeUIsQ0FBQ3BFLEtBQUssR0FDdEQ7b0JBQ0V2RSxjQUNJeUksT0FBT0UseUJBQXlCLENBQUNwRSxLQUFLLENBQUNFLEdBQUcsQ0FDdEMsQ0FBQ21FLFFBQWdCOzRCQUNiRixPQUFPRSxNQUFNQyxTQUFTOzRCQUN0QkMsUUFBUUYsTUFBTUUsTUFBTTt3QkFDeEI7Z0JBR1o7WUFDSjtRQUNKLE9BQU87WUFDSDlJLGNBQWM7UUFDbEI7SUFDSjtJQUVBLE1BQU1pSixrQkFBa0I7UUFDcEIsSUFDSTlGLE1BQU1DLE9BQU8sQ0FBQ3JELGVBQ2RBLFdBQVcwRCxNQUFNLEdBQUcsS0FDcEIvRixpQkFDQUEsY0FBYzZLLDJCQUEyQixJQUN6QzdLLGNBQWM2SywyQkFBMkIsQ0FBQ2hFLEtBQUssSUFDL0NwQixNQUFNQyxPQUFPLENBQUMxRixjQUFjNkssMkJBQTJCLENBQUNoRSxLQUFLLEdBQy9EO1lBQ0UsTUFBTTJFLHVCQUNGeEwsY0FBYzZLLDJCQUEyQixDQUFDaEUsS0FBSyxDQUFDakIsTUFBTSxDQUNsRCxDQUFDbUYsU0FBZ0JBLFVBQVVBLE9BQU9DLEtBQUssS0FBSztZQUdwRCxNQUFNUyxzQkFDRkQscUJBQXFCekYsTUFBTSxHQUFHLElBQUl5RixvQkFBb0IsQ0FBQyxFQUFFLEdBQUc7WUFFaEUsSUFDSUMsdUJBQ0FBLG9CQUFvQlIseUJBQXlCLElBQzdDUSxvQkFBb0JSLHlCQUF5QixDQUFDcEUsS0FBSyxJQUNuRHBCLE1BQU1DLE9BQU8sQ0FDVCtGLG9CQUFvQlIseUJBQXlCLENBQUNwRSxLQUFLLEdBRXpEO2dCQUNFLGNBQWM7Z0JBQ2QsSUFBSW1FLFFBQVE7Z0JBQ1osTUFBTVUsa0JBQ0ZELG9CQUFvQlIseUJBQXlCLENBQUNwRSxLQUFLLENBQUM4RSxJQUFJLENBQ3BELENBQUM3RSxPQUFjQSxRQUFRQSxLQUFLcUUsU0FBUyxLQUFLSDtnQkFHbEQsMEdBQTBHO2dCQUMxRyxJQUFJVSxpQkFBaUI7Z0JBQ2pCLGtFQUFrRTtnQkFDbEUsa0RBQWtEO2dCQUN0RDtnQkFFQSxlQUFlO2dCQUNmVixRQUFRO2dCQUNSLE1BQU1ZLG1CQUNGSCxvQkFBb0JSLHlCQUF5QixDQUFDcEUsS0FBSyxDQUFDOEUsSUFBSSxDQUNwRCxDQUFDN0UsT0FBY0EsUUFBUUEsS0FBS3FFLFNBQVMsS0FBS0g7Z0JBR2xELDBHQUEwRztnQkFDMUcsSUFBSVksa0JBQWtCO2dCQUNsQixtRUFBbUU7Z0JBQ25FLGtEQUFrRDtnQkFDdEQ7Z0JBRUEsV0FBVztnQkFDWFosUUFBUTtnQkFDUixNQUFNYSxnQkFBZ0J4SixXQUFXc0osSUFBSSxDQUNqQyxDQUFDWixTQUFnQkEsVUFBVUEsT0FBT0MsS0FBSyxLQUFLQTtnQkFFaER4SSxpQkFBaUJxSixDQUFBQSwwQkFBQUEsb0NBQUFBLGNBQWVULE1BQU0sS0FBSTtnQkFFMUMsTUFBTVUsZUFDRkwsb0JBQW9CUix5QkFBeUIsQ0FBQ3BFLEtBQUssQ0FBQzhFLElBQUksQ0FDcEQsQ0FBQzdFLE9BQWNBLFFBQVFBLEtBQUtxRSxTQUFTLEtBQUtIO2dCQUdsRCwwR0FBMEc7Z0JBQzFHLElBQUljLGNBQWM7b0JBQ2QsTUFBTUMsY0FBY0QsYUFBYUUsb0JBQW9CO29CQUNyRCxNQUFNQyxpQkFBaUJuTywrREFBWUEsQ0FDL0JnTyxjQUNBL04sZ0ZBQW1CQTtvQkFHdkIsdUNBQXVDO29CQUN2QyxJQUFJZ08sZUFBZUEsWUFBWTNFLElBQUksT0FBTyxJQUFJO3dCQUMxQzFFLGdCQUFnQnFKO29CQUNwQixPQUFPLElBQUlFLGtCQUFrQkEsZUFBZTdFLElBQUksT0FBTyxJQUFJO3dCQUN2RDFFLGdCQUFnQnVKO29CQUNwQjtnQkFDQSx1Q0FBdUM7Z0JBQzNDO2dCQUVBLFlBQVk7Z0JBQ1pqQixRQUFRO2dCQUNSLE1BQU1rQixpQkFBaUI3SixXQUFXc0osSUFBSSxDQUNsQyxDQUFDWixTQUFnQkEsVUFBVUEsT0FBT0MsS0FBSyxLQUFLQTtnQkFFaERwSSxrQkFBa0JzSixDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCZCxNQUFNLEtBQUk7Z0JBRTVDLE1BQU1lLGdCQUNGVixvQkFBb0JSLHlCQUF5QixDQUFDcEUsS0FBSyxDQUFDOEUsSUFBSSxDQUNwRCxDQUFDN0UsT0FBY0EsUUFBUUEsS0FBS3FFLFNBQVMsS0FBS0g7Z0JBR2xELDBHQUEwRztnQkFDMUcsSUFBSW1CLGVBQWU7b0JBQ2YsTUFBTUosY0FBY0ksY0FBY0gsb0JBQW9CO29CQUN0RCxNQUFNQyxpQkFBaUJuTywrREFBWUEsQ0FDL0JxTyxlQUNBcE8sZ0ZBQW1CQTtvQkFHdkIsdUNBQXVDO29CQUN2QyxJQUFJZ08sZUFBZUEsWUFBWTNFLElBQUksT0FBTyxJQUFJO3dCQUMxQ3RFLGlCQUFpQmlKO29CQUNyQixPQUFPLElBQUlFLGtCQUFrQkEsZUFBZTdFLElBQUksT0FBTyxJQUFJO3dCQUN2RHRFLGlCQUFpQm1KO29CQUNyQjtnQkFDQSx3Q0FBd0M7Z0JBQzVDO2dCQUVBLGVBQWU7Z0JBQ2ZqQixRQUFRO2dCQUNSLE1BQU1vQixvQkFBb0IvSixXQUFXc0osSUFBSSxDQUNyQyxDQUFDWixTQUFnQkEsVUFBVUEsT0FBT0MsS0FBSyxLQUFLQTtnQkFFaERoSSxxQkFBcUJvSixDQUFBQSw4QkFBQUEsd0NBQUFBLGtCQUFtQmhCLE1BQU0sS0FBSTtnQkFFbEQsTUFBTWlCLG1CQUNGWixvQkFBb0JSLHlCQUF5QixDQUFDcEUsS0FBSyxDQUFDOEUsSUFBSSxDQUNwRCxDQUFDN0UsT0FBY0EsUUFBUUEsS0FBS3FFLFNBQVMsS0FBS0g7Z0JBR2xELDBHQUEwRztnQkFDMUcsSUFBSXFCLGtCQUFrQjtvQkFDbEIsTUFBTU4sY0FBY00saUJBQWlCTCxvQkFBb0I7b0JBQ3pELE1BQU1DLGlCQUFpQm5PLCtEQUFZQSxDQUMvQnVPLGtCQUNBdE8sZ0ZBQW1CQTtvQkFHdkIsdUNBQXVDO29CQUN2QyxJQUFJZ08sZUFBZUEsWUFBWTNFLElBQUksT0FBTyxJQUFJO3dCQUMxQ2xFLG9CQUFvQjZJO29CQUN4QixPQUFPLElBQUlFLGtCQUFrQkEsZUFBZTdFLElBQUksT0FBTyxJQUFJO3dCQUN2RGxFLG9CQUFvQitJO29CQUN4QjtnQkFDQSw0Q0FBNEM7Z0JBQ2hEO1lBQ0o7UUFDSixPQUFPO1lBQ0gsZ0RBQWdEO1lBQ2hEekosaUJBQWlCO1lBQ2pCRSxnQkFBZ0I7WUFDaEJFLGtCQUFrQjtZQUNsQkUsaUJBQWlCO1lBQ2pCRSxxQkFBcUI7WUFDckJFLG9CQUFvQjtRQUN4QjtJQUNKO0lBRUEsTUFBTSxDQUFDeUgsWUFBWSxHQUFHNU8sNkRBQVlBLENBQUNhLDZEQUFTQSxFQUFFO1FBQzFDeUosYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTXJCLE9BQU9xQixTQUFTK0YsY0FBYyxDQUFDekYsS0FBSztZQUMxQyxJQUFJM0IsTUFBTTtnQkFDTixNQUFNd0YsZUFBZXhGLEtBQUtVLE1BQU0sQ0FBQyxDQUFDbEUsT0FBYyxDQUFDQSxLQUFLdUgsUUFBUTtnQkFDOUQvRyxVQUFVd0k7WUFDZDtRQUNKO1FBQ0FsRSxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQzVDO0lBQ0o7SUFFQSxNQUFNOEYsY0FBYyxDQUFDQztRQUNqQixJQUFJLENBQUNBLE1BQU07WUFDUCw4REFBOEQ7WUFDOUQsTUFBTUMsY0FBYyxJQUFJM0s7WUFDeEJELGFBQWE0SztZQUNickoscUJBQXFCO2dCQUNqQixHQUFHRCxpQkFBaUI7Z0JBQ3BCMEYsU0FBU3ZMLDBFQUFnQkEsQ0FBQ21QO1lBQzlCO1lBQ0E7UUFDSjtRQUVBLElBQUk7WUFDQSw0REFBNEQ7WUFDNUQsTUFBTUMsWUFBWSxJQUFJNUssS0FBSzBLO1lBRTNCLDZCQUE2QjtZQUM3QixJQUFJRyxNQUFNRCxVQUFVNUQsT0FBTyxLQUFLO2dCQUM1QnBDLFFBQVFELEtBQUssQ0FBQyx5Q0FBeUMrRjtnQkFDdkQ7WUFDSjtZQUVBM0ssYUFBYTZLO1lBQ2J0SixxQkFBcUI7Z0JBQ2pCLEdBQUdELGlCQUFpQjtnQkFDcEIwRixTQUFTdkwsMEVBQWdCQSxDQUFDb1A7WUFDOUI7WUFFQSxtRUFBbUU7WUFDbkUsSUFBSTNLLGNBQWMySyxVQUFVNUQsT0FBTyxLQUFLL0csV0FBVytHLE9BQU8sSUFBSTtnQkFDMUQ5RyxjQUFjO2dCQUNkb0IscUJBQXFCLENBQUN3SixPQUFlO3dCQUNqQyxHQUFHQSxJQUFJO3dCQUNQakYsVUFBVTtvQkFDZDtZQUNKO1FBQ0osRUFBRSxPQUFPbEIsT0FBTztZQUNaQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtZQUN2Q2xELE1BQU07Z0JBQ0Z5SCxPQUFPO2dCQUNQNkIsYUFBYTtnQkFDYkMsU0FBUztZQUNiO1FBQ0o7SUFDSjtJQUVBLE1BQU1DLGVBQWUsQ0FBQ1A7UUFDbEIsSUFBSSxDQUFDQSxNQUFNO1lBQ1AsOERBQThEO1lBQzlEeEssY0FBYztZQUNkb0IscUJBQXFCO2dCQUNqQixHQUFHRCxpQkFBaUI7Z0JBQ3BCd0UsVUFBVTtZQUNkO1lBQ0E7UUFDSjtRQUVBLElBQUk7WUFDQSw0REFBNEQ7WUFDNUQsTUFBTStFLFlBQVksSUFBSTVLLEtBQUswSztZQUUzQiw2QkFBNkI7WUFDN0IsSUFBSUcsTUFBTUQsVUFBVTVELE9BQU8sS0FBSztnQkFDNUJwQyxRQUFRRCxLQUFLLENBQUMsMENBQTBDK0Y7Z0JBQ3hEO1lBQ0o7WUFFQSwrREFBK0Q7WUFDL0QsdUJBQXVCO1lBQ3ZCLElBQUlFLFVBQVVNLFFBQVEsT0FBTyxLQUFLTixVQUFVTyxVQUFVLE9BQU8sR0FBRztnQkFDNUQsTUFBTUMsTUFBTSxJQUFJcEw7Z0JBQ2hCNEssVUFBVVMsUUFBUSxDQUFDRCxJQUFJRixRQUFRO2dCQUMvQk4sVUFBVVUsVUFBVSxDQUFDRixJQUFJRCxVQUFVO1lBQ3ZDO1lBRUEseUNBQXlDO1lBQ3pDLE1BQU1JLFlBQVkxUSw0Q0FBS0EsQ0FBQytQO1lBRXhCLHlDQUF5QztZQUN6QyxJQUFJOUssYUFBYXlMLFVBQVVDLFFBQVEsQ0FBQzFMLFlBQVk7Z0JBQzVDMkIsTUFBTTtvQkFDRnlILE9BQU87b0JBQ1A2QixhQUFhO29CQUNiQyxTQUFTO2dCQUNiO2dCQUNBO1lBQ0o7WUFFQSwwRUFBMEU7WUFDMUU5SyxjQUFjMEs7WUFFZCx3REFBd0Q7WUFDeER0SixxQkFBcUI7Z0JBQ2pCLEdBQUdELGlCQUFpQjtnQkFDcEJ3RSxVQUFVckssMEVBQWdCQSxDQUFDb1A7WUFDL0I7UUFDSixFQUFFLE9BQU9qRyxPQUFPO1lBQ1pDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDbEQsTUFBTTtnQkFDRnlILE9BQU87Z0JBQ1A2QixhQUNJO2dCQUNKQyxTQUFTO1lBQ2I7UUFDSjtJQUNKO0lBRUEsTUFBTVMsb0JBQW9CO1FBQ3RCLDBCQUEwQjtRQUMxQixJQUFJLENBQUNySixtQkFBbUI7WUFDcEJYLE1BQU07Z0JBQ0Z5SCxPQUFPO2dCQUNQNkIsYUFDSTtnQkFDSkMsU0FBUztZQUNiO1lBQ0E7UUFDSjtRQUVBLGlFQUFpRTtRQUNqRSxJQUFJMUksY0FBY3FCLE1BQU1DLE9BQU8sQ0FBQ3RCLGFBQWE7WUFDekMsK0VBQStFO1lBQy9FLE1BQU1vSix3QkFBd0IsSUFBSUM7WUFFbEMsSUFBSXRMLFFBQVFzRCxNQUFNQyxPQUFPLENBQUN2RCxPQUFPO2dCQUM3QkEsS0FBSytHLE9BQU8sQ0FBQyxDQUFDbEM7b0JBQ1Ysa0VBQWtFO29CQUNsRSxJQUNJQSxVQUNBQSxPQUFPL0UsTUFBTSxJQUNid0QsTUFBTUMsT0FBTyxDQUFDc0IsT0FBTy9FLE1BQU0sR0FDN0I7d0JBQ0UsMENBQTBDO3dCQUMxQyxNQUFNeUwsaUJBQWlCMUcsT0FBTy9FLE1BQU0sQ0FBQ3lGLElBQUksQ0FDckMsQ0FBQ2hHLE9BQWNBLFFBQVFBLEtBQUtpRyxRQUFRLEtBQUs7d0JBRTdDLElBQUkrRixnQkFBZ0I7NEJBQ2hCRixzQkFBc0JHLEdBQUcsQ0FBQzNHLE9BQU9vQyxZQUFZO3dCQUNqRDtvQkFDSixPQUFPLElBQUlwQyxVQUFVQSxPQUFPVyxRQUFRLEtBQUssTUFBTTt3QkFDM0M2RixzQkFBc0JHLEdBQUcsQ0FBQzNHLE9BQU9vQyxZQUFZO29CQUNqRDtnQkFDSjtZQUNKO1lBRUEsb0RBQW9EO1lBQ3BELE1BQU13RSx1QkFBdUJ4SixXQUFXd0IsTUFBTSxDQUFDLENBQUNvQjtnQkFDNUMsSUFBSSxDQUFDQSxRQUFRLE9BQU87Z0JBQ3BCLE9BQU8sQ0FBQ3dHLHNCQUFzQkssR0FBRyxDQUFDN0csT0FBT0ssS0FBSztZQUNsRDtZQUVBLGlGQUFpRjtZQUNqRixNQUFNeUcsc0JBQXNCRixxQkFBcUJoSSxNQUFNLENBQ25ELENBQUNvQixTQUNHLENBQUNBLFVBQ0QsQ0FBQ3hHLG1CQUNELENBQUNpRixNQUFNQyxPQUFPLENBQUNsRixvQkFDZixDQUFDQSxnQkFBZ0JxSCxRQUFRLENBQUMsQ0FBQ2IsT0FBT0ssS0FBSztZQUcvQzFELHFCQUFxQm1LO1FBQ3pCLE9BQU87WUFDSCw4REFBOEQ7WUFDOURuSyxxQkFBcUIsRUFBRTtRQUMzQjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNOEksY0FBYyxJQUFJM0s7UUFDeEIsTUFBTXFCLG9CQUF5QjtZQUMzQjhCLElBQUk7WUFDSm5GLGdCQUFnQixDQUFDQTtZQUNqQnNKLGNBQWM7WUFDZE8saUJBQWlCO1lBQ2pCZCxTQUFTdkwsMEVBQWdCQSxDQUFDbVA7WUFDMUI5RSxVQUFVO1FBQ2Q7UUFFQXZFLHFCQUFxQkQ7UUFDckJ0QixhQUFhNEs7UUFDYnpLLGNBQWM7UUFDZFAsY0FBYztRQUNkRSxRQUFRO1FBQ1IyQiwyQkFBMkI7SUFDL0I7SUFFQSxNQUFNeUsscUJBQXFCLENBQUNDO1FBQ3hCLElBQUksQ0FBQzlKLG1CQUFtQjtZQUNwQlgsTUFBTTtnQkFDRnlILE9BQU87Z0JBQ1A2QixhQUNJO2dCQUNKQyxTQUFTO1lBQ2I7WUFDQTtRQUNKO1FBRUEsNEVBQTRFO1FBQzVFLE1BQU1tQixhQUNGRCxXQUFXL0wsTUFBTSxJQUFJK0wsV0FBVy9MLE1BQU0sQ0FBQzhELE1BQU0sR0FBRyxJQUMxQ2lJLFdBQVcvTCxNQUFNLENBQUMsRUFBRSxHQUNwQitMO1FBRVY1SyxxQkFBcUI7WUFDakI2QixFQUFFLEVBQUVnSix1QkFBQUEsaUNBQUFBLFdBQVloSixFQUFFO1lBQ2xCbkYsY0FBYyxFQUFFbU8sdUJBQUFBLGlDQUFBQSxXQUFZbk8sY0FBYztZQUMxQ3NKLFlBQVksRUFBRTRFLHVCQUFBQSxpQ0FBQUEsV0FBWTVFLFlBQVk7WUFDdENPLGVBQWUsRUFBRXNFLHVCQUFBQSxpQ0FBQUEsV0FBWXRFLGVBQWU7WUFDNUNkLE9BQU8sRUFBRW9GLHVCQUFBQSxpQ0FBQUEsV0FBWXBGLE9BQU87WUFDNUJsQixRQUFRLEVBQUVzRyx1QkFBQUEsaUNBQUFBLFdBQVl0RyxRQUFRO1lBQzlCK0IsV0FBVyxFQUFFdUUsdUJBQUFBLGlDQUFBQSxXQUFZdkUsV0FBVztRQUN4QztRQUVBLDBEQUEwRDtRQUMxRCxNQUFNd0Usd0JBQXdCO1lBQzFCakgsT0FBTyxHQUE0QytHLE9BQXpDQSxXQUFXdk4sVUFBVSxDQUFDeUcsU0FBUyxJQUFJLElBQUcsS0FBK0UsT0FBNUU4RyxXQUFXdk4sVUFBVSxDQUFDMEcsT0FBTyxLQUFLLE9BQU82RyxXQUFXdk4sVUFBVSxDQUFDMEcsT0FBTyxHQUFHLElBQUtDLElBQUk7WUFDcklDLE9BQU8yRyxXQUFXdk4sVUFBVSxDQUFDd0UsRUFBRTtZQUMvQkMsTUFBTThJLFdBQVd2TixVQUFVO1lBQzNCNkcsU0FBUztnQkFDTEosV0FBVzhHLFdBQVd2TixVQUFVLENBQUN5RyxTQUFTO2dCQUMxQ0MsU0FBUzZHLFdBQVd2TixVQUFVLENBQUMwRyxPQUFPO2dCQUN0Q0ksUUFBUXlHLFdBQVd2TixVQUFVLENBQUMrRyxZQUFZO1lBQzlDO1FBQ0o7UUFFQS9GLGNBQWN5TTtRQUVkLDRDQUE0QztRQUM1QyxNQUFNQyxlQUFlbE0sT0FBTzBKLElBQUksQ0FDNUIsQ0FBQ3lDLGFBQW9CQSxXQUFXbkosRUFBRSxNQUFLZ0osdUJBQUFBLGlDQUFBQSxXQUFZdEUsZUFBZTtRQUd0RSxJQUFJd0UsY0FBYztZQUNkeE0sUUFBUTtnQkFDSnNGLE9BQU9rSCxhQUFhbkQsS0FBSztnQkFDekIzRCxPQUFPOEcsYUFBYWxKLEVBQUU7WUFDMUI7UUFDSjtRQUVBcEQsYUFDSW9NLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWXBGLE9BQU8sSUFBRyxJQUFJL0csS0FBS21NLFdBQVdwRixPQUFPLElBQUksSUFBSS9HO1FBRTdERSxjQUNJaU0sQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZdEcsUUFBUSxJQUFHLElBQUk3RixLQUFLbU0sV0FBV3RHLFFBQVEsSUFBSTtRQUUzRHJFLDJCQUEyQjtJQUMvQjtJQUVBLE1BQU0rSyxvQkFBb0IsQ0FBQ0w7UUFDdkIsSUFBSSxDQUFDOUosbUJBQW1CO1lBQ3BCWCxNQUFNO2dCQUNGeUgsT0FBTztnQkFDUDZCLGFBQ0k7Z0JBQ0pDLFNBQVM7WUFDYjtZQUNBO1FBQ0o7UUFFQSwyREFBMkQ7UUFDM0QsTUFBTXdCLGVBQWUsQ0FBQ04sV0FBV3ZOLFVBQVU7UUFFM0MsSUFBSTZOLGNBQWM7WUFDZCx3QkFBd0I7WUFDeEIsNENBQTRDO1lBQzVDLE1BQU1DLGVBQ0ZwTSxRQUFRc0QsTUFBTUMsT0FBTyxDQUFDdkQsUUFDaEJBLEtBQUt3SixJQUFJLENBQUMsQ0FBQzZDO2dCQUNQLE9BQ0lBLEtBQ0FBLEVBQUV2TSxNQUFNLElBQ1J3RCxNQUFNQyxPQUFPLENBQUM4SSxFQUFFdk0sTUFBTSxLQUN0QnVNLEVBQUV2TSxNQUFNLENBQUN5RixJQUFJLENBQ1QsQ0FBQytHLElBQVdBLEtBQUtBLEVBQUV4SixFQUFFLEtBQUsrSSxXQUFXL0ksRUFBRTtZQUduRCxLQUNBO1lBRVYsSUFBSXNKLGNBQWM7b0JBTU9QO2dCQUxyQix5REFBeUQ7Z0JBQ3pENUsscUJBQXFCO29CQUNqQjZCLEVBQUUsRUFBRStJLHVCQUFBQSxpQ0FBQUEsV0FBWS9JLEVBQUU7b0JBQ2xCbkYsY0FBYyxFQUFFa08sdUJBQUFBLGlDQUFBQSxXQUFZbE8sY0FBYztvQkFDMUNzSixjQUFjbUYsYUFBYW5GLFlBQVk7b0JBQ3ZDTyxlQUFlLEVBQUVxRSx1QkFBQUEsa0NBQUFBLDRCQUFBQSxXQUFZdkUsYUFBYSxjQUF6QnVFLGdEQUFBQSwwQkFBMkIvSSxFQUFFO29CQUM5QzRELE9BQU8sRUFBRW1GLHVCQUFBQSxpQ0FBQUEsV0FBWW5GLE9BQU87b0JBQzVCbEIsUUFBUSxFQUFFcUcsdUJBQUFBLGlDQUFBQSxXQUFZckcsUUFBUTtvQkFDOUIrQixXQUFXLEVBQUVzRSx1QkFBQUEsaUNBQUFBLFdBQVl0RSxXQUFXO2dCQUN4QztnQkFFQSwwREFBMEQ7Z0JBQzFELE1BQU13RSx3QkFBd0I7b0JBQzFCakgsT0FBTyxHQUE4Q3NILE9BQTNDQSxhQUFhOU4sVUFBVSxDQUFDeUcsU0FBUyxJQUFJLElBQUcsS0FBbUYsT0FBaEZxSCxhQUFhOU4sVUFBVSxDQUFDMEcsT0FBTyxLQUFLLE9BQU9vSCxhQUFhOU4sVUFBVSxDQUFDMEcsT0FBTyxHQUFHLElBQUtDLElBQUk7b0JBQzNJQyxPQUFPa0gsYUFBYTlOLFVBQVUsQ0FBQ3dFLEVBQUU7b0JBQ2pDQyxNQUFNcUosYUFBYTlOLFVBQVU7b0JBQzdCNkcsU0FBUzt3QkFDTEosV0FBV3FILGFBQWE5TixVQUFVLENBQUN5RyxTQUFTO3dCQUM1Q0MsU0FBU29ILGFBQWE5TixVQUFVLENBQUMwRyxPQUFPO3dCQUN4Q0ksUUFBUWdILGFBQWE5TixVQUFVLENBQUMrRyxZQUFZO29CQUNoRDtnQkFDSjtnQkFFQS9GLGNBQWN5TTtnQkFFZCxXQUFXO2dCQUNYLElBQUlGLFdBQVd2RSxhQUFhLEVBQUU7b0JBQzFCLE1BQU0wRSxlQUFlbE0sT0FBTzBKLElBQUksQ0FDNUIsQ0FBQ3lDOzRCQUNxQko7K0JBQWxCSSxXQUFXbkosRUFBRSxNQUFLK0ksdUJBQUFBLGtDQUFBQSw0QkFBQUEsV0FBWXZFLGFBQWEsY0FBekJ1RSxnREFBQUEsMEJBQTJCL0ksRUFBRTs7b0JBR3ZELElBQUlrSixjQUFjO3dCQUNkeE0sUUFBUTs0QkFDSnNGLE9BQU9rSCxhQUFhbkQsS0FBSzs0QkFDekIzRCxPQUFPOEcsYUFBYWxKLEVBQUU7d0JBQzFCO29CQUNKO2dCQUNKO1lBQ0osT0FBTztnQkFDSCw0Q0FBNEM7Z0JBQzVDMUIsTUFBTTtvQkFDRnlILE9BQU87b0JBQ1A2QixhQUFhO29CQUNiQyxTQUFTO2dCQUNiO2dCQUNBO1lBQ0o7UUFDSixPQUFPO2dCQU1rQmtCO1lBTHJCLDZCQUE2QjtZQUM3QjVLLHFCQUFxQjtnQkFDakI2QixFQUFFLEVBQUUrSSx1QkFBQUEsaUNBQUFBLFdBQVkvSSxFQUFFO2dCQUNsQm5GLGNBQWMsRUFBRWtPLHVCQUFBQSxpQ0FBQUEsV0FBWWxPLGNBQWM7Z0JBQzFDc0osWUFBWSxFQUFFNEUsdUJBQUFBLGlDQUFBQSxXQUFZNUUsWUFBWTtnQkFDdENPLGVBQWUsRUFBRXFFLHVCQUFBQSxrQ0FBQUEsNkJBQUFBLFdBQVl2RSxhQUFhLGNBQXpCdUUsaURBQUFBLDJCQUEyQi9JLEVBQUU7Z0JBQzlDNEQsT0FBTyxFQUFFbUYsdUJBQUFBLGlDQUFBQSxXQUFZbkYsT0FBTztnQkFDNUJsQixRQUFRLEVBQUVxRyx1QkFBQUEsaUNBQUFBLFdBQVlyRyxRQUFRO2dCQUM5QitCLFdBQVcsRUFBRXNFLHVCQUFBQSxpQ0FBQUEsV0FBWXRFLFdBQVc7WUFDeEM7WUFFQSwwREFBMEQ7WUFDMUQsTUFBTXdFLHdCQUF3QjtnQkFDMUJqSCxPQUFPLEdBQTRDK0csT0FBekNBLFdBQVd2TixVQUFVLENBQUN5RyxTQUFTLElBQUksSUFBRyxLQUErRSxPQUE1RThHLFdBQVd2TixVQUFVLENBQUMwRyxPQUFPLEtBQUssT0FBTzZHLFdBQVd2TixVQUFVLENBQUMwRyxPQUFPLEdBQUcsSUFBS0MsSUFBSTtnQkFDcklDLE9BQU8yRyxXQUFXdk4sVUFBVSxDQUFDd0UsRUFBRTtnQkFDL0JDLE1BQU04SSxXQUFXdk4sVUFBVTtnQkFDM0I2RyxTQUFTO29CQUNMSixXQUFXOEcsV0FBV3ZOLFVBQVUsQ0FBQ3lHLFNBQVM7b0JBQzFDQyxTQUFTNkcsV0FBV3ZOLFVBQVUsQ0FBQzBHLE9BQU87b0JBQ3RDSSxRQUFReUcsV0FBV3ZOLFVBQVUsQ0FBQytHLFlBQVk7Z0JBQzlDO1lBQ0o7WUFFQS9GLGNBQWN5TTtZQUVkLFdBQVc7WUFDWCxJQUFJRixXQUFXdkUsYUFBYSxFQUFFO2dCQUMxQixNQUFNMEUsZUFBZWxNLE9BQU8wSixJQUFJLENBQzVCLENBQUN5Qzt3QkFDcUJKOzJCQUFsQkksV0FBV25KLEVBQUUsTUFBSytJLHVCQUFBQSxrQ0FBQUEsNEJBQUFBLFdBQVl2RSxhQUFhLGNBQXpCdUUsZ0RBQUFBLDBCQUEyQi9JLEVBQUU7O2dCQUd2RCxJQUFJa0osY0FBYztvQkFDZHhNLFFBQVE7d0JBQ0pzRixPQUFPa0gsYUFBYW5ELEtBQUs7d0JBQ3pCM0QsT0FBTzhHLGFBQWFsSixFQUFFO29CQUMxQjtnQkFDSjtZQUNKO1FBQ0o7UUFFQSxZQUFZO1FBQ1pwRCxhQUNJbU0sV0FBV25GLE9BQU8sR0FBRyxJQUFJL0csS0FBS2tNLFdBQVduRixPQUFPLElBQUksSUFBSS9HO1FBRTVERSxjQUNJZ00sV0FBV3JHLFFBQVEsR0FBRyxJQUFJN0YsS0FBS2tNLFdBQVdyRyxRQUFRLElBQUk7UUFHMURsRSw0QkFBNEI7SUFDaEM7SUFFQSxNQUFNaUwsbUJBQW1CLE9BQU9DO1FBQzVCLElBQUksQ0FBQ0EsVUFBVTtRQUNmaE4sUUFBUTtZQUNKc0YsT0FBTztZQUNQSSxPQUFPO1FBQ1g7UUFFQSxNQUFNLEVBQUVuQyxJQUFJLEVBQUUsR0FBRyxNQUFNa0IsZ0JBQWdCO1lBQ25DRixXQUFXO2dCQUNQa0QsY0FBY3VGLFNBQVN0SCxLQUFLO1lBQ2hDO1FBQ0o7UUFFQSxNQUFNTCxTQUFTOUIsS0FBSzBKLG9CQUFvQjtRQUN4QyxNQUFNNUcsbUJBQW1CNUssK0VBQTZCQSxDQUNsRDtZQUFDNEo7U0FBTyxFQUNSO1lBQUM1RztTQUFPLENBQ1gsQ0FBQyxFQUFFO1FBRUosTUFBTWlILFFBQVE7WUFDVixHQUFHc0gsUUFBUTtZQUNYekosTUFBTThDO1FBQ1Y7UUFFQXZHLGNBQWM0RjtRQUVkLHVDQUF1QztRQUN2QyxJQUNJQSxNQUFNbkMsSUFBSSxJQUNWbUMsTUFBTW5DLElBQUksQ0FBQzJKLGNBQWMsSUFDekJ4SCxNQUFNbkMsSUFBSSxDQUFDMkosY0FBYyxDQUFDNUgsS0FBSyxLQUFLLFFBQ3RDO1lBQ0VwRCw2QkFBNkI7UUFDakM7UUFFQSxtQkFBbUI7UUFDbkIsSUFBSWhFLFdBQVc0RixNQUFNQyxPQUFPLENBQUM3RixVQUFVO1lBQ25DLE1BQU1ZLGFBQWFaLFFBQVE4TCxJQUFJLENBQzNCLENBQUMzRSxTQUFnQkEsVUFBVUEsT0FBTy9CLEVBQUUsS0FBS29DLE1BQU1BLEtBQUs7WUFHeEQsSUFBSTVHLGNBQWNBLFdBQVdxTyxhQUFhLEVBQUU7Z0JBQ3hDLElBQUk3TSxVQUFVd0QsTUFBTUMsT0FBTyxDQUFDekQsU0FBUztvQkFDakMsTUFBTThNLFdBQVc5TSxPQUFPMEosSUFBSSxDQUN4QixDQUFDOEMsSUFBV0EsS0FBS0EsRUFBRXhKLEVBQUUsS0FBS3hFLFdBQVdxTyxhQUFhO29CQUV0RCxJQUFJQyxVQUFVO3dCQUNWLE1BQU1DLFVBQVU7NEJBQ1ovSCxPQUFPOEgsU0FBUy9ELEtBQUs7NEJBQ3JCM0QsT0FBTzBILFNBQVM5SixFQUFFO3dCQUN0Qjt3QkFDQXRELFFBQVFxTjt3QkFDUjVMLHFCQUFxQjs0QkFDakIsR0FBR0QsaUJBQWlCOzRCQUNwQmlHLGNBQWMzSSxXQUFXd0UsRUFBRTs0QkFDM0IwRSxpQkFBaUJvRixTQUFTOUosRUFBRTt3QkFDaEM7b0JBQ0osT0FBTzt3QkFDSDdCLHFCQUFxQjs0QkFDakIsR0FBR0QsaUJBQWlCOzRCQUNwQmlHLGNBQWMzSSxXQUFXd0UsRUFBRTt3QkFDL0I7b0JBQ0o7Z0JBQ0osT0FBTztvQkFDSDdCLHFCQUFxQjt3QkFDakIsR0FBR0QsaUJBQWlCO3dCQUNwQmlHLGNBQWMzSSxXQUFXd0UsRUFBRTtvQkFDL0I7Z0JBQ0o7WUFDSixPQUFPLElBQUl4RSxZQUFZO2dCQUNuQjJDLHFCQUFxQjtvQkFDakIsR0FBR0QsaUJBQWlCO29CQUNwQmlHLGNBQWMzSSxXQUFXd0UsRUFBRTtnQkFDL0I7WUFDSjtRQUNKO0lBQ0o7SUFFQSxNQUFNZ0ssYUFBYSxDQUFDNUg7UUFDaEIxRixRQUFRMEY7UUFDUmpFLHFCQUFxQjtZQUNqQixHQUFHRCxpQkFBaUI7WUFDcEJ3RyxpQkFBaUJ0QyxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUssS0FBSTtRQUNyQztJQUNKO0lBRUEsTUFBTTZILGVBQWU7UUFDakI5TCxxQkFBcUIsQ0FBQztRQUN0QjNCLGNBQWM7UUFDZEUsUUFBUTtRQUNSRSxhQUFhLElBQUlDO1FBQ2pCRSxjQUFjO1FBQ2RzQiwyQkFBMkI7UUFDM0JHLDRCQUE0QjtJQUNoQztJQUVBLE1BQU0wTCxhQUFhLE9BQU9DO1FBQ3RCLDJCQUEyQjtRQUMzQixJQUNJLENBQUNqTSxrQkFBa0JpRyxZQUFZLElBQy9Cakcsa0JBQWtCaUcsWUFBWSxLQUFLLEdBQ3JDO1lBQ0U3RixNQUFNO2dCQUNGeUgsT0FBTztnQkFDUDZCLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDYjtZQUNBO1FBQ0o7UUFFQSxJQUNJLENBQUMzSixrQkFBa0J3RyxlQUFlLElBQ2xDeEcsa0JBQWtCd0csZUFBZSxLQUFLLEdBQ3hDO1lBQ0VwRyxNQUFNO2dCQUNGeUgsT0FBTztnQkFDUDZCLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDYjtZQUNBO1FBQ0o7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTXVDLHFCQUFxQkMsU0FBU0MsY0FBYyxDQUM5QztRQUVKLE1BQU03RixjQUFjMkYsQ0FBQUEsK0JBQUFBLHlDQUFBQSxtQkFBb0JoSSxLQUFLLEtBQUk7UUFFakQsTUFBTW5CLFlBQVk7WUFDZGpCLElBQUk5QixrQkFBa0I4QixFQUFFO1lBQ3hCbUUsY0FBY2pHLGtCQUFrQmlHLFlBQVk7WUFDNUNPLGlCQUFpQixFQUFDeEcsOEJBQUFBLHdDQUFBQSxrQkFBbUJ3RyxlQUFlO1lBQ3BEN0osZ0JBQWdCLENBQUNBO1lBQ2pCK0ksU0FBU2pILFlBQVl0RSwwRUFBZ0JBLENBQUNzRSxhQUFhO1lBQ25EK0YsVUFBVTVGLGFBQWF6RSwwRUFBZ0JBLENBQUN5RSxjQUFjO1lBQ3REMkgsYUFBYUE7UUFDakI7UUFDQSxJQUFJO1lBQ0EsMENBQTBDO1lBQzFDLElBQUl2RyxrQkFBa0I4QixFQUFFLEdBQUcsR0FBRztnQkFDMUIsSUFBSTFFLFNBQVM7b0JBQ1QsK0NBQStDO29CQUMvQyxNQUFNSyxZQUFZdUUsSUFBSSxDQUFDZTtvQkFFdkIseUNBQXlDO29CQUN6QyxNQUFNc0osVUFDRnJOLFFBQVFzRCxNQUFNQyxPQUFPLENBQUN2RCxRQUNoQkEsS0FBSzRFLEdBQUcsQ0FBQyxDQUFDeUgsSUFBV0EsRUFBRXZKLEVBQUUsRUFBRVcsTUFBTSxDQUFDNkosV0FDbEMsRUFBRTtvQkFFWixpQkFBaUI7b0JBQ2pCck0scUJBQXFCLENBQUM7b0JBRXRCLDhCQUE4QjtvQkFDOUIsSUFBSWtGLFdBQVcsTUFBTTFILFlBQVk4TyxRQUFRLENBQUNGO29CQUUxQyxJQUFJbEgsVUFBVTt3QkFDViw0Q0FBNEM7d0JBQzVDLE1BQU1xSCxnQkFBZ0JySCxTQUFTdkIsR0FBRyxDQUFDLENBQUNsQjs0QkFDaEMsSUFBSUEsV0FBV0EsUUFBUXBGLFVBQVUsRUFBRTtnQ0FDL0IsTUFBTXVKLCtCQUNGNU0sK0VBQTZCQSxDQUN6QjtvQ0FBQ3lJLFFBQVFwRixVQUFVO2lDQUFDLEVBQ3BCO29DQUFDTDtpQ0FBTyxDQUNYLENBQUMsRUFBRTtnQ0FDUixPQUFPO29DQUNILEdBQUd5RixPQUFPO29DQUNWcEYsWUFBWXVKO2dDQUNoQjs0QkFDSjs0QkFDQSxPQUFPbkU7d0JBQ1g7d0JBRUEsbUNBQW1DO3dCQUNuQyxNQUFNK0osY0FDRnZILHdCQUF3QnNIO3dCQUU1QixlQUFlO3dCQUNmdk4sUUFBUXdOO3dCQUNSM1AsZUFBZTJQO29CQUNuQjtnQkFDSixPQUFPO29CQUNILHFDQUFxQztvQkFDckNDLHNDQUFzQzt3QkFDbEMzSixXQUFXOzRCQUFFQyxPQUFPRDt3QkFBVTtvQkFDbEM7Z0JBQ0o7Z0JBRUEsZ0JBQWdCO2dCQUNoQjVDLDJCQUEyQjtnQkFDM0IsSUFBSThMLFdBQVcsVUFBVTtvQkFDckIzTCw0QkFBNEI7Z0JBQ2hDO1lBQ0osT0FBTyxJQUFJTixrQkFBa0JpRyxZQUFZLEdBQUcsR0FBRztnQkFDM0MsSUFBSTdJLFNBQVM7b0JBQ1QseUNBQXlDO29CQUN6QyxNQUFNdVAsV0FBV3BTLGlGQUFnQkE7b0JBQ2pDLE1BQU13SCxPQUFPO3dCQUNULEdBQUdnQixTQUFTO3dCQUNaakIsSUFBSTZLO29CQUNSO29CQUVBLDJDQUEyQztvQkFDM0MsTUFBTWxQLFlBQVl1RSxJQUFJLENBQUNEO29CQUV2QixvREFBb0Q7b0JBQ3BELE1BQU02SyxpQkFBaUJqUCxlQUFlNkssSUFBSSxDQUN0QyxDQUFDNkMsSUFBV0EsRUFBRXZKLEVBQUUsS0FBSzlCLGtCQUFrQmlHLFlBQVk7b0JBRXZELE1BQU0rRSxlQUFlbk4sVUFBVTJLLElBQUksQ0FDL0IsQ0FBQzhDLElBQVdBLEVBQUV4SixFQUFFLEtBQUs5QixrQkFBa0J3RyxlQUFlO29CQUcxRCxJQUFJLENBQUNvRyxrQkFBa0IsQ0FBQzVCLGNBQWM7d0JBQ2xDNUssTUFBTTs0QkFDRnlILE9BQU87NEJBQ1A2QixhQUNJOzRCQUNKQyxTQUFTO3dCQUNiO3dCQUNBO29CQUNKO29CQUVBLHdFQUF3RTtvQkFDeEUsTUFBTWtELGVBQWU7d0JBQ2pCLEdBQUc5SyxJQUFJO3dCQUNQekUsWUFBWXJELCtFQUE2QkEsQ0FDckM7NEJBQUMyUzt5QkFBZSxFQUNoQjs0QkFBQzNQO3lCQUFPLENBQ1gsQ0FBQyxFQUFFO3dCQUNKcUosZUFBZTBFO29CQUNuQjtvQkFFQSxtREFBbUQ7b0JBQ25ELE1BQU04QixlQUFleEssTUFBTUMsT0FBTyxDQUFDdkQsUUFBUTsyQkFBSUE7cUJBQUssR0FBRyxFQUFFO29CQUV6RCx1REFBdUQ7b0JBQ3ZELE1BQU0rTiwwQkFBMEJELGFBQWFFLFNBQVMsQ0FDbEQsQ0FBQzNCLElBQVdBLEtBQUtBLEVBQUVwRixZQUFZLEtBQUtsRSxLQUFLa0UsWUFBWTtvQkFHekQsSUFBSWdILGtCQUFrQjsyQkFBSUg7cUJBQWE7b0JBRXZDLElBQUlDLDRCQUE0QixDQUFDLEdBQUc7d0JBQ2hDLHlFQUF5RTt3QkFDekUsTUFBTUcscUJBQXFCOzRCQUN2QixHQUFHRCxlQUFlLENBQUNGLHdCQUF3Qjt3QkFDL0M7d0JBRUEsSUFDSUcsbUJBQW1CcE8sTUFBTSxJQUN6QndELE1BQU1DLE9BQU8sQ0FBQzJLLG1CQUFtQnBPLE1BQU0sR0FDekM7NEJBQ0UsZ0RBQWdEOzRCQUNoRG9PLG1CQUFtQnBPLE1BQU0sQ0FBQ3VILElBQUksQ0FBQztnQ0FDM0J2RSxJQUFJQyxLQUFLRCxFQUFFO2dDQUNYd0UsZUFBZTBFO2dDQUNmdEYsU0FBUzNELEtBQUsyRCxPQUFPO2dDQUNyQmxCLFVBQVV6QyxLQUFLeUMsUUFBUTtnQ0FDdkIrQixhQUFheEUsS0FBS3dFLFdBQVc7Z0NBQzdCQyxpQkFBaUJ6RSxLQUFLeUUsZUFBZTtnQ0FDckM3SixnQkFBZ0JvRixLQUFLcEYsY0FBYzs0QkFDdkM7d0JBQ0osT0FBTzs0QkFDSCw0Q0FBNEM7NEJBQzVDdVEsbUJBQW1CcE8sTUFBTSxHQUFHO2dDQUN4QjtvQ0FDSWdELElBQUlDLEtBQUtELEVBQUU7b0NBQ1h3RSxlQUFlMEU7b0NBQ2Z0RixTQUFTM0QsS0FBSzJELE9BQU87b0NBQ3JCbEIsVUFBVXpDLEtBQUt5QyxRQUFRO29DQUN2QitCLGFBQWF4RSxLQUFLd0UsV0FBVztvQ0FDN0JDLGlCQUFpQnpFLEtBQUt5RSxlQUFlO29DQUNyQzdKLGdCQUFnQm9GLEtBQUtwRixjQUFjO2dDQUN2Qzs2QkFDSDt3QkFDTDt3QkFFQSxzQ0FBc0M7d0JBQ3RDc1EsZUFBZSxDQUFDRix3QkFBd0IsR0FDcENHO29CQUNSLE9BQU87d0JBQ0gsMkVBQTJFO3dCQUMzRUQsZ0JBQWdCNUcsSUFBSSxDQUFDOzRCQUNqQixHQUFHd0csWUFBWTs0QkFDZi9OLFFBQVE7Z0NBQ0o7b0NBQ0lnRCxJQUFJQyxLQUFLRCxFQUFFO29DQUNYd0UsZUFBZTBFO29DQUNmdEYsU0FBUzNELEtBQUsyRCxPQUFPO29DQUNyQmxCLFVBQVV6QyxLQUFLeUMsUUFBUTtvQ0FDdkIrQixhQUFheEUsS0FBS3dFLFdBQVc7b0NBQzdCQyxpQkFBaUJ6RSxLQUFLeUUsZUFBZTtvQ0FDckM3SixnQkFBZ0JvRixLQUFLcEYsY0FBYztnQ0FDdkM7NkJBQ0g7d0JBQ0w7b0JBQ0o7b0JBRUEsOEJBQThCO29CQUM5QixNQUFNOFAsY0FBY3ZILHdCQUF3QitIO29CQUU1Qyx5Q0FBeUM7b0JBQ3pDaE8sUUFBUXdOO29CQUNSM1AsZUFBZTJQO29CQUNmeE0scUJBQXFCLENBQUM7b0JBRXRCLHFFQUFxRTtvQkFDckUsTUFBTW9NLFVBQVVZLGdCQUNYckosR0FBRyxDQUFDLENBQUN5SCxJQUFXQSxLQUFLQSxFQUFFdkosRUFBRSxFQUN6QlcsTUFBTSxDQUFDNkosU0FDUGEsTUFBTSxDQUFDO3dCQUFDUjtxQkFBUztvQkFFdEIsSUFBSXhILFdBQVcsTUFBTTFILFlBQVk4TyxRQUFRLENBQUNGO29CQUMxQyxJQUFJbEgsVUFBVTt3QkFDViw0Q0FBNEM7d0JBQzVDLE1BQU1pSSxrQkFBa0JqSSxTQUFTdkIsR0FBRyxDQUFDLENBQUNsQjs0QkFDbEMsSUFBSUEsV0FBV0EsUUFBUXBGLFVBQVUsRUFBRTtnQ0FDL0IsTUFBTXVKLCtCQUNGNU0sK0VBQTZCQSxDQUN6QjtvQ0FBQ3lJLFFBQVFwRixVQUFVO2lDQUFDLEVBQ3BCO29DQUFDTDtpQ0FBTyxDQUNYLENBQUMsRUFBRTtnQ0FDUixPQUFPO29DQUNILEdBQUd5RixPQUFPO29DQUNWcEYsWUFBWXVKO2dDQUNoQjs0QkFDSjs0QkFDQSxPQUFPbkU7d0JBQ1g7d0JBRUEsbUNBQW1DO3dCQUNuQyxNQUFNMkssZ0JBQ0ZuSSx3QkFBd0JrSTt3QkFFNUIsc0RBQXNEO3dCQUN0RG5PLFFBQVFvTzt3QkFDUnZRLGVBQWV1UTtvQkFDbkI7Z0JBQ0osT0FBTztvQkFDSCxxQ0FBcUM7b0JBQ3JDQyxzQ0FBc0M7d0JBQ2xDdkssV0FBVzs0QkFBRUMsT0FBT0Q7d0JBQVU7b0JBQ2xDO2dCQUNKO2dCQUVBLGVBQWU7Z0JBQ2Y1QywyQkFBMkI7WUFDL0IsT0FBTztnQkFDSCw2Q0FBNkM7Z0JBQzdDNEw7WUFDSjtRQUNKLEVBQUUsT0FBT3pJLE9BQU87WUFDWkMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0NsRCxNQUFNO2dCQUNGeUgsT0FBTztnQkFDUDZCLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDYjtRQUNKO0lBQ0o7SUFFQSxNQUFNLENBQUMrQyxzQ0FBc0MsR0FBRzdULDREQUFXQSxDQUN2REMsNEZBQXFDQSxFQUNyQztRQUNJcUssYUFBYTtZQUNULHlEQUF5RDtZQUN6RCxJQUFJbkQsa0JBQWtCOEIsRUFBRSxHQUFHLEtBQUs5QyxRQUFRc0QsTUFBTUMsT0FBTyxDQUFDdkQsT0FBTztnQkFDekQsOENBQThDO2dCQUM5QyxJQUFJaU8sa0JBQWtCTSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLFNBQVMsQ0FBQ3pPO2dCQUVoRCxpREFBaUQ7Z0JBQ2pELElBQUkwTyxrQkFBa0I7Z0JBRXRCLGdDQUFnQztnQkFDaEMsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlWLGdCQUFnQnJLLE1BQU0sRUFBRStLLElBQUs7b0JBQzdDLE1BQU05SixTQUFTb0osZUFBZSxDQUFDVSxFQUFFO29CQUVqQyxrREFBa0Q7b0JBQ2xELElBQUk5SixPQUFPL0IsRUFBRSxLQUFLOUIsa0JBQWtCOEIsRUFBRSxFQUFFO3dCQUNwQyx1QkFBdUI7d0JBQ3ZCbUwsZUFBZSxDQUFDVSxFQUFFLEdBQUc7NEJBQ2pCLEdBQUc5SixNQUFNOzRCQUNUVyxVQUFVeEUsa0JBQWtCd0UsUUFBUTt3QkFDeEM7d0JBQ0FrSixrQkFBa0I7d0JBQ2xCO29CQUNKO29CQUVBLGtEQUFrRDtvQkFDbEQsSUFBSTdKLE9BQU8vRSxNQUFNLElBQUl3RCxNQUFNQyxPQUFPLENBQUNzQixPQUFPL0UsTUFBTSxHQUFHO3dCQUMvQyxJQUFLLElBQUk4TyxJQUFJLEdBQUdBLElBQUkvSixPQUFPL0UsTUFBTSxDQUFDOEQsTUFBTSxFQUFFZ0wsSUFBSzs0QkFDM0MsTUFBTXJQLE9BQU9zRixPQUFPL0UsTUFBTSxDQUFDOE8sRUFBRTs0QkFDN0IsSUFBSXJQLEtBQUt1RCxFQUFFLEtBQUs5QixrQkFBa0I4QixFQUFFLEVBQUU7Z0NBQ2xDLHlCQUF5QjtnQ0FDekIrQixPQUFPL0UsTUFBTSxDQUFDOE8sRUFBRSxHQUFHO29DQUNmLEdBQUdyUCxJQUFJO29DQUNQaUcsVUFBVXhFLGtCQUFrQndFLFFBQVE7Z0NBQ3hDO2dDQUNBa0osa0JBQWtCO2dDQUNsQjs0QkFDSjt3QkFDSjt3QkFFQSxJQUFJQSxpQkFBaUI7NEJBQ2pCO3dCQUNKO29CQUNKO2dCQUNKO2dCQUVBLElBQUlBLGlCQUFpQjtvQkFDakIsOEJBQThCO29CQUM5QixNQUFNakIsY0FDRnZILHdCQUF3QitIO29CQUU1Qix5Q0FBeUM7b0JBQ3pDaE8sUUFBUXdOO29CQUNSM1AsZUFBZTJQO2dCQUNuQjtZQUNKO1lBRUEsb0VBQW9FO1lBQ3BFLE1BQU1vQixhQUFhO21CQUFJN08sS0FBSzRFLEdBQUcsQ0FBQyxDQUFDeUgsSUFBV0EsRUFBRXZKLEVBQUU7YUFBRTtZQUNsRDdCLHFCQUFxQixDQUFDO1lBQ3RCLE1BQU02TixlQUE2QixDQUFDO1lBQ3BDQSxhQUFhaE0sRUFBRSxHQUFHO2dCQUFFaU0sSUFBSUY7WUFBVztZQUNuQ0MsYUFBYWhJLFFBQVEsR0FBRztnQkFBRWYsSUFBSTtZQUFNO1lBQ3BDaUosMENBQTBDO2dCQUN0Q2pMLFdBQVc7b0JBQ1BOLFFBQVFxTDtnQkFDWjtZQUNKO1FBQ0o7UUFDQXpLLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLHlDQUF5Q0E7UUFDM0Q7SUFDSjtJQUdKLE1BQU0sQ0FBQ1Isd0JBQXdCLEVBQUVtTCxTQUFTcEwsNkJBQTZCLEVBQUUsQ0FBQyxHQUN0RWhLLDREQUFXQSxDQUFDRyw0RkFBcUNBLEVBQUU7UUFDL0NtSyxhQUFhLENBQUNDO1lBQ1YsTUFBTXJCLE9BQU9xQixTQUFTOEsscUNBQXFDO1lBQzNEbFIsa0JBQWtCK0U7UUFDdEI7UUFDQXNCLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDNUM7SUFDSjtJQUVKLE1BQU0sQ0FBQ2dLLHNDQUFzQyxHQUFHelUsNERBQVdBLENBQ3ZERSw0RkFBcUNBLEVBQ3JDO1FBQ0lvSyxhQUFhLENBQUNwQjtnQkFHYWQ7WUFGdkIsNERBQTREO1lBQzVELG9EQUFvRDtZQUNwRCxNQUFNMkwsa0JBQWlCM0wsbUJBQUFBLFdBQVd1SCxJQUFJLENBQ2xDLENBQUMyRixJQUFXQSxFQUFFakssS0FBSyxLQUFLbEUsa0JBQWtCaUcsWUFBWSxlQURuQ2hGLHVDQUFBQSxpQkFFcEJjLElBQUk7WUFFUCxNQUFNaUosZUFBZWxNLE9BQU8wSixJQUFJLENBQzVCLENBQUM4QyxJQUFXQSxFQUFFeEosRUFBRSxLQUFLOUIsa0JBQWtCd0csZUFBZTtZQUcxRCxJQUFJb0csa0JBQWtCNUIsY0FBYztvQkFhcEJtQjtnQkFaWix3RUFBd0U7Z0JBQ3hFLE1BQU1VLGVBQWU7b0JBQ2pCL0ssSUFBSUMsS0FBS3VMLHFDQUFxQyxDQUFDeEwsRUFBRTtvQkFDakRtRSxjQUFjakcsa0JBQWtCaUcsWUFBWTtvQkFDNUNPLGlCQUFpQnhHLGtCQUFrQndHLGVBQWU7b0JBQ2xEN0osZ0JBQWdCLENBQUNBO29CQUNqQitJLFNBQVNqSCxZQUFZdEUsMEVBQWdCQSxDQUFDc0UsYUFBYTtvQkFDbkQrRixVQUFVNUYsYUFDSnpFLDBFQUFnQkEsQ0FBQ3lFLGNBQ2pCO29CQUNOMkgsYUFDSSxFQUNJNEYsMkJBQUFBLFNBQVNDLGNBQWMsQ0FDbkIsNkJBREpELCtDQURKLHlCQUlHakksS0FBSyxLQUFJO29CQUNoQjVHLFlBQVlzUDtvQkFDWnRHLGVBQWUwRTtnQkFDbkI7Z0JBRUEsZ0VBQWdFO2dCQUNoRSxJQUFJaUMsa0JBQWtCak8sT0FBTzt1QkFBSUE7aUJBQUssR0FBRyxFQUFFO2dCQUUzQyx1REFBdUQ7Z0JBQ3ZELE1BQU0rTiwwQkFBMEJFLGdCQUFnQkQsU0FBUyxDQUNyRCxDQUFDM0IsSUFDR0EsRUFBRXBGLFlBQVksS0FBS2pHLGtCQUFrQmlHLFlBQVk7Z0JBR3pELElBQUk4RywyQkFBMkIsR0FBRztvQkFDOUIseUVBQXlFO29CQUN6RSxNQUFNRyxxQkFDRkQsZUFBZSxDQUFDRix3QkFBd0I7b0JBQzVDLE1BQU0zRixnQkFBZ0I4RixtQkFBbUJwTyxNQUFNLEdBQ3pDOzJCQUFJb08sbUJBQW1CcE8sTUFBTTtxQkFBQyxHQUM5QixFQUFFO29CQUVSc0ksY0FBY2YsSUFBSSxDQUFDO3dCQUNmdkUsSUFBSUMsS0FBS3VMLHFDQUFxQyxDQUFDeEwsRUFBRTt3QkFDakR3RSxlQUFlMEU7d0JBQ2Z0RixTQUFTbUgsYUFBYW5ILE9BQU87d0JBQzdCbEIsVUFBVXFJLGFBQWFySSxRQUFRO3dCQUMvQitCLGFBQWFzRyxhQUFhdEcsV0FBVzt3QkFDckNDLGlCQUFpQnFHLGFBQWFyRyxlQUFlO3dCQUM3QzdKLGdCQUFnQmtRLGFBQWFsUSxjQUFjO29CQUMvQztvQkFFQSxtREFBbUQ7b0JBQ25Ec1EsZUFBZSxDQUFDRix3QkFBd0IsR0FBRzt3QkFDdkMsR0FBR0csa0JBQWtCO3dCQUNyQnBPLFFBQVFzSTtvQkFDWjtnQkFDSixPQUFPO29CQUNILDJFQUEyRTtvQkFDM0U2RixnQkFBZ0I1RyxJQUFJLENBQUM7d0JBQ2pCLEdBQUd3RyxZQUFZO3dCQUNmL04sUUFBUTs0QkFDSjtnQ0FDSWdELElBQUlDLEtBQ0N1TCxxQ0FBcUMsQ0FDckN4TCxFQUFFO2dDQUNQd0UsZUFBZTBFO2dDQUNmdEYsU0FBU21ILGFBQWFuSCxPQUFPO2dDQUM3QmxCLFVBQVVxSSxhQUFhckksUUFBUTtnQ0FDL0IrQixhQUFhc0csYUFBYXRHLFdBQVc7Z0NBQ3JDQyxpQkFDSXFHLGFBQWFyRyxlQUFlO2dDQUNoQzdKLGdCQUFnQmtRLGFBQWFsUSxjQUFjOzRCQUMvQzt5QkFDSDtvQkFDTDtnQkFDSjtnQkFFQSw4QkFBOEI7Z0JBQzlCLE1BQU04UCxjQUFjdkgsd0JBQXdCK0g7Z0JBRTVDLHlDQUF5QztnQkFDekNoTyxRQUFRd047Z0JBQ1IzUCxlQUFlMlA7WUFDbkI7WUFFQSxvRUFBb0U7WUFDcEUsTUFBTW9CLGFBQWE3TyxPQUNiO21CQUNPQSxpQkFBQUEsMkJBQUFBLEtBQU00RSxHQUFHLENBQUMsQ0FBQ3lILElBQVdBLEVBQUV2SixFQUFFO2dCQUM3QkMsS0FBS3VMLHFDQUFxQyxDQUFDeEwsRUFBRTthQUNoRCxHQUNEO2dCQUFDQyxLQUFLdUwscUNBQXFDLENBQUN4TCxFQUFFO2FBQUM7WUFDckQ3QixxQkFBcUIsQ0FBQztZQUN0QixNQUFNNk4sZUFBNkIsQ0FBQztZQUNwQ0EsYUFBYWhNLEVBQUUsR0FBRztnQkFBRWlNLElBQUlGO1lBQVc7WUFDbkNHLDBDQUEwQztnQkFDdENqTCxXQUFXO29CQUNQTixRQUFRcUw7Z0JBQ1o7WUFDSjtRQUNKO1FBQ0F6SyxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyx5Q0FBeUNBO1FBQzNEO0lBQ0o7SUFHSixNQUFNLENBQUMwSywwQ0FBMEMsR0FBR3BWLDZEQUFZQSxDQUM1RGtCLG1GQUErQkEsRUFDL0I7UUFDSW9KLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1yQixPQUFPcUIsU0FBU2dMLG9DQUFvQyxDQUFDMUssS0FBSztZQUNoRSxJQUFJM0IsTUFBTTtnQkFDTiw0Q0FBNEM7Z0JBQzVDLE1BQU15SyxnQkFBZ0J6SyxLQUFLNkIsR0FBRyxDQUFDLENBQUNsQjtvQkFDNUIsSUFBSUEsUUFBUXBGLFVBQVUsRUFBRTt3QkFDcEIsTUFBTXVKLCtCQUNGNU0sK0VBQTZCQSxDQUN6Qjs0QkFBQ3lJLFFBQVFwRixVQUFVO3lCQUFDLEVBQ3BCOzRCQUFDTDt5QkFBTyxDQUNYLENBQUMsRUFBRTt3QkFDUixPQUFPOzRCQUNILEdBQUd5RixPQUFPOzRCQUNWcEYsWUFBWXVKO3dCQUNoQjtvQkFDSjtvQkFDQSxPQUFPbkU7Z0JBQ1g7Z0JBRUEseUNBQXlDO2dCQUN6QyxJQUFJb0UsY0FBYzBGO2dCQUNsQixJQUFJeE4sUUFBUUEsS0FBSzRELE1BQU0sR0FBRyxHQUFHO29CQUN6QiwwREFBMEQ7b0JBQzFELE1BQU1tRSxrQkFBa0IvSCxLQUFLZ0ksTUFBTSxDQUMvQixDQUFDcEQsS0FBMEJDO3dCQUN2QixJQUFJQSxPQUFPb0MsWUFBWSxFQUFFOzRCQUNyQnJDLEdBQUcsQ0FBQ0MsT0FBT29DLFlBQVksQ0FBQyxHQUFHcEM7d0JBQy9CO3dCQUNBLE9BQU9EO29CQUNYLEdBQ0EsQ0FBQztvQkFHTCw4REFBOEQ7b0JBQzlEa0QsY0FBYzBGLGNBQWM1SSxHQUFHLENBQUMsQ0FBQ2xCO3dCQUM3QixNQUFNd0UsaUJBQ0ZILGVBQWUsQ0FBQ3JFLFFBQVF1RCxZQUFZLENBQUM7d0JBQ3pDLElBQ0lpQixrQkFDQUEsZUFBZXBJLE1BQU0sSUFDckJvSSxlQUFlcEksTUFBTSxDQUFDOEQsTUFBTSxHQUFHLEdBQ2pDOzRCQUNFLHNEQUFzRDs0QkFDdEQsTUFBTXlMLG9CQUNGbkgsZUFBZXBJLE1BQU0sQ0FBQ2tPLFNBQVMsQ0FDM0IsQ0FBQ3pPLE9BQWNBLEtBQUt1RCxFQUFFLEtBQUtZLFFBQVFaLEVBQUU7NEJBRzdDLElBQUl1TSxxQkFBcUIsR0FBRztnQ0FDeEIsd0VBQXdFO2dDQUN4RSxNQUFNakgsZ0JBQWdCO3VDQUNmRixlQUFlcEksTUFBTTtpQ0FDM0I7Z0NBQ0RzSSxhQUFhLENBQUNpSCxrQkFBa0IsR0FBRztvQ0FDL0IsR0FBR2pILGFBQWEsQ0FBQ2lILGtCQUFrQjtvQ0FDbkMsOENBQThDO29DQUM5QzNJLFNBQVNoRCxRQUFRZ0QsT0FBTztvQ0FDeEJsQixVQUFVOUIsUUFBUThCLFFBQVE7b0NBQzFCK0IsYUFBYTdELFFBQVE2RCxXQUFXO2dDQUNwQztnQ0FFQSxPQUFPO29DQUNILEdBQUc3RCxPQUFPO29DQUNWNUQsUUFBUXNJO2dDQUNaOzRCQUNKLE9BQU87Z0NBQ0gsa0VBQWtFO2dDQUNsRSxNQUFNQSxnQkFBZ0I7dUNBQ2ZGLGVBQWVwSSxNQUFNO2lDQUMzQjtnQ0FDRHNJLGNBQWNmLElBQUksQ0FBQztvQ0FDZnZFLElBQUlZLFFBQVFaLEVBQUU7b0NBQ2R3RSxlQUFlNUQsUUFBUTRELGFBQWE7b0NBQ3BDWixTQUFTaEQsUUFBUWdELE9BQU87b0NBQ3hCbEIsVUFBVTlCLFFBQVE4QixRQUFRO29DQUMxQitCLGFBQWE3RCxRQUFRNkQsV0FBVztvQ0FDaENDLGlCQUNJOUQsUUFBUThELGVBQWU7b0NBQzNCN0osZ0JBQWdCK0YsUUFBUS9GLGNBQWM7Z0NBQzFDO2dDQUVBLE9BQU87b0NBQ0gsR0FBRytGLE9BQU87b0NBQ1Y1RCxRQUFRc0k7Z0NBQ1o7NEJBQ0o7d0JBQ0o7d0JBRUEscUVBQXFFO3dCQUNyRSxPQUFPOzRCQUNILEdBQUcxRSxPQUFPOzRCQUNWNUQsUUFBUTtnQ0FDSjtvQ0FDSWdELElBQUlZLFFBQVFaLEVBQUU7b0NBQ2R3RSxlQUFlNUQsUUFBUTRELGFBQWE7b0NBQ3BDWixTQUFTaEQsUUFBUWdELE9BQU87b0NBQ3hCbEIsVUFBVTlCLFFBQVE4QixRQUFRO29DQUMxQitCLGFBQWE3RCxRQUFRNkQsV0FBVztvQ0FDaENDLGlCQUNJOUQsUUFBUThELGVBQWU7b0NBQzNCN0osZ0JBQWdCK0YsUUFBUS9GLGNBQWM7Z0NBQzFDOzZCQUNIO3dCQUNMO29CQUNKO2dCQUNKO2dCQUVBLG1DQUFtQztnQkFDbkMsTUFBTThQLGNBQWN2SCx3QkFBd0I0QjtnQkFFNUM3SCxRQUFRd047Z0JBQ1IzUCxlQUFlMlA7Z0JBRWYsTUFBTW5JLFVBQVVyRCxXQUFXd0IsTUFBTSxDQUFDLENBQUNvQjtvQkFDL0IsSUFBSSxDQUFDOUIsTUFBTTt3QkFDUCxPQUFPO29CQUNYO29CQUNBLE9BQU8sQ0FBQ0EsS0FBS3dDLElBQUksQ0FDYixDQUFDN0IsVUFDR0EsUUFBUXBGLFVBQVUsQ0FBQ3dFLEVBQUUsS0FBSytCLE9BQU9LLEtBQUssSUFDdEN4QixRQUFROEIsUUFBUSxLQUFLO2dCQUVqQztnQkFDQWhFLHFCQUNJOEQsUUFBUTdCLE1BQU0sQ0FDVixDQUFDb0IsU0FDRyxDQUFDeEcsbUJBQ0QsQ0FBQ0EsZ0JBQWdCcUgsUUFBUSxDQUFDLENBQUNiLE9BQU9LLEtBQUs7WUFHdkQ7UUFDSjtRQUNBYixTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FDVCw2Q0FDQUE7UUFFUjtJQUNKO0lBR0osTUFBTWdMLGdCQUFnQjtRQUNsQjFOLCtCQUErQjtRQUUvQixJQUFJLENBQUNaLGtCQUFrQjhCLEVBQUUsRUFBRTtZQUN2QjFCLE1BQU07Z0JBQ0Z5SCxPQUFPO2dCQUNQNkIsYUFBYTtnQkFDYkMsU0FBUztZQUNiO1lBQ0E7UUFDSjtRQUVBLElBQUl2TSxTQUFTO1lBQ1QsSUFBSTtnQkFDQSxpQ0FBaUM7Z0JBQ2pDLE1BQU1tUixTQUFTLE1BQU05USxZQUFZK1EsTUFBTSxDQUFDO29CQUNwQzFNLElBQUk5QixrQkFBa0I4QixFQUFFO2dCQUM1QjtnQkFFQSxJQUFJLENBQUN5TSxRQUFRO29CQUNULG9DQUFvQztvQkFDcEMsTUFBTTlRLFlBQVl1RSxJQUFJLENBQUM7d0JBQ25CRixJQUFJOUIsa0JBQWtCOEIsRUFBRTt3QkFDeEJnRSxVQUFVO29CQUNkO2dCQUNKO2dCQUVBLE1BQU0rSCxhQUFhO3VCQUFJN08sS0FBSzRFLEdBQUcsQ0FBQyxDQUFDeUgsSUFBV0EsRUFBRXZKLEVBQUU7aUJBQUU7Z0JBQ2xEN0IscUJBQXFCLENBQUM7Z0JBQ3RCLE1BQU04QixPQUFPLE1BQU10RSxZQUFZOE8sUUFBUSxDQUFDc0I7Z0JBRXhDLElBQUk5TCxNQUFNO29CQUNOLDRDQUE0QztvQkFDNUMsTUFBTXlLLGdCQUFnQnpLLEtBQUs2QixHQUFHLENBQUMsQ0FBQ2xCO3dCQUM1QixJQUFJQSxRQUFRcEYsVUFBVSxFQUFFOzRCQUNwQixNQUFNdUosK0JBQ0Y1TSwrRUFBNkJBLENBQ3pCO2dDQUFDeUksUUFBUXBGLFVBQVU7NkJBQUMsRUFDcEI7Z0NBQUNMOzZCQUFPLENBQ1gsQ0FBQyxFQUFFOzRCQUNSLE9BQU87Z0NBQ0gsR0FBR3lGLE9BQU87Z0NBQ1ZwRixZQUFZdUo7NEJBQ2hCO3dCQUNKO3dCQUNBLE9BQU9uRTtvQkFDWDtvQkFFQSxtQ0FBbUM7b0JBQ25DLE1BQU0rSixjQUFjdkgsd0JBQXdCc0g7b0JBRTVDdk4sUUFBUXdOO29CQUNSM1AsZUFBZTJQO2dCQUNuQjtZQUNKLEVBQUUsT0FBT25KLE9BQU87Z0JBQ1pDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO2dCQUM3Q2xELE1BQU07b0JBQ0Z5SCxPQUFPO29CQUNQNkIsYUFBYTtvQkFDYkMsU0FBUztnQkFDYjtZQUNKO1FBQ0osT0FBTztZQUNILElBQUk7Z0JBQ0EsNENBQTRDO2dCQUM1QyxNQUFNOEUsc0NBQXNDO29CQUN4QzFMLFdBQVc7d0JBQ1AyTCxLQUFLOzRCQUFDMU8sa0JBQWtCOEIsRUFBRTt5QkFBQztvQkFDL0I7Z0JBQ0o7WUFDSixFQUFFLE9BQU93QixPQUFPO2dCQUNaQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtnQkFDN0NsRCxNQUFNO29CQUNGeUgsT0FBTztvQkFDUDZCLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ2I7WUFDSjtRQUNKO1FBRUF4SiwyQkFBMkI7SUFDL0I7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTSxDQUFDc08sc0NBQXNDLEdBQUc1Viw0REFBV0EsQ0FDdkRJLDZGQUFzQ0EsRUFDdEM7UUFDSWtLLGFBQWE7WUFDVCxNQUFNMEssYUFBYTttQkFBSTdPLEtBQUs0RSxHQUFHLENBQUMsQ0FBQ3lILElBQVdBLEVBQUV2SixFQUFFO2FBQUU7WUFDbEQsTUFBTWdNLGVBQTZCLENBQUM7WUFDcENBLGFBQWFoTSxFQUFFLEdBQUc7Z0JBQUVpTSxJQUFJRjtZQUFXO1lBQ25DRywwQ0FBMEM7Z0JBQ3RDakwsV0FBVztvQkFDUE4sUUFBUXFMO2dCQUNaO1lBQ0o7WUFDQWxOLCtCQUErQjtZQUMvQlQsMkJBQTJCO1FBQy9CO1FBQ0FrRCxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FDVCxnREFDQUE7UUFFUjtJQUNKO0lBRUosd0RBQXdEO0lBQ3hELE1BQU1xTCxZQUFZO1FBQ2QsSUFBSSxDQUFDM1AsUUFBUSxDQUFDc0QsTUFBTUMsT0FBTyxDQUFDdkQsT0FBTyxPQUFPO1FBRTFDLE1BQU00UCxRQUFRNVAsS0FBS3lELE1BQU0sQ0FDckIsQ0FBQ29CLFNBQ0dBLFVBQVVBLE9BQU9vQyxZQUFZLEdBQUcsS0FBS3BDLE9BQU9XLFFBQVEsS0FBSyxNQUMvRDVCLE1BQU07UUFDUixPQUFPZ007SUFDWDtJQUNBbFcsZ0RBQVNBLENBQUM7UUFDTixJQUFJd0csWUFBWTtZQUNaa0o7UUFDSjtJQUNKLEdBQUc7UUFBQ2xKO0tBQVc7SUFFZnhHLGdEQUFTQSxDQUFDO1FBQ04sSUFBSSxDQUFDbUMsc0RBQU9BLENBQUNnQyxnQkFBZ0I7WUFDekI0SztRQUNKO0lBQ0osR0FBRztRQUFDNUs7S0FBYztRQTZnQnFDSSxnQkFRSkE7SUFwaEJuRCw2Q0FBNkM7SUFFN0MscUJBQ0k7OzBCQUNJLDhEQUFDNFI7Z0JBQUlsTSxXQUFVOztrQ0FDWCw4REFBQ3hHLHNEQUFJQTt3QkFBQ3dHLFdBQVU7OzBDQUNaLDhEQUFDakgsMERBQUVBOzBDQUFDOzs7Ozs7NEJBQ0hzRCxxQkFDRyw4REFBQzZQOztrREFDRyw4REFBQzNWLHVEQUFLQTs7MERBQ0YsOERBQUNJLDZEQUFXQTswREFDUiw0RUFBQ0MsMERBQVFBOztzRUFDTCw4REFBQ0YsMkRBQVNBOzREQUFDc0osV0FBVTtzRUFBbUQ7Ozs7OztzRUFHeEUsOERBQUN0SiwyREFBU0E7NERBQUNzSixXQUFVO3NFQUFxRDs7Ozs7O3dEQUl6RXhCLEdBQUcyTixRQUFRLGlCQUNSOztnRUFDSzFQLGtCQUFrQix1QkFDZiw4REFBQy9GLDJEQUFTQTtvRUFBQ3NKLFdBQVU7OEVBQ2hCckQsZ0JBQ0c7Ozs7OztnRUFJWEUsbUJBQW1CLHVCQUNoQiw4REFBQ25HLDJEQUFTQTtvRUFBQ3NKLFdBQVU7OEVBQ2hCakQsaUJBQ0c7Ozs7Ozs7eUZBS2hCLDhEQUFDckcsMkRBQVNBOzREQUFDc0osV0FBVTtzRUFDakI7O29FQUNLdkQsa0JBQWtCLFFBQ2JFLGdCQUNBLFlBQ0E7b0VBQUc7a0ZBRVQsOERBQUN5UDt3RUFBR3BNLFdBQVU7Ozs7OztvRUFDYm5ELG1CQUFtQixRQUNkRSxpQkFDQSxhQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU0xQiw4REFBQ3ZHLDJEQUFTQTswREFDTDZGLEtBQ0l5RCxNQUFNLENBQ0gsQ0FBQ29CLFNBQ0csQ0FBQ0EsT0FBT29DLFlBQVksR0FBRyxLQUN2QnBDLE9BQU9pQyxRQUFRLEtBQUssT0FFM0JsQyxHQUFHLENBQUMsQ0FBQ0M7d0RBa0YwQkE7b0RBakY1QixzQ0FBc0M7b0RBQ3RDLE1BQU1tTCxvQkFDRm5MLE9BQU8vRSxNQUFNLElBQ2J3RCxNQUFNQyxPQUFPLENBQUNzQixPQUFPL0UsTUFBTSxLQUMzQitFLE9BQU8vRSxNQUFNLENBQUM4RCxNQUFNLEdBQUc7b0RBRTNCLGlDQUFpQztvREFDakMsTUFBTXFNLG1CQUNGRCxvQkFDTW5MLE9BQU8vRSxNQUFNLENBQ1JvUSxLQUFLLENBQUMsR0FDTnpNLE1BQU0sQ0FDSCxDQUFDbEU7d0RBQ0csNkJBQTZCO3dEQUM3QixNQUFNNFEsaUJBQ0Z0TCxPQUNLL0UsTUFBTSxDQUFDLEVBQUUsQ0FDVHdILGFBQWEsSUFDbEIsT0FFU3hILE1BQU0sQ0FBQyxFQUFFLENBQ1R3SCxhQUFhLENBQ3BCdUIsS0FBSzt3REFFWCwyQkFBMkI7d0RBQzNCLE1BQU11SCxtQkFDRjdRLEtBQUsrSCxhQUFhLElBQ2xCLEtBQ1NBLGFBQWEsQ0FDcEJ1QixLQUFLO3dEQUVYLDRDQUE0Qzt3REFDNUMsT0FDSXVILG9CQUNBRCxtQkFDSUM7b0RBRVosS0FFUixFQUFFO29EQUVaLHFCQUNJLDhEQUFDM1csdURBQWM7OzBFQUdYLDhEQUFDYywwREFBUUE7Z0VBRUwrVixpQkFBZTFTO2dFQUNmK0YsV0FBVyxTQUErQyxPQUF0Q3FNLG9CQUFvQixlQUFlO2dFQUN2RE8sU0FBUyxDQUFDQztvRUFDTiw4QkFBOEI7b0VBQzlCLElBQUk1UyxRQUFRO29FQUVaLDBEQUEwRDtvRUFDMUQsSUFDSTRTLEVBQUVDLE1BQU0sWUFDSkMsZUFDSEYsQ0FBQUEsRUFBRUMsTUFBTSxDQUFDRSxPQUFPLENBQ2IsYUFFQUgsRUFBRUMsTUFBTSxDQUFDRSxPQUFPLENBQ1osa0JBQ0osR0FDTjt3RUFDRTtvRUFDSjtvRUFDQS9FLG1CQUNJL0c7Z0VBRVI7O2tGQUNBLDhEQUFDekssMkRBQVNBO3dFQUNOdUosV0FBVzFHLG1EQUFFQSxDQUNULGFBQ0FnVCxpQkFBaUJyTSxNQUFNLEdBQ25CLEtBQ0E7a0ZBRVIsNEVBQUNpTTs0RUFBSWxNLFdBQVU7OzhGQUNYLDhEQUFDL0csMERBQU1BO29GQUNIZ1UsTUFBSztvRkFDTGpHLFNBQ0k5RixFQUFBQSxvQ0FBQUEsT0FDS3ZHLFVBQVUsQ0FDVm9PLGNBQWMsY0FGbkI3SCx3REFBQUEsa0NBR01DLEtBQUssTUFDWCxTQUNNLGdCQUNBOzhGQUVWLDRFQUFDakksa0VBQWNBO2tHQUNWRSx1RUFBZUEsQ0FDWjhILE9BQ0t2RyxVQUFVLENBQ1Z5RyxTQUFTLEVBQ2RGLE9BQ0t2RyxVQUFVLENBQ1YwRyxPQUFPOzs7Ozs7Ozs7Ozs4RkFJeEIsOERBQUM2SztvRkFBSWxNLFdBQVU7OEZBQ1gsNEVBQUNrTjt3RkFBS2xOLFdBQVU7OzBHQUNaLDhEQUFDa047Z0dBQUtsTixXQUFVOztvR0FFUmtCLE9BQ0t2RyxVQUFVLENBQ1Z5RyxTQUFTO29HQUNoQjtvR0FFRUYsT0FDS3ZHLFVBQVUsQ0FDVjBHLE9BQU87Ozs7Ozs7NEZBR25CSCxPQUFPMEMsV0FBVyxrQkFDZiw4REFBQ2xMLDREQUFPQTs7a0hBQ0osOERBQUNFLG1FQUFjQTt3R0FDWGdVLFNBQVMsQ0FDTEM7NEdBRUFBLEVBQUVNLGVBQWU7d0dBQ3JCO3dHQUNBbk4sV0FBVTtrSEFDViw0RUFBQ3ZILDJGQUFRQTs0R0FDTHVILFdBQVU7NEdBQ1ZpTixNQUNJOzs7Ozs7Ozs7OztrSEFJWiw4REFBQ3RVLG1FQUFjQTt3R0FBQ3FILFdBQVU7a0hBQ3RCLDRFQUFDa007NEdBQUlsTSxXQUFVO3NIQUVQa0IsT0FBTzBDLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFXdEQsOERBQUNuTiwyREFBU0E7d0VBQ051SixXQUFXMUcsbURBQUVBLENBQ1QsK0JBQ0FnVCxpQkFBaUJyTSxNQUFNLEdBQ25CLEtBQ0E7a0ZBRVIsNEVBQUNpTTs0RUFBSWxNLFdBQVU7c0ZBQ1ZrQixPQUFPeUMsYUFBYSxJQUNyQixPQUNXQSxhQUFhLENBQ3RCdUIsS0FBSyxHQUNELE9BQ1d2QixhQUFhLENBQ3RCdUIsS0FBSyxHQUNQOzs7Ozs7Ozs7OztvRUFJYjFHLEdBQUcyTixRQUFRLGlCQUNSOzswRkFDSSw4REFBQzFWLDJEQUFTQTtnRkFDTnVKLFdBQVcxRyxtREFBRUEsQ0FDVCxjQUNBZ1QsaUJBQWlCck0sTUFBTSxHQUNuQixLQUNBOzBGQUVQeEQsa0JBQ0csU0FDQ3lFLENBQUFBLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUTZCLE9BQU8sSUFDVnJKLHdFQUFjQSxDQUNWd0gsT0FBTzZCLE9BQU8sSUFFbEIsZUFBYzs7Ozs7OzBGQUc1Qiw4REFBQ3RNLDJEQUFTQTtnRkFDTnVKLFdBQVcxRyxtREFBRUEsQ0FDVCxxQ0FDQWdULGlCQUFpQnJNLE1BQU0sR0FDbkIsS0FDQTswRkFFUHBELG1CQUNHLFNBQ0MsRUFBQ3FFLE9BQU9XLFFBQVEsaUJBQ2IsOERBQUN4SiwwREFBTUE7b0ZBQ0gyTyxTQUFRO29GQUNSaEgsV0FBVzFHLG1EQUFFQSxDQUNUO29GQUVKOFQsVUFDSW5UO29GQUVKMlMsU0FBUyxDQUNMQzt3RkFFQUEsRUFBRU0sZUFBZTt3RkFDakI1RSxrQkFDSXJIO29GQUVSOzhGQUNDbkUsaUJBQ0c7Ozs7O3lHQUdSLDhEQUFDbVE7b0ZBQ0dsTixXQUFXMUcsbURBQUVBLENBQ1Q7OEZBRUhJLHdFQUFjQSxDQUNYd0gsT0FBT1csUUFBUTs7Ozs7d0ZBRzNCOzs7Ozs7O3FHQUlaLDhEQUFDcEwsMkRBQVNBO2tGQUNOLDRFQUFDeVY7NEVBQ0dsTSxXQUFXMUcsbURBQUVBLENBQ1QsNEVBQ0FnVCxpQkFBaUJyTSxNQUFNLEdBQ25CLEtBQ0E7O2dGQUVQeEQsa0JBQ0csU0FDQ3lFLENBQUFBLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUTZCLE9BQU8sSUFDVnJKLHdFQUFjQSxDQUNWd0gsT0FBTzZCLE9BQU8sSUFFbEIsZUFBYztnRkFFdkJsRyxtQkFDRyxTQUNDLEVBQUNxRSxPQUFPVyxRQUFRLGlCQUNiLDhEQUFDeEosMERBQU1BO29GQUNIMk8sU0FBUTtvRkFDUmhILFdBQVcxRyxtREFBRUEsQ0FDVDtvRkFFSjhULFVBQ0luVDtvRkFFSjJTLFNBQVMsQ0FDTEM7d0ZBRUFBLEVBQUVNLGVBQWU7d0ZBQ2pCNUUsa0JBQ0lySDtvRkFFUjs4RkFDQ25FLGlCQUNHOzs7Ozt5R0FHUiw4REFBQ21RO29GQUNHbE4sV0FBVzFHLG1EQUFFQSxDQUNUOzhGQUVISSx3RUFBY0EsQ0FDWHdILE9BQU9XLFFBQVE7Ozs7O3dGQUczQjs7Ozs7Ozs7Ozs7OzsrREFoT1hYLE9BQU8vQixFQUFFOzs7Ozs0REF1T2pCbU4saUJBQWlCckwsR0FBRyxDQUNqQixDQUNJckYsTUFDQXlSLHNCQUVBLDhEQUFDelcsMERBQVFBO29FQUVMK1YsaUJBQ0kxUztvRUFFSitGLFdBQVcxRyxtREFBRUEsQ0FDVCxTQUNBK1QsVUFDSWYsaUJBQWlCck0sTUFBTSxHQUNuQixJQUNGLEtBQ0E7b0VBRVYyTSxTQUFTLENBQ0xDO3dFQUVBLDhCQUE4Qjt3RUFDOUIsSUFBSTVTLFFBQ0E7d0VBRUosMERBQTBEO3dFQUMxRCxJQUNJNFMsRUFBRUMsTUFBTSxZQUNKQyxlQUNIRixDQUFBQSxFQUFFQyxNQUFNLENBQUNFLE9BQU8sQ0FDYixhQUVBSCxFQUFFQyxNQUFNLENBQUNFLE9BQU8sQ0FDWixrQkFDSixHQUNOOzRFQUNFO3dFQUNKO3dFQUNBL0UsbUJBQ0kvRztvRUFFUjs7c0ZBQ0EsOERBQUN6SywyREFBU0E7NEVBQ051SixXQUFXMUcsbURBQUVBLENBQ1Q7c0ZBRUosNEVBQUM0UztnRkFBSWxNLFdBQVU7O29GQUNWcU4sVUFDRyxtQkFDQSw4REFBQ25CO3dGQUFJbE0sV0FBVTs7Ozs7O2tHQUVuQiw4REFBQ2tNO3dGQUFJbE0sV0FBVTs7Ozs7O2tHQUNmLDhEQUFDa007d0ZBQUlsTSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzRkFJdkIsOERBQUN2SiwyREFBU0E7NEVBQ051SixXQUFXMUcsbURBQUVBLENBQ1Q7c0ZBRUosNEVBQUM0UztnRkFBSWxNLFdBQVU7MEZBQ1ZwRSxLQUFLK0gsYUFBYSxJQUNuQixLQUNTQSxhQUFhLENBQ3BCdUIsS0FBSyxHQUNELEtBQ1N2QixhQUFhLENBRWpCdUIsS0FBSyxHQUNWOzs7Ozs7Ozs7Ozt3RUFHYjFHLEdBQUcyTixRQUFRLGlCQUNSOzs4RkFDSSw4REFBQzFWLDJEQUFTQTtvRkFDTnVKLFdBQVcxRyxtREFBRUEsQ0FDVDs4RkFFSG1ELGtCQUNHLFNBQ0NiLENBQUFBLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTW1ILE9BQU8sSUFDUnJKLHdFQUFjQSxDQUNWa0MsS0FBS21ILE9BQU8sSUFFaEIsZUFBYzs7Ozs7OzhGQUU1Qiw4REFBQ3RNLDJEQUFTQTtvRkFDTnVKLFdBQVcxRyxtREFBRUEsQ0FDVDs4RkFFSHVELG1CQUNHLFNBQ0MsRUFBQ2pCLEtBQUtpRyxRQUFRLGlCQUNYLDhEQUFDeEosMERBQU1BO3dGQUNIMk8sU0FBUTt3RkFDUmhILFdBQVcxRyxtREFBRUEsQ0FDVDt3RkFFSjhULFVBQ0luVDt3RkFFSjJTLFNBQVMsQ0FDTEM7NEZBRUFBLEVBQUVNLGVBQWU7NEZBQ2pCNUUsa0JBQ0kzTTt3RkFFUjtrR0FDQ21CLGlCQUNHOzs7Ozs2R0FHUiw4REFBQ21RO3dGQUNHbE4sV0FBVzFHLG1EQUFFQSxDQUNUO2tHQUVISSx3RUFBY0EsQ0FDWGtDLEtBQUtpRyxRQUFROzs7Ozs0RkFHekI7Ozs7Ozs7eUdBSVosOERBQUNwTCwyREFBU0E7c0ZBQ04sNEVBQUN5VjtnRkFBSWxNLFdBQVU7O29GQUNWdkQsa0JBQ0csU0FDQ2IsQ0FBQUEsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNbUgsT0FBTyxJQUNSckosd0VBQWNBLENBQ1ZrQyxLQUFLbUgsT0FBTyxJQUVoQixlQUFjO29GQUN2QmxHLG1CQUNHLFNBQ0MsRUFBQ2pCLEtBQUtpRyxRQUFRLGlCQUNYLDhEQUFDeEosMERBQU1BO3dGQUNIMk8sU0FBUTt3RkFDUmhILFdBQVcxRyxtREFBRUEsQ0FDVDt3RkFFSjhULFVBQ0luVDt3RkFFSjJTLFNBQVMsQ0FDTEM7NEZBRUFBLEVBQUVNLGVBQWU7NEZBQ2pCNUUsa0JBQ0kzTTt3RkFFUjtrR0FDQ21CLGlCQUNHOzs7Ozs2R0FHUiw4REFBQ21RO3dGQUNHbE4sV0FBVzFHLG1EQUFFQSxDQUNUO2tHQUVISSx3RUFBY0EsQ0FDWGtDLEtBQUtpRyxRQUFROzs7Ozs0RkFHekI7Ozs7Ozs7Ozs7Ozs7bUVBL0pYLFFBQW1Cd0wsT0FBWHpSLEtBQUt1RCxFQUFFLEVBQUMsS0FBUyxPQUFOa087Ozs7Ozt1REFoUC9CLFFBQWtCLE9BQVZuTSxPQUFPL0IsRUFBRTs7Ozs7Z0RBd1psQzs7Ozs7Ozs7Ozs7O2tEQUlaLDhEQUFDeEYsdURBQVVBOzswREFDUCw4REFBQ3VTO2dEQUFJbE0sV0FBVTs7a0VBQ1gsOERBQUM3SCx3REFBS0E7d0RBQUM2SCxXQUFVO2tFQUFxQjs7Ozs7O2tFQUd0Qyw4REFBQzNHLHdEQUFLQTt3REFDRjJOLFNBQ0l6TixpREFBUUEsQ0FBQ2UsV0FDVDBSLGNBQWUxUixDQUFBQSxDQUFBQSxpQkFBQUEsbUJBQUFBLDZCQUFBQSxPQUFRZ1QsTUFBTSxjQUFkaFQsNEJBQUFBLGlCQUFrQixLQUMzQixnQkFDQTt3REFFVjBGLFdBQVU7a0VBQ1R6RyxpREFBUUEsQ0FBQ2UsVUFBVUEsT0FBT2lULE9BQU8sR0FBRzs7Ozs7O29EQUV4Q2hVLGlEQUFRQSxDQUFDZSxXQUNOMFIsY0FBZTFSLENBQUFBLENBQUFBLGtCQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFnVCxNQUFNLGNBQWRoVCw2QkFBQUEsa0JBQWtCLG9CQUM3Qiw4REFBQ2tUO3dEQUFNeE4sV0FBVTtrRUFBbUI7Ozs7Ozs7Ozs7OzswREFRaEQsOERBQUMzSCwwREFBTUE7Z0RBQ0gySCxXQUFVO2dEQUNWb04sVUFBVW5UO2dEQUNWMlMsU0FBU25GOzBEQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7cURBTXhDLDhEQUFDeUU7Z0NBQUlsTSxXQUFVOzBDQUNYLDRFQUFDM0gsMERBQU1BO29DQUNIK1UsVUFBVW5UO29DQUNWMlMsU0FBU25GOzhDQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTzNDcEwsUUFDR25DLGtCQUNBQSwwQkFBQUEscUNBQUFBLDZDQUFBQSxjQUFlNkssMkJBQTJCLGNBQTFDN0ssa0VBQUFBLG1EQUFBQSwyQ0FBNEM2RyxLQUFLLGNBQWpEN0csdUVBQUFBLGlEQUFtRDJMLElBQUksQ0FDbkQsQ0FBQ1osU0FDR0EsT0FBT0MsS0FBSyxLQUFLLGtCQUNqQkQsT0FBT3dJLE1BQU0sS0FBSyx3QkFFdEIsOERBQUNqVSxzREFBSUE7d0JBQUN3RyxXQUFVO2tDQUNaLDRFQUFDNUksa0VBQVdBOzRCQUNScUQsU0FBU0E7NEJBQ1RQLGVBQWVBOzRCQUNmRCxRQUFRQSxVQUFVLENBQUNtRTs0QkFDbkJoRSxrQkFBa0JBOzRCQUNsQkMsbUJBQW1CQTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXZDLDhEQUFDdkIsNEVBQWNBO2dCQUNYNFUsWUFBWW5RO2dCQUNab1EsZUFBZW5RO2dCQUNmb1EsY0FBY3ZFO2dCQUNkRCxjQUFjQTtnQkFDZHlFLHlCQUNJeFEsa0JBQWtCOEIsRUFBRSxHQUFHLElBQ2pCLElBQU1sQiwrQkFBK0IsUUFDckM2UDtnQkFFVkMsdUJBQXVCMVEsa0JBQWtCOEIsRUFBRSxHQUFHO2dCQUM5QzZPLHVCQUFzQjtnQkFDdEI5SSxPQUNJN0gsa0JBQWtCOEIsRUFBRSxHQUFHLElBQ2pCLHVCQUNBO2dCQUVWOE8sWUFBWTVRLGtCQUFrQjhCLEVBQUUsR0FBRyxJQUFJLFdBQVc7Z0JBQ2xEK08sWUFBVztnQkFDWEMsa0JBQWlCOzBCQUNqQiw0RUFBQ2pDO29CQUFJbE0sV0FBVTs7c0NBQ1gsOERBQUNrTTs0QkFBSWxNLFdBQVU7O2dDQUNWckYsNEJBQ0csOERBQUMxQiwwREFBTUE7b0NBQ0grTixTQUNJck0sRUFBQUEsbUJBQUFBLFdBQVd5RSxJQUFJLGNBQWZ6RSx3Q0FBQUEsa0NBQUFBLGlCQUFpQm9PLGNBQWMsY0FBL0JwTyxzREFBQUEsZ0NBQWlDd0csS0FBSyxNQUN0QyxTQUNNLGdCQUNBO29DQUVWbkIsV0FBVTs7c0RBQ1YsOERBQUM3RywrREFBV0E7NENBQ1JpVixHQUFHLEdBQUV6VCxzQkFBQUEsV0FBVzZHLE9BQU8sY0FBbEI3RywwQ0FBQUEsb0JBQW9COEcsTUFBTTs0Q0FDL0I0TSxLQUFLLEdBQTBDMVQsT0FBdkNBLEVBQUFBLHVCQUFBQSxXQUFXNkcsT0FBTyxjQUFsQjdHLDJDQUFBQSxxQkFBb0J5RyxTQUFTLEtBQUksSUFBRyxLQUFxQyxPQUFsQ3pHLEVBQUFBLHVCQUFBQSxXQUFXNkcsT0FBTyxjQUFsQjdHLDJDQUFBQSxxQkFBb0IwRyxPQUFPLEtBQUksSUFBS0MsSUFBSTs7Ozs7O3NEQUUzRiw4REFBQ3BJLGtFQUFjQTtzREFDVkUsdUVBQWVBLENBQ1p1QixFQUFBQSx1QkFBQUEsV0FBVzZHLE9BQU8sY0FBbEI3RywyQ0FBQUEscUJBQW9CeUcsU0FBUyxPQUN6QnpHLG9CQUFBQSxXQUFXeUUsSUFBSSxjQUFmekUsd0NBQUFBLGtCQUFpQnlHLFNBQVMsR0FDOUJ6RyxFQUFBQSx1QkFBQUEsV0FBVzZHLE9BQU8sY0FBbEI3RywyQ0FBQUEscUJBQW9CMEcsT0FBTyxPQUN2QjFHLG9CQUFBQSxXQUFXeUUsSUFBSSxjQUFmekUsd0NBQUFBLGtCQUFpQjBHLE9BQU87Ozs7Ozs7Ozs7Ozs4Q0FLNUMsOERBQUM2SztvQ0FBSWxNLFdBQVU7O3NEQUNYLDhEQUFDaEgsMERBQUVBOzRDQUFDZ0gsV0FBVTtzREFDVHJGLENBQUFBLHVCQUFBQSxrQ0FBQUEsdUJBQUFBLFdBQVk2RyxPQUFPLGNBQW5CN0csMkNBQUFBLHFCQUFxQnlHLFNBQVMsTUFDL0J6Ryx1QkFBQUEsa0NBQUFBLG9CQUFBQSxXQUFZeUUsSUFBSSxjQUFoQnpFLHdDQUFBQSxrQkFBa0J5RyxTQUFTLE1BQzNCekcsdUJBQUFBLGtDQUFBQSx1QkFBQUEsV0FBWTZHLE9BQU8sY0FBbkI3RywyQ0FBQUEscUJBQXFCMEcsT0FBTyxNQUM1QjFHLHVCQUFBQSxrQ0FBQUEsb0JBQUFBLFdBQVl5RSxJQUFJLGNBQWhCekUsd0NBQUFBLGtCQUFrQjBHLE9BQU8sSUFDbkIsR0FBd0UxRyxPQUFyRUEsRUFBQUEsdUJBQUFBLFdBQVc2RyxPQUFPLGNBQWxCN0csMkNBQUFBLHFCQUFvQnlHLFNBQVMsT0FBSXpHLG9CQUFBQSxXQUFXeUUsSUFBSSxjQUFmekUsd0NBQUFBLGtCQUFpQnlHLFNBQVMsS0FBSSxJQUFHLEtBQWlFLE9BQTlEekcsRUFBQUEsdUJBQUFBLFdBQVc2RyxPQUFPLGNBQWxCN0csMkNBQUFBLHFCQUFvQjBHLE9BQU8sT0FBSTFHLG9CQUFBQSxXQUFXeUUsSUFBSSxjQUFmekUsd0NBQUFBLGtCQUFpQjBHLE9BQU8sS0FBSSxJQUFLQyxJQUFJLEtBQzVJOzs7Ozs7d0NBRVQzRyxjQUNJQSxDQUFBQSxDQUFBQSx1QkFBQUEsa0NBQUFBLG9CQUFBQSxXQUFZeUUsSUFBSSxjQUFoQnpFLHlDQUFBQSxtQ0FBQUEsa0JBQWtCb08sY0FBYyxjQUFoQ3BPLHVEQUFBQSxpQ0FBa0N3RyxLQUFLLE1BQ3hDLHVCQUNJLDhEQUFDK0s7OzhEQUNHLDhEQUFDb0M7b0RBQUV0TyxXQUFVOzhEQUEyQjs7Ozs7O2dEQUd2Q3JGLENBQUFBLHVCQUFBQSxrQ0FBQUEsb0JBQUFBLFdBQVl5RSxJQUFJLGNBQWhCekUseUNBQUFBLG1DQUFBQSxrQkFBa0JvTyxjQUFjLGNBQWhDcE8sdURBQUFBLGlDQUNLNFQsSUFBSSxLQUNONVQsV0FBV3lFLElBQUksQ0FBQzJKLGNBQWMsQ0FBQ3dGLElBQUksQ0FDOUJ0TyxNQUFNLEdBQUcsbUJBQ1YsOERBQUN1TztvREFBR3hPLFdBQVU7OERBQ1RyRixXQUFXeUUsSUFBSSxDQUFDMkosY0FBYyxDQUFDd0YsSUFBSSxDQUFDdE4sR0FBRyxDQUNwQyxDQUNJd04sS0FDQXBCLHNCQUVBLDhEQUFDcUI7NERBRUcxTyxXQUFVOzs4RUFDViw4REFBQ2tOO29FQUFLbE4sV0FBVTs4RUFBMkI7Ozs7Ozs4RUFHM0MsOERBQUNrTjs7d0VBRU91QixJQUNLRSxZQUFZLENBQ1p6SixLQUFLO3dFQUNaO3dFQUFJO3dFQUNKO3dFQUVFdUosSUFDS25KLE1BQU0sQ0FDTm5FLEtBQUs7Ozs7Ozs7OzJEQWZia007Ozs7Ozs7Ozs7Ozs7OztpRUF5QmpDLDhEQUFDaUI7NENBQUV0TyxXQUFVO3NEQUFvQzs7Ozs7Z0RBR3JEOzs7Ozs7Ozs7Ozs7O3NDQUdaLDhEQUFDa007NEJBQUlsTSxXQUFVOzs4Q0FDWCw4REFBQzFILDhEQUFRQTtvQ0FDTDZJLE9BQU87b0NBQ1B5TixLQUFLO29DQUNMQyxpQkFBZ0I7b0NBQ2hCQyxTQUFTbFIsa0JBQWtCcUQsR0FBRyxDQUMxQixDQUFDOE4sU0FBd0I7NENBQ3JCLEdBQUdBLE1BQU07NENBQ1R4TixPQUFPeU4sT0FBT0QsT0FBT3hOLEtBQUs7d0NBQzlCO29DQUVKQSxPQUFPNUc7b0NBQ1BzVSxVQUFVckc7Ozs7Ozs4Q0FFZCw4REFBQ3JRLHlGQUFnQkE7b0NBQ2I0SSxPQUFNO29DQUNOK04sWUFBWUMsT0FBT3ZULGlCQUFBQSwyQkFBQUEsS0FBTTJGLEtBQUssS0FBSztvQ0FDbkMwTixVQUFVOUY7b0NBQ1ZpRyxPQUFPO29DQUNQUixLQUFLO29DQUNMblUsU0FBU0E7b0NBQ1Q0VSxrQkFBa0I7Ozs7Ozs7Ozs7OztzQ0FJMUIsOERBQUNuRDs0QkFBSWxNLFdBQVU7O2dDQUNWdkQsa0JBQWtCLHVCQUNmLDhEQUFDNUQsOERBQVVBO29DQUNQc0csSUFBRztvQ0FDSHlQLEtBQUs7b0NBQ0xyTixPQUFPekY7b0NBQ1BtVCxVQUFVeEk7b0NBQ1Z0RixPQUFPeEUsZ0JBQWdCO29DQUN2QjJTLFlBQVc7b0NBQ1hDLGFBQWEsR0FBNkIsT0FBMUI1UyxnQkFBZ0IsV0FBVTtvQ0FDMUM2UyxNQUFLO29DQUNMQyxNQUFLO29DQUNMQyxlQUFlO29DQUNmQyxNQUFNblgsMkZBQUtBO29DQUNYd0gsV0FBVTs7Ozs7O2dDQUlqQm5ELG1CQUFtQix1QkFDaEIsOERBQUNoRSw4REFBVUE7b0NBQ1BzRyxJQUFHO29DQUNIeVAsS0FBSztvQ0FDTHJOLE9BQU90RixjQUFjNlI7b0NBQ3JCbUIsVUFBVWhJO29DQUNWOUYsT0FBT3BFLGlCQUFpQjtvQ0FDeEJ3UyxhQUFhLEdBQStCLE9BQTVCeFMsaUJBQWlCLFlBQVc7b0NBQzVDeVMsTUFBSztvQ0FDTEMsTUFBSyxXQUFXLHVDQUF1Qzs7b0NBQ3ZESCxZQUFXO29DQUNYTSxZQUFXLFFBQVEsNkJBQTZCOztvQ0FDaERGLGVBQWU7b0NBQ2ZHLFdBQVc7b0NBQ1hGLE1BQU1uWCwyRkFBS0E7b0NBQ1h3SCxXQUFVOzs7Ozs7Ozs7Ozs7d0JBS3JCL0Msc0JBQXNCLHVCQUNuQiw4REFBQzlFLHdEQUFLQTs0QkFDRjJYLFNBQVE7NEJBQ1IzTyxPQUFPaEUsb0JBQW9CO3NDQUMzQiw0RUFBQy9FLDhEQUFRQTtnQ0FDTCtHLElBQUc7Z0NBQ0g0USxNQUFNO2dDQUNOL1AsV0FBVTtnQ0FDVnVQLGFBQVk7Z0NBQ1pTLFlBQVksRUFBRTNTLDhCQUFBQSx3Q0FBQUEsa0JBQW1CdUcsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPaEUsOERBQUM5Syw0RUFBY0E7Z0JBQ1g0VSxZQUFZaFE7Z0JBQ1ppUSxlQUFlaFE7Z0JBQ2ZpUSxjQUFjLElBQU12RSxXQUFXO2dCQUMvQkQsY0FBY0E7Z0JBQ2Q2RSxZQUFZclUsbUVBQWVBLENBQUMsVUFBVTtnQkFDdENzVSxZQUFXO2dCQUNYQyxrQkFBaUI7Z0JBQ2pCbEIsTUFBSztnQkFDTC9ILE9BQU07Z0JBQ05sRixXQUFVOzBCQUNWLDRFQUFDa007b0JBQUlsTSxXQUFVOzhCQUNYLDRFQUFDbkgsOERBQVVBO3dCQUNQK1YsS0FBSzt3QkFDTHpQLElBQUc7d0JBQ0g4USxNQUFLO3dCQUNMOU8sT0FBTyxHQUErQixPQUE1QnBFLGlCQUFpQixZQUFXO3dCQUN0Q3dFLE9BQU90RixjQUFjNlI7d0JBQ3JCMEIsTUFBSzt3QkFDTEMsTUFBSyxXQUFXLHVDQUF1Qzs7d0JBQ3ZEUixVQUFVaEk7d0JBQ1ZxSSxZQUFXO3dCQUNYTSxZQUFXLFFBQVEsNkJBQTZCOzt3QkFDaERMLGFBQWEsR0FBK0IsT0FBNUJ4UyxpQkFBaUIsWUFBVzt3QkFDNUMyUyxlQUFlO3dCQUNmRyxXQUFXO3dCQUNYRixNQUFNblgsMkZBQUtBO3dCQUNYd0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswQkFLdEIsOERBQUNsSCw0RUFBY0E7Z0JBQ1g0VSxZQUFZNVA7Z0JBQ1o2UCxlQUFlNVA7Z0JBQ2ZvUSxrQkFBaUI7Z0JBQ2pCbk8sV0FBVTtnQkFDVmtPLFlBQVc7Z0JBQ1hELFlBQVc7Z0JBQ1hMLGNBQWMsSUFBTTdQLDZCQUE2QjtnQkFDakRxTCxjQUFjO29CQUNWckwsNkJBQTZCO29CQUM3QnBDLGNBQWM7b0JBQ2RFLFFBQVE7Z0JBQ1o7Z0JBQ0FxSixPQUFNO2dCQUNOOEIsU0FBUTtnQkFDUmtKLFFBQVE7MEJBQ1IsNEVBQUNoRTtvQkFBSWxNLFdBQVU7O3NDQUNYLDhEQUFDc087O2dDQUNJM1QsQ0FBQUEsdUJBQUFBLGtDQUFBQSxvQkFBQUEsV0FBWXlFLElBQUksY0FBaEJ6RSx3Q0FBQUEsa0JBQWtCeUcsU0FBUyxNQUM1QnpHLHVCQUFBQSxrQ0FBQUEscUJBQUFBLFdBQVl5RSxJQUFJLGNBQWhCekUseUNBQUFBLG1CQUFrQjBHLE9BQU8sSUFDbkIsR0FBc0MxRyxPQUFuQ0EsV0FBV3lFLElBQUksQ0FBQ2dDLFNBQVMsSUFBSSxJQUFHLEtBQWlDLE9BQTlCekcsV0FBV3lFLElBQUksQ0FBQ2lDLE9BQU8sSUFBSSxJQUFLQyxJQUFJLEtBQzFFO2dDQUFvQjtnQ0FBSTs7Ozs7Ozt3QkFLakMzRyx1QkFBQUEsa0NBQUFBLHFCQUFBQSxXQUFZeUUsSUFBSSxjQUFoQnpFLDBDQUFBQSxtQ0FBQUEsbUJBQWtCb08sY0FBYyxjQUFoQ3BPLHdEQUFBQSx1Q0FBQUEsaUNBQWtDNFQsSUFBSSxjQUF0QzVULDJEQUFBQSxxQ0FBd0NzRyxHQUFHLENBQ3hDLENBQUNELE1BQVdtUCx5QkFDUiw4REFBQzdCOzBDQUNHLDRFQUFDOEI7OENBQ0ksR0FBZ0NwUCxPQUE3QkEsS0FBSzJOLFlBQVksQ0FBQ3pKLEtBQUssRUFBQyxPQUF1QixPQUFsQmxFLEtBQUtzRSxNQUFNLENBQUNuRSxLQUFLOzs7Ozs7K0JBRmxEZ1A7Ozs7O3NDQVFoQiw4REFBQzdCO3NDQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFPWCw4REFBQ3hWLDRFQUFjQTtnQkFDWDRVLFlBQVkxUDtnQkFDWjJQLGVBQWUxUDtnQkFDZjJQLGNBQWNqQztnQkFDZHZDLGNBQWM7b0JBQ1ZuTCwrQkFBK0I7Z0JBQy9CLG9FQUFvRTtnQkFDeEU7Z0JBQ0FnUSxZQUFXO2dCQUNYQyxZQUFXO2dCQUNYQyxrQkFBaUI7Z0JBQ2pCbkgsU0FBUTtnQkFDUmtKLFFBQVE7Z0JBQ1JoTCxPQUFNOzBCQUNOLDRFQUFDZ0g7b0JBQUlsTSxXQUFVOzt3QkFBVTt3QkFDVzt3QkFDL0JyRixDQUFBQSx1QkFBQUEsa0NBQUFBLHFCQUFBQSxXQUFZeUUsSUFBSSxjQUFoQnpFLHlDQUFBQSxtQkFBa0J5RyxTQUFTLE1BQUl6Ryx1QkFBQUEsa0NBQUFBLHFCQUFBQSxXQUFZeUUsSUFBSSxjQUFoQnpFLHlDQUFBQSxtQkFBa0IwRyxPQUFPLElBQ25ELEdBQXNDMUcsT0FBbkNBLFdBQVd5RSxJQUFJLENBQUNnQyxTQUFTLElBQUksSUFBRyxLQUFpQyxPQUE5QnpHLFdBQVd5RSxJQUFJLENBQUNpQyxPQUFPLElBQUksSUFBS0MsSUFBSSxLQUMxRTt3QkFBb0I7d0JBQUk7Ozs7Ozs7Ozs7Ozs7O0FBTWxEO0dBcHhGd0J6SDs7UUFxQkN4Qyw0REFBZUE7UUE2QmxCRSxzREFBUUE7UUFjZmtDLDZFQUFjQTtRQXlFQ3hELHlEQUFZQTtRQVFYQSx5REFBWUE7UUFvbEJqQkEseURBQVlBO1FBb3ZCY0Msd0RBQVdBO1FBOEV2REEsd0RBQVdBO1FBVWlDQSx3REFBV0E7UUFzSFBELHlEQUFZQTtRQXFPaEJDLHdEQUFXQTs7O0tBNTREdkMyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXcvY3Jldy50c3g/MWIzNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5LCB1c2VNdXRhdGlvbiB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5pbXBvcnQge1xyXG4gICAgVXBkYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbixcclxuICAgIENyZWF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24sXHJcbiAgICBDcmVhdGVDcmV3V2VsZmFyZV9Mb2dCb29rRW50cnlTZWN0aW9uLFxyXG4gICAgRGVsZXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbnMsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvbXV0YXRpb24nXHJcbmltcG9ydCB7XHJcbiAgICBUYWJsZSxcclxuICAgIFRhYmxlQm9keSxcclxuICAgIFRhYmxlQ2VsbCxcclxuICAgIFRhYmxlSGVhZCxcclxuICAgIFRhYmxlSGVhZGVyLFxyXG4gICAgVGFibGVSb3csXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RhYmxlJ1xyXG5pbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnXHJcbmltcG9ydCB7XHJcbiAgICBDUkVXX0RVVFksXHJcbiAgICBDUkVXX0xJU1RfV0lUSE9VVF9UUkFJTklOR19TVEFUVVMgYXMgQ1JFV19MSVNULFxyXG4gICAgQ1JFV19ERVRBSUxfV0lUSF9UUkFJTklOR19TVEFUVVMgYXMgQ1JFV19ERVRBSUxfQllfSUQsXHJcbiAgICBDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uLFxyXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xyXG5pbXBvcnQgeyBMb2dCb29rRW50cnlDcmV3U2VjdGlvbiB9IGZyb20gJy4uLy4uLy4uLy4uL3R5cGVzL2xvZ2Jvb2stZW50cnktY3Jldy1zZWN0aW9uJ1xyXG5cclxuaW1wb3J0IENyZXdXZWxmYXJlIGZyb20gJy4uL2RhaWx5LWNoZWNrcy9jcmV3LXdlbGZhcmUnXHJcbmltcG9ydCB7IHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IHsgR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMgfSBmcm9tICdAL2FwcC9saWIvYWN0aW9ucydcclxuXHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2UtdG9hc3QnXHJcbmltcG9ydCB7IGZvcm1hdERCRGF0ZVRpbWUgfSBmcm9tICdAL2FwcC9oZWxwZXJzL2RhdGVIZWxwZXInXHJcbmltcG9ydCBTZWFMb2dzTWVtYmVyTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvc2VhTG9nc01lbWJlcidcclxuaW1wb3J0IENyZXdEdXR5TW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvY3Jld0R1dHknXHJcbmltcG9ydCBDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvY3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbidcclxuaW1wb3J0IHsgZ2VuZXJhdGVVbmlxdWVJZCB9IGZyb20gJ0AvYXBwL29mZmxpbmUvaGVscGVycy9mdW5jdGlvbnMnXHJcbmltcG9ydCBDcmV3V2VsZmFyZV9Mb2dCb29rRW50cnlTZWN0aW9uTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvY3Jld1dlbGZhcmVfTG9nQm9va0VudHJ5U2VjdGlvbidcclxuaW1wb3J0IHsgZ2V0UGVybWlzc2lvbnMsIGhhc1Blcm1pc3Npb24gfSBmcm9tICdAL2FwcC9oZWxwZXJzL3VzZXJIZWxwZXInXHJcbmltcG9ydCB7IGdldEZpZWxkTmFtZSB9IGZyb20gJy4uL3Zlc3NlbHMvYWN0aW9ucydcclxuaW1wb3J0IHsgU0xBTExfTG9nQm9va0ZpZWxkcyB9IGZyb20gJ0AvYXBwL2xpYi9sb2dib29rLWNvbmZpZ3VyYXRpb24nXHJcbmltcG9ydCB7IGlzRW1wdHkgfSBmcm9tICdsb2Rhc2gnXHJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xyXG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90ZXh0YXJlYSdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IHsgQ29tYm9ib3ggfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY29tYm9Cb3gnXHJcbmltcG9ydCBDcmV3RHV0eURyb3Bkb3duIGZyb20gJ0AvY29tcG9uZW50cy9maWx0ZXIvY29tcG9uZW50cy9jcmV3LWR1dHktZHJvcGRvd24nXHJcbmltcG9ydCB7IENsb2NrLCBJbmZvSWNvbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHtcclxuICAgIFBvcG92ZXIsXHJcbiAgICBQb3BvdmVyQ29udGVudCxcclxuICAgIFBvcG92ZXJUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9wb3BvdmVyJ1xyXG5pbXBvcnQgRGF0ZVBpY2tlciBmcm9tICdAL2NvbXBvbmVudHMvRGF0ZVJhbmdlJ1xyXG5pbXBvcnQgeyBBbGVydERpYWxvZ05ldyB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hbGVydC1kaWFsb2ctbmV3J1xyXG5pbXBvcnQgeyBIMiwgSDMgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdHlwb2dyYXBoeSdcclxuaW1wb3J0IHtcclxuICAgIEF2YXRhcixcclxuICAgIEF2YXRhckZhbGxiYWNrLFxyXG4gICAgQXZhdGFySW1hZ2UsXHJcbiAgICBnZXRDcmV3SW5pdGlhbHMsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2F2YXRhcidcclxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXHJcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9hcHAvbGliL3V0aWxzJ1xyXG5cclxuLy8gSW1wb3J0IHR5cGVzIGZyb20gc2VwYXJhdGUgZmlsZVxyXG5pbXBvcnQge1xyXG4gICAgQ3Jld01lbWJlcixcclxuICAgIENyZXdEdXR5LFxyXG4gICAgQ3Jld09wdGlvbixcclxuICAgIFZlc3NlbCxcclxuICAgIENyZXdQcm9wcyxcclxuICAgIGlzVmVzc2VsLFxyXG59IGZyb20gJy4vdHlwZXMnXHJcbmltcG9ydCB7IENhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcclxuaW1wb3J0IHsgRGl2SWNvbiB9IGZyb20gJ2xlYWZsZXQnXHJcbmltcG9ydCB7IHVzZUJyZWFrcG9pbnRzIH0gZnJvbSAnQC9jb21wb25lbnRzL2hvb2tzL3VzZUJyZWFrcG9pbnRzJ1xyXG5pbXBvcnQgeyBmb3JtYXREYXRlVGltZSB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvZGF0ZUhlbHBlcidcclxuaW1wb3J0IHsgRm9vdGVyV3JhcHBlciB9IGZyb20gJ0AvY29tcG9uZW50cy9mb290ZXItd3JhcHBlcidcclxuaW1wb3J0IHsgRm9ybUZvb3RlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aSdcclxuaW1wb3J0IHJlc3BvbnNpdmVMYWJlbCwge1xyXG4gICAgdXNlUmVzcG9uc2l2ZUxhYmVsLFxyXG59IGZyb20gJy4uLy4uLy4uLy4uL3V0aWxzL3Jlc3BvbnNpdmVMYWJlbCdcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENyZXcoe1xyXG4gICAgY3Jld1NlY3Rpb25zID0gZmFsc2UsXHJcbiAgICBhbGxDcmV3LFxyXG4gICAgbG9nQm9va0VudHJ5SUQsXHJcbiAgICBsb2NrZWQsXHJcbiAgICBsb2dCb29rQ29uZmlnID0gZmFsc2UsXHJcbiAgICBzZXRDcmV3TWVtYmVycyxcclxuICAgIGNyZXdXZWxmYXJlQ2hlY2ssXHJcbiAgICB1cGRhdGVDcmV3V2VsZmFyZSxcclxuICAgIHZlc3NlbCA9IGZhbHNlLFxyXG4gICAgbWFzdGVySUQgPSAwLFxyXG4gICAgbG9nRW50cnlTZWN0aW9ucyxcclxuICAgIG9mZmxpbmUgPSBmYWxzZSxcclxuICAgIGNyZXdNZW1iZXJzTGlzdCxcclxufTogQ3Jld1Byb3BzKSB7XHJcbiAgICBjb25zdCBzZWFMb2dzTWVtYmVyTW9kZWwgPSBuZXcgU2VhTG9nc01lbWJlck1vZGVsKClcclxuICAgIGNvbnN0IGNyZXdEdXR5TW9kZWwgPSBuZXcgQ3Jld0R1dHlNb2RlbCgpXHJcbiAgICBjb25zdCBsYkNyZXdNb2RlbCA9IG5ldyBDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uTW9kZWwoKVxyXG4gICAgY29uc3QgbGJXZWxmYXJlTW9kZWwgPSBuZXcgQ3Jld1dlbGZhcmVfTG9nQm9va0VudHJ5U2VjdGlvbk1vZGVsKClcclxuICAgIGNvbnN0IFthbGxWZXNzZWxDcmV3cywgc2V0QWxsVmVzc2VsQ3Jld3NdID0gdXNlU3RhdGU8Q3Jld01lbWJlcltdPihbXSlcclxuICAgIGNvbnN0IFthbGxEdXRpZXMsIHNldEFsbER1dGllc10gPSB1c2VTdGF0ZTxDcmV3RHV0eVtdPihbXSlcclxuICAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXHJcbiAgICBjb25zdCB2ZXNzZWxJRCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3Zlc3NlbElEJykgPz8gMFxyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgICBjb25zdCBbbG9hZGVkLCBzZXRMb2FkZWRdID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgICBjb25zdCBbY3Jld01lbWJlciwgc2V0Q3Jld01lbWJlcl0gPSB1c2VTdGF0ZTxDcmV3T3B0aW9uIHwgbnVsbD4obnVsbClcclxuICAgIGNvbnN0IFtkdXR5LCBzZXREdXR5XSA9IHVzZVN0YXRlPHtcclxuICAgICAgICBsYWJlbDogc3RyaW5nXHJcbiAgICAgICAgdmFsdWU6IHN0cmluZyB8IG51bWJlclxyXG4gICAgfSB8IG51bGw+KG51bGwpXHJcbiAgICBjb25zdCBbbG9naW5UaW1lLCBzZXRMb2dpblRpbWVdID0gdXNlU3RhdGUobmV3IERhdGUoKSlcclxuICAgIC8vIFN0b3JlIGxvZ291dFRpbWUgYXMgYSBzdGFuZGFyZCBEYXRlIG9iamVjdCBvciBudWxsXHJcbiAgICBjb25zdCBbbG9nb3V0VGltZSwgc2V0TG9nb3V0VGltZV0gPSB1c2VTdGF0ZTxEYXRlIHwgbnVsbD4obnVsbClcclxuICAgIGNvbnN0IFtkdXRpZXMsIHNldER1dGllc10gPSB1c2VTdGF0ZTxDcmV3RHV0eVtdPihbXSlcclxuICAgIGNvbnN0IFtjcmV3LCBzZXRDcmV3XSA9IHVzZVN0YXRlPGFueT4oY3Jld1NlY3Rpb25zKVxyXG4gICAgY29uc3QgW2NyZXdDb25maWcsIHNldENyZXdDb25maWddID0gdXNlU3RhdGU8YW55Pih0cnVlKVxyXG4gICAgLy8gRmllbGQgbGFiZWxzIGFuZCBzdGF0dXNcclxuICAgIGNvbnN0IFtwdW5jaEluU3RhdHVzLCBzZXRQdW5jaEluU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oKVxyXG4gICAgY29uc3QgW3B1bmNoSW5MYWJlbCwgc2V0UHVuY2hJbkxhYmVsXSA9IHVzZVN0YXRlPHN0cmluZz4oJ1NpZ24gSW4nKVxyXG4gICAgY29uc3QgW3B1bmNoT3V0U3RhdHVzLCBzZXRQdW5jaE91dFN0YXR1c10gPSB1c2VTdGF0ZTxzdHJpbmc+KClcclxuICAgIGNvbnN0IFtwdW5jaE91dExhYmVsLCBzZXRQdW5jaE91dExhYmVsXSA9IHVzZVN0YXRlPHN0cmluZz4oJ1NpZ24gT3V0JylcclxuICAgIGNvbnN0IFt3b3JrRGV0YWlsc1N0YXR1cywgc2V0V29ya0RldGFpbHNTdGF0dXNdID0gdXNlU3RhdGU8c3RyaW5nPigpXHJcbiAgICBjb25zdCBbd29ya0RldGFpbHNMYWJlbCwgc2V0V29ya0RldGFpbHNMYWJlbF0gPVxyXG4gICAgICAgIHVzZVN0YXRlPHN0cmluZz4oJ1dvcmsgRGV0YWlscycpXHJcbiAgICAvLyBjb25zdCBbZWRpdENyZXcsIHNldEVkaXRDcmV3XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAgIC8vIGNvbnN0IFtlZGl0Q3Jld01lbWJlciwgc2V0RWRpdENyZXdNZW1iZXJdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgICBjb25zdCBbY3Jld01hbmlmZXN0RW50cnksIHNldENyZXdNYW5pZmVzdEVudHJ5XSA9IHVzZVN0YXRlPGFueT4oe30pXHJcbiAgICBjb25zdCBbb3BlbkFkZENyZXdNZW1iZXJEaWFsb2csIHNldG9wZW5BZGRDcmV3TWVtYmVyRGlhbG9nXSA9XHJcbiAgICAgICAgdXNlU3RhdGUoZmFsc2UpXHJcblxyXG4gICAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxyXG5cclxuICAgIC8vIEZ1bmN0aW9uIHJlbW92ZWQgYXMgd2UncmUgbm93IGRpcmVjdGx5IHVzaW5nIGhhbmRsZVNhdmVcclxuICAgIGNvbnN0IFtvcGVuRWRpdExvZ291dFRpbWVEaWFsb2csIHNldE9wZW5FZGl0TG9nb3V0VGltZURpYWxvZ10gPVxyXG4gICAgICAgIHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgY29uc3QgW2NyZXdNZW1iZXJPcHRpb25zLCBzZXRDcmV3TWVtYmVyT3B0aW9uc10gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW29wZW5DcmV3VHJhaW5pbmdEdWVEaWFsb2csIHNldE9wZW5DcmV3VHJhaW5pbmdEdWVEaWFsb2ddID1cclxuICAgICAgICB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtvcGVuQ29uZmlybUNyZXdEZWxldGVEaWFsb2csIHNldE9wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZ10gPVxyXG4gICAgICAgIHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgY29uc3QgW3Blcm1pc3Npb25zLCBzZXRQZXJtaXNzaW9uc10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG4gICAgY29uc3QgW2VkaXRfbG9nQm9va0VudHJ5LCBzZXRFZGl0X2xvZ0Jvb2tFbnRyeV0gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG4gICAgY29uc3QgW2FsbE1lbWJlcnMsIHNldEFsbE1lbWJlcnNdID0gdXNlU3RhdGU8YW55PihbXSlcclxuXHJcbiAgICBjb25zdCBicCA9IHVzZUJyZWFrcG9pbnRzKClcclxuXHJcbiAgICBjb25zdCBpbml0X3Blcm1pc3Npb25zID0gKCkgPT4ge1xyXG4gICAgICAgIGlmIChcclxuICAgICAgICAgICAgcGVybWlzc2lvbnMgJiZcclxuICAgICAgICAgICAgaGFzUGVybWlzc2lvbihcclxuICAgICAgICAgICAgICAgIHByb2Nlc3MuZW52LkVESVRfTE9HQk9PS0VOVFJZIHx8ICdFRElUX0xPR0JPT0tFTlRSWScsXHJcbiAgICAgICAgICAgICAgICBwZXJtaXNzaW9ucyxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICBzZXRFZGl0X2xvZ0Jvb2tFbnRyeSh0cnVlKVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHNldEVkaXRfbG9nQm9va0VudHJ5KGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjcmVhdGVPZmZsaW5lQ3Jld1dlbGZhcmVDaGVjayA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICAvLyBJIG5lZWQgdG8gYWRkIGEgMi1zZWNvbmQgZGVsYXkgdG8gZml4IENvbnN0cmFpbnRFcnJvcjogS2V5IGFscmVhZHkgZXhpc3RzIGluIHRoZSBvYmplY3Qgc3RvcmUuXHJcbiAgICAgICAgY29uc3QgZGVsYXkgPSAobXM6IG51bWJlcikgPT5cclxuICAgICAgICAgICAgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgbXMpKVxyXG4gICAgICAgIGF3YWl0IGRlbGF5KDIwMDApXHJcbiAgICAgICAgY29uc3QgaWQgPSBnZW5lcmF0ZVVuaXF1ZUlkKClcclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgbGJXZWxmYXJlTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgIGlkOiBpZCxcclxuICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6IGxvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICBmaXRuZXNzOiBudWxsLFxyXG4gICAgICAgICAgICBpbVNhZmU6IG51bGwsXHJcbiAgICAgICAgICAgIHNhZmV0eUFjdGlvbnM6IG51bGwsXHJcbiAgICAgICAgICAgIHdhdGVyUXVhbGl0eTogbnVsbCxcclxuICAgICAgICAgICAgX190eXBlbmFtZTogJ0NyZXdXZWxmYXJlX0xvZ0Jvb2tFbnRyeVNlY3Rpb24nLFxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgdXBkYXRlQ3Jld1dlbGZhcmUoZGF0YSlcclxuICAgIH1cclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldFBlcm1pc3Npb25zKGdldFBlcm1pc3Npb25zKVxyXG4gICAgICAgIGluaXRfcGVybWlzc2lvbnMoKVxyXG4gICAgfSwgW10pXHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpbml0X3Blcm1pc3Npb25zKClcclxuICAgIH0sIFtwZXJtaXNzaW9uc10pXHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAobG9nRW50cnlTZWN0aW9ucyAmJiBBcnJheS5pc0FycmF5KGxvZ0VudHJ5U2VjdGlvbnMpKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGhhc0NyZXdXZWxmYXJlID0gbG9nRW50cnlTZWN0aW9ucy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAoc2VjdGlvbjogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIHNlY3Rpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLmNsYXNzTmFtZSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgJ1NlYUxvZ3NcXFxcQ3Jld1dlbGZhcmVfTG9nQm9va0VudHJ5U2VjdGlvbicsXHJcbiAgICAgICAgICAgICkubGVuZ3RoXHJcbiAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgIGhhc0NyZXdXZWxmYXJlID09PSAwICYmXHJcbiAgICAgICAgICAgICAgICAhY3Jld1dlbGZhcmVDaGVjayAmJlxyXG4gICAgICAgICAgICAgICAgIWxvYWRlZCAmJlxyXG4gICAgICAgICAgICAgICAgIWNyZWF0ZUNyZXdXZWxmYXJlQ2hlY2tMb2FkaW5nXHJcbiAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgc2V0TG9hZGVkKHRydWUpXHJcbiAgICAgICAgICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNyZWF0ZU9mZmxpbmVDcmV3V2VsZmFyZUNoZWNrKClcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY3JlYXRlQ3Jld1dlbGZhcmVDaGVjayh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogK2xvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2xvZ0VudHJ5U2VjdGlvbnNdKVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeUNyZXdEZXRhaWxdID0gdXNlTGF6eVF1ZXJ5KENSRVdfREVUQUlMX0JZX0lELCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge30sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignR2V0Q3Jld0RldGFpbEVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgW3F1ZXJ5VmVzc2VsQ3Jld3NdID0gdXNlTGF6eVF1ZXJ5KENSRVdfTElTVCwge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2UpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRTZWFMb2dzTWVtYmVyc1xyXG4gICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgYWxsTWVtYmVycyA9IGRhdGEubm9kZXNcclxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuICtpdGVtLmlkICE9PSArbWFzdGVySURcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIC5tYXAoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNvbnN0IGNyZXdXaXRoVHJhaW5pbmcgPSBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gICAgIFttZW1iZXJdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIClbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBgJHttZW1iZXIuZmlyc3ROYW1lIHx8ICcnfSAke21lbWJlci5zdXJuYW1lIHx8ICcnfWAudHJpbSgpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IG1lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGRhdGE6IGNyZXdXaXRoVHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9maWxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlyc3ROYW1lOiBtZW1iZXIuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1cm5hbWU6IG1lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF2YXRhcjogbWVtYmVyLnByb2ZpbGVJbWFnZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgIHNldEFsbE1lbWJlcnMoYWxsTWVtYmVycylcclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBtZW1iZXJzID0gYWxsTWVtYmVycy5maWx0ZXIoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFjcmV3U2VjdGlvbnMpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWVcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgIUFycmF5LmlzQXJyYXkoY3Jld1NlY3Rpb25zKSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAhY3Jld1NlY3Rpb25zLnNvbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc2VjdGlvbjogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3Rpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLmNyZXdNZW1iZXIgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLmNyZXdNZW1iZXIuaWQgPT09IG1lbWJlci52YWx1ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3Rpb24ucHVuY2hPdXQgPT09IG51bGwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlck9wdGlvbnMgPSBtZW1iZXJzLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICFjcmV3TWVtYmVyc0xpc3QgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgIUFycmF5LmlzQXJyYXkoY3Jld01lbWJlcnNMaXN0KSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAhY3Jld01lbWJlcnNMaXN0LmluY2x1ZGVzKCttZW1iZXIudmFsdWUpLFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXJPcHRpb25zKG1lbWJlck9wdGlvbnMpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5VmVzc2VsQ3Jld3MgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuXHJcbiAgICBjb25zdCBsb2FkVmVzc2VsQ3Jld3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHNlYUxvZ3NNZW1iZXJNb2RlbC5nZXRCeVZlc3NlbElkKHZlc3NlbElEKVxyXG4gICAgICAgICAgICBzZXRBbGxWZXNzZWxDcmV3cyhkYXRhKVxyXG4gICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgbWVtYmVycyA9IGRhdGFcclxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuICtpdGVtLmlkICE9PSArbWFzdGVySURcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIC5tYXAoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdXaXRoVHJhaW5pbmcgPSBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFttZW1iZXJdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBgJHttZW1iZXIuZmlyc3ROYW1lIHx8ICcnfSAke21lbWJlci5zdXJuYW1lIHx8ICcnfWAudHJpbSgpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IG1lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGNyZXdXaXRoVHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9maWxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlyc3ROYW1lOiBtZW1iZXIuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1cm5hbWU6IG1lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF2YXRhcjogbWVtYmVyLnByb2ZpbGVJbWFnZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9KSAvLyBmaWx0ZXIgb3V0IG1lbWJlcnMgd2hvIGFyZSBhbHJlYWR5IGluIHRoZSBjcmV3IGxpc3RcclxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWNyZXdTZWN0aW9ucykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIUFycmF5LmlzQXJyYXkoY3Jld1NlY3Rpb25zKSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIWNyZXdTZWN0aW9ucy5zb21lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChzZWN0aW9uOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3Rpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VjdGlvbi5jcmV3TWVtYmVyICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3Rpb24uY3Jld01lbWJlci5pZCA9PT0gbWVtYmVyLnZhbHVlICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3Rpb24ucHVuY2hPdXQgPT09IG51bGwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgc2V0Q3Jld01lbWJlck9wdGlvbnMobWVtYmVycylcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGF3YWl0IHF1ZXJ5VmVzc2VsQ3Jld3Moe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlaGljbGVzOiB7IGlkOiB7IGVxOiB2ZXNzZWxJRCB9IH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQXJjaGl2ZWQ6IHsgZXE6IGZhbHNlIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGlzTG9hZGluZykge1xyXG4gICAgICAgICAgICBsb2FkRHV0aWVzKClcclxuICAgICAgICAgICAgLy8gaGFuZGxlU2V0Q3Jld0NvbmZpZygpXHJcbiAgICAgICAgICAgIGxvYWRWZXNzZWxDcmV3cygpXHJcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcclxuICAgICAgICB9XHJcbiAgICB9LCBbaXNMb2FkaW5nXSlcclxuXHJcbiAgICAvLyBHcm91cCBjcmV3IGR1dGllcyBieSBjcmV3IG1lbWJlclxyXG4gICAgY29uc3QgZ3JvdXBDcmV3RHV0aWVzQnlNZW1iZXIgPSAoY3Jld0RhdGE6IGFueVtdKSA9PiB7XHJcbiAgICAgICAgaWYgKCFjcmV3RGF0YSB8fCAhQXJyYXkuaXNBcnJheShjcmV3RGF0YSkgfHwgY3Jld0RhdGEubGVuZ3RoID09PSAwKVxyXG4gICAgICAgICAgICByZXR1cm4gW11cclxuXHJcbiAgICAgICAgY29uc3QgZ3JvdXBlZENyZXc6IFJlY29yZDxzdHJpbmcsIGFueT4gPSB7fVxyXG5cclxuICAgICAgICAvLyBGaXJzdCwgc29ydCB0aGUgY3JldyBkYXRhIGJ5IHB1bmNoSW4gdGltZSB0byBlbnN1cmUgY29uc2lzdGVudCBvcmRlcmluZ1xyXG4gICAgICAgIGNvbnN0IHNvcnRlZENyZXdEYXRhID0gWy4uLmNyZXdEYXRhXS5zb3J0KChhLCBiKSA9PiB7XHJcbiAgICAgICAgICAgIGlmICghYSB8fCAhYikgcmV0dXJuIDBcclxuICAgICAgICAgICAgY29uc3QgdGltZUEgPSBhLnB1bmNoSW4gPyBuZXcgRGF0ZShhLnB1bmNoSW4pLmdldFRpbWUoKSA6IDBcclxuICAgICAgICAgICAgY29uc3QgdGltZUIgPSBiLnB1bmNoSW4gPyBuZXcgRGF0ZShiLnB1bmNoSW4pLmdldFRpbWUoKSA6IDBcclxuICAgICAgICAgICAgcmV0dXJuIHRpbWVBIC0gdGltZUIgLy8gQXNjZW5kaW5nIG9yZGVyIChvbGRlc3QgZmlyc3QpXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgLy8gRmlsdGVyIG91dCBhcmNoaXZlZCBtZW1iZXJzIGZpcnN0XHJcbiAgICAgICAgY29uc3QgYWN0aXZlQ3Jld0RhdGEgPSBzb3J0ZWRDcmV3RGF0YS5maWx0ZXIoXHJcbiAgICAgICAgICAgIChtZW1iZXIpID0+IG1lbWJlciAmJiAhbWVtYmVyLmFyY2hpdmVkLFxyXG4gICAgICAgIClcclxuXHJcbiAgICAgICAgYWN0aXZlQ3Jld0RhdGEuZm9yRWFjaCgobWVtYmVyKSA9PiB7XHJcbiAgICAgICAgICAgIGlmICghbWVtYmVyKSByZXR1cm5cclxuXHJcbiAgICAgICAgICAgIGNvbnN0IGNyZXdNZW1iZXJJZCA9IG1lbWJlci5jcmV3TWVtYmVySURcclxuICAgICAgICAgICAgaWYgKCFjcmV3TWVtYmVySWQpIHJldHVyblxyXG5cclxuICAgICAgICAgICAgLy8gSWYgdGhpcyBtZW1iZXIgYWxyZWFkeSBoYXMgZHV0aWVzIGFycmF5LCBwcmVzZXJ2ZSBpdFxyXG4gICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICBtZW1iZXIuZHV0aWVzICYmXHJcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KG1lbWJlci5kdXRpZXMpICYmXHJcbiAgICAgICAgICAgICAgICBtZW1iZXIuZHV0aWVzLmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAoIWdyb3VwZWRDcmV3W2NyZXdNZW1iZXJJZF0pIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBJbml0aWFsaXplIHdpdGggdGhlIGV4aXN0aW5nIGR1dGllc1xyXG4gICAgICAgICAgICAgICAgICAgIGdyb3VwZWRDcmV3W2NyZXdNZW1iZXJJZF0gPSB7IC4uLm1lbWJlciB9XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIE1lcmdlIGR1dGllcyBmcm9tIHRoaXMgbWVtYmVyIHdpdGggZXhpc3RpbmcgZHV0aWVzXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdEdXRpZXMgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBncm91cGVkQ3Jld1tjcmV3TWVtYmVySWRdLmR1dGllcyB8fCBbXVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZHV0aWVzLmZvckVhY2goKGR1dHk6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWR1dHkpIHJldHVyblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGR1dHkgaXMgYWxyZWFkeSBpbiB0aGUgbGlzdCAoYXZvaWQgZHVwbGljYXRlcyBieSBJRClcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNEdXBsaWNhdGVCeUlkID0gZXhpc3RpbmdEdXRpZXMuc29tZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChleGlzdGluZ0R1dHk6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0R1dHkgJiYgZXhpc3RpbmdEdXR5LmlkID09PSBkdXR5LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBPbmx5IGFkZCBpZiBpdCdzIG5vdCBhIGR1cGxpY2F0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWlzRHVwbGljYXRlQnlJZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdEdXRpZXMucHVzaChkdXR5KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgZ3JvdXBlZENyZXdbY3Jld01lbWJlcklkXSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uZ3JvdXBlZENyZXdbY3Jld01lbWJlcklkXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZHV0aWVzOiBleGlzdGluZ0R1dGllcyxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgbWVtYmVycyB3aXRob3V0IGEgZHV0aWVzIGFycmF5XHJcbiAgICAgICAgICAgICAgICBpZiAoIWdyb3VwZWRDcmV3W2NyZXdNZW1iZXJJZF0pIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBJbml0aWFsaXplIHdpdGggdGhlIGZpcnN0IGR1dHlcclxuICAgICAgICAgICAgICAgICAgICBncm91cGVkQ3Jld1tjcmV3TWVtYmVySWRdID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5tZW1iZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBtZW1iZXIuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eVBlcmZvcm1lZDogbWVtYmVyLmR1dHlQZXJmb3JtZWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogbWVtYmVyLnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hPdXQ6IG1lbWJlci5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3b3JrRGV0YWlsczogbWVtYmVyLndvcmtEZXRhaWxzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogbWVtYmVyLmR1dHlQZXJmb3JtZWRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogbWVtYmVyLmxvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgXSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgIGdyb3VwZWRDcmV3W2NyZXdNZW1iZXJJZF0uZHV0aWVzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShncm91cGVkQ3Jld1tjcmV3TWVtYmVySWRdLmR1dGllcylcclxuICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgZHV0eSBpcyBhbHJlYWR5IGluIHRoZSBsaXN0IChhdm9pZCBkdXBsaWNhdGVzIGJ5IElEKVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRHVwbGljYXRlQnlJZCA9IGdyb3VwZWRDcmV3W1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVySWRcclxuICAgICAgICAgICAgICAgICAgICBdLmR1dGllcy5zb21lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoZXhpc3RpbmdEdXR5OiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0R1dHkgJiYgZXhpc3RpbmdEdXR5LmlkID09PSBtZW1iZXIuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBBbHNvIGNoZWNrIGlmIHRoaXMgaXMgYSBkdXBsaWNhdGUgZHV0eSB0eXBlIHdpdGggdGhlIHNhbWUgdGltZSAod2hpY2ggd291bGQgYmUgcmVkdW5kYW50KVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRHVwbGljYXRlRHV0eVR5cGUgPSBncm91cGVkQ3Jld1tcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcklkXHJcbiAgICAgICAgICAgICAgICAgICAgXS5kdXRpZXMuc29tZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGV4aXN0aW5nRHV0eTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdEdXR5ICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0R1dHkuZHV0eVBlcmZvcm1lZElEID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5kdXR5UGVyZm9ybWVkSUQgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nRHV0eS5wdW5jaEluID09PSBtZW1iZXIucHVuY2hJbixcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIE9ubHkgYWRkIGlmIGl0J3Mgbm90IGEgZHVwbGljYXRlIGJ5IElEIG9yIGR1dHkgdHlwZVxyXG4gICAgICAgICAgICAgICAgICAgIGlmICghaXNEdXBsaWNhdGVCeUlkICYmICFpc0R1cGxpY2F0ZUR1dHlUeXBlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGdyb3VwZWRDcmV3W2NyZXdNZW1iZXJJZF0uZHV0aWVzLnB1c2goe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IG1lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IG1lbWJlci5kdXR5UGVyZm9ybWVkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogbWVtYmVyLnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogbWVtYmVyLnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IG1lbWJlci53b3JrRGV0YWlscyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogbWVtYmVyLmR1dHlQZXJmb3JtZWRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvZ0Jvb2tFbnRyeUlEOiBtZW1iZXIubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgLy8gU29ydCBkdXRpZXMgYnkgcHVuY2hJbiB0aW1lIGluIGFzY2VuZGluZyBvcmRlciBmb3IgZWFjaCBjcmV3IG1lbWJlclxyXG4gICAgICAgIE9iamVjdC52YWx1ZXMoZ3JvdXBlZENyZXcpLmZvckVhY2goKGNyZXdNZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICBjcmV3TWVtYmVyICYmXHJcbiAgICAgICAgICAgICAgICBjcmV3TWVtYmVyLmR1dGllcyAmJlxyXG4gICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShjcmV3TWVtYmVyLmR1dGllcykgJiZcclxuICAgICAgICAgICAgICAgIGNyZXdNZW1iZXIuZHV0aWVzLmxlbmd0aCA+IDFcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICBjcmV3TWVtYmVyLmR1dGllcy5zb3J0KChhOiBhbnksIGI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICghYSB8fCAhYikgcmV0dXJuIDBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0aW1lQSA9IGEucHVuY2hJbiA/IG5ldyBEYXRlKGEucHVuY2hJbikuZ2V0VGltZSgpIDogMFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRpbWVCID0gYi5wdW5jaEluID8gbmV3IERhdGUoYi5wdW5jaEluKS5nZXRUaW1lKCkgOiAwXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRpbWVBIC0gdGltZUIgLy8gQXNjZW5kaW5nIG9yZGVyIChvbGRlc3QgZmlyc3QpXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgcmV0dXJuIE9iamVjdC52YWx1ZXMoZ3JvdXBlZENyZXcpXHJcbiAgICB9XHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAoY3Jld1NlY3Rpb25zICYmIEFycmF5LmlzQXJyYXkoY3Jld1NlY3Rpb25zKSkge1xyXG4gICAgICAgICAgICAvLyBQcm9jZXNzIGVhY2ggY3JldyBtZW1iZXIncyB0cmFpbmluZyBzdGF0dXNcclxuICAgICAgICAgICAgY29uc3QgcHJvY2Vzc2VkQ3Jld1NlY3Rpb25zID0gY3Jld1NlY3Rpb25zLm1hcCgoc2VjdGlvbjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoc2VjdGlvbiAmJiBzZWN0aW9uLmNyZXdNZW1iZXIpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBBcHBseSBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyB0byB0aGUgY3Jld01lbWJlciBwcm9wZXJ0eVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdNZW1iZXJXaXRoVHJhaW5pbmdTdGF0dXMgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtzZWN0aW9uLmNyZXdNZW1iZXJdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClbMF1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uc2VjdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcjogY3Jld01lbWJlcldpdGhUcmFpbmluZ1N0YXR1cyxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gc2VjdGlvblxyXG4gICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgLy8gUHJlc2VydmUgZXhpc3RpbmcgZHV0aWVzIGlmIHRoZXkgZXhpc3RcclxuICAgICAgICAgICAgbGV0IHVwZGF0ZWREYXRhID0gcHJvY2Vzc2VkQ3Jld1NlY3Rpb25zXHJcbiAgICAgICAgICAgIGlmIChjcmV3ICYmIEFycmF5LmlzQXJyYXkoY3JldykgJiYgY3Jldy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBDcmVhdGUgYSBtYXAgb2YgZXhpc3RpbmcgY3JldyBtZW1iZXJzIHdpdGggdGhlaXIgZHV0aWVzXHJcbiAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ0NyZXdNYXAgPSBjcmV3LnJlZHVjZShcclxuICAgICAgICAgICAgICAgICAgICAobWFwOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LCBtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobWVtYmVyICYmIG1lbWJlci5jcmV3TWVtYmVySUQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1hcFttZW1iZXIuY3Jld01lbWJlcklEXSA9IG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBtYXBcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIHt9LFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIGFueSBleGlzdGluZyBjcmV3IG1lbWJlcnMgaGF2ZSBkdXRpZXMgdGhhdCBuZWVkIHRvIGJlIHByZXNlcnZlZFxyXG4gICAgICAgICAgICAgICAgY29uc3QgaGFzRXhpc3RpbmdEdXRpZXMgPSBPYmplY3QudmFsdWVzKGV4aXN0aW5nQ3Jld01hcCkuc29tZShcclxuICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlciAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZHV0aWVzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFycmF5LmlzQXJyYXkobWVtYmVyLmR1dGllcykgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmR1dGllcy5sZW5ndGggPiAwLFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIGlmIChoYXNFeGlzdGluZ0R1dGllcykge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBwcm9jZXNzZWQgZGF0YSB3aXRoIGV4aXN0aW5nIGR1dGllcyB3aGVyZSBhcHBsaWNhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgdXBkYXRlZERhdGEgPSBwcm9jZXNzZWRDcmV3U2VjdGlvbnMubWFwKChzZWN0aW9uOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFzZWN0aW9uIHx8ICFzZWN0aW9uLmNyZXdNZW1iZXJJRCkgcmV0dXJuIHNlY3Rpb25cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVtYmVyID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nQ3Jld01hcFtzZWN0aW9uLmNyZXdNZW1iZXJJRF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmR1dGllcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShleGlzdGluZ01lbWJlci5kdXRpZXMpICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlci5kdXRpZXMubGVuZ3RoID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgc2VjdGlvbidzIElEIGlzIGFscmVhZHkgaW4gdGhlIGV4aXN0aW5nIGR1dGllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZHV0eUV4aXN0cyA9IGV4aXN0aW5nTWVtYmVyLmR1dGllcy5zb21lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChkdXR5OiBhbnkpID0+IGR1dHkgJiYgZHV0eS5pZCA9PT0gc2VjdGlvbi5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZHV0eUV4aXN0cykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRoaXMgc2VjdGlvbiBpcyBhbHJlYWR5IGluIHRoZSBkdXRpZXMsIHNvIHJldHVybiB0aGUgc2VjdGlvbiB3aXRoIGR1dGllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnNlY3Rpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogZXhpc3RpbmdNZW1iZXIuZHV0aWVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBhIG5ldyBkdXR5IGZvciB0aGlzIGNyZXcgbWVtYmVyLCBhZGQgaXQgdG8gdGhlaXIgZHV0aWVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZER1dGllcyA9IFsuLi5leGlzdGluZ01lbWJlci5kdXRpZXNdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlZER1dGllcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHNlY3Rpb24uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IHNlY3Rpb24uZHV0eVBlcmZvcm1lZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogc2VjdGlvbi5wdW5jaEluLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogc2VjdGlvbi5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IHNlY3Rpb24ud29ya0RldGFpbHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogc2VjdGlvbi5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvZ0Jvb2tFbnRyeUlEOiBzZWN0aW9uLmxvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnNlY3Rpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogdXBkYXRlZER1dGllcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIE5vIGV4aXN0aW5nIGR1dGllcyBmb3IgdGhpcyBjcmV3IG1lbWJlciwgY3JlYXRlIGEgbmV3IGR1dGllcyBhcnJheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uc2VjdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHNlY3Rpb24uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IHNlY3Rpb24uZHV0eVBlcmZvcm1lZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogc2VjdGlvbi5wdW5jaEluLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogc2VjdGlvbi5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IHNlY3Rpb24ud29ya0RldGFpbHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogc2VjdGlvbi5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvZ0Jvb2tFbnRyeUlEOiBzZWN0aW9uLmxvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gR3JvdXAgY3JldyBkdXRpZXMgYnkgY3JldyBtZW1iZXJcclxuICAgICAgICAgICAgY29uc3QgZ3JvdXBlZENyZXdTZWN0aW9ucyA9IGdyb3VwQ3Jld0R1dGllc0J5TWVtYmVyKHVwZGF0ZWREYXRhKVxyXG4gICAgICAgICAgICBzZXRDcmV3KGdyb3VwZWRDcmV3U2VjdGlvbnMpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2NyZXdTZWN0aW9ucywgdmVzc2VsXSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChtYXN0ZXJJRCA+IDApIHtcclxuICAgICAgICAgICAgbG9hZFZlc3NlbENyZXdzKClcclxuICAgICAgICB9XHJcbiAgICB9LCBbbWFzdGVySURdKVxyXG5cclxuICAgIGNvbnN0IGxvYWREdXRpZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGNyZXdEdXR5TW9kZWwuZ2V0QWxsKClcclxuICAgICAgICAgICAgc2V0QWxsRHV0aWVzKGRhdGEpXHJcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBhY3RpdmVEdXRpZXMgPSBkYXRhLmZpbHRlcigoZHV0eTogYW55KSA9PiAhZHV0eS5hcmNoaXZlZClcclxuICAgICAgICAgICAgICAgIHNldER1dGllcyhhY3RpdmVEdXRpZXMpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBhd2FpdCBxdWVyeUR1dGllcygpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNldENyZXdDb25maWcgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICBsb2dCb29rQ29uZmlnICYmXHJcbiAgICAgICAgICAgIGxvZ0Jvb2tDb25maWcuY3VzdG9taXNlZExvZ0Jvb2tDb21wb25lbnRzICYmXHJcbiAgICAgICAgICAgIGxvZ0Jvb2tDb25maWcuY3VzdG9taXNlZExvZ0Jvb2tDb21wb25lbnRzLm5vZGVzICYmXHJcbiAgICAgICAgICAgIEFycmF5LmlzQXJyYXkobG9nQm9va0NvbmZpZy5jdXN0b21pc2VkTG9nQm9va0NvbXBvbmVudHMubm9kZXMpXHJcbiAgICAgICAgKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNyZXdNZW1iZXJzQ29uZmlncyA9XHJcbiAgICAgICAgICAgICAgICBsb2dCb29rQ29uZmlnLmN1c3RvbWlzZWRMb2dCb29rQ29tcG9uZW50cy5ub2Rlcy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgKGNvbmZpZzogYW55KSA9PiBjb25maWcgJiYgY29uZmlnLnRpdGxlID09PSAnQ3JldyBNZW1iZXJzJyxcclxuICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IGxlbmd0aCA9IGNyZXdNZW1iZXJzQ29uZmlncy5sZW5ndGhcclxuXHJcbiAgICAgICAgICAgIGlmIChsZW5ndGggPT09IDEpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNvbmZpZyA9IGNyZXdNZW1iZXJzQ29uZmlnc1swXVxyXG4gICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZyAmJlxyXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZy5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZHMubm9kZXMgJiZcclxuICAgICAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGNvbmZpZy5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzLm5vZGVzKVxyXG4gICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld0NvbmZpZyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlnLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZHMubm9kZXMubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKGZpZWxkOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IGZpZWxkLmZpZWxkTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IGZpZWxkLnN0YXR1cyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChsZW5ndGggPiAxKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBzb3J0ZWRDb25maWdzID0gWy4uLmNyZXdNZW1iZXJzQ29uZmlnc10uc29ydChcclxuICAgICAgICAgICAgICAgICAgICAoYTogYW55LCBiOiBhbnkpID0+IHBhcnNlSW50KGIuaWQpIC0gcGFyc2VJbnQoYS5pZCksXHJcbiAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgY29uZmlnID0gc29ydGVkQ29uZmlnc1swXVxyXG4gICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZyAmJlxyXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZy5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZHMubm9kZXMgJiZcclxuICAgICAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGNvbmZpZy5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzLm5vZGVzKVxyXG4gICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld0NvbmZpZyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlnLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZHMubm9kZXMubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKGZpZWxkOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IGZpZWxkLmZpZWxkTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IGZpZWxkLnN0YXR1cyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHNldENyZXdDb25maWcoZmFsc2UpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNldFN0YXR1cyA9ICgpID0+IHtcclxuICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgIEFycmF5LmlzQXJyYXkoY3Jld0NvbmZpZykgJiZcclxuICAgICAgICAgICAgY3Jld0NvbmZpZy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgIGxvZ0Jvb2tDb25maWcgJiZcclxuICAgICAgICAgICAgbG9nQm9va0NvbmZpZy5jdXN0b21pc2VkTG9nQm9va0NvbXBvbmVudHMgJiZcclxuICAgICAgICAgICAgbG9nQm9va0NvbmZpZy5jdXN0b21pc2VkTG9nQm9va0NvbXBvbmVudHMubm9kZXMgJiZcclxuICAgICAgICAgICAgQXJyYXkuaXNBcnJheShsb2dCb29rQ29uZmlnLmN1c3RvbWlzZWRMb2dCb29rQ29tcG9uZW50cy5ub2RlcylcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgY29uc3QgY3Jld01lbWJlckNvbXBvbmVudHMgPVxyXG4gICAgICAgICAgICAgICAgbG9nQm9va0NvbmZpZy5jdXN0b21pc2VkTG9nQm9va0NvbXBvbmVudHMubm9kZXMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgIChjb25maWc6IGFueSkgPT4gY29uZmlnICYmIGNvbmZpZy50aXRsZSA9PT0gJ0NyZXcgTWVtYmVycycsXHJcbiAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyQ29tcG9uZW50ID1cclxuICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJDb21wb25lbnRzLmxlbmd0aCA+IDAgPyBjcmV3TWVtYmVyQ29tcG9uZW50c1swXSA6IG51bGxcclxuXHJcbiAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJDb21wb25lbnQgJiZcclxuICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJDb21wb25lbnQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkcyAmJlxyXG4gICAgICAgICAgICAgICAgY3Jld01lbWJlckNvbXBvbmVudC5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzLm5vZGVzICYmXHJcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KFxyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJDb21wb25lbnQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkcy5ub2RlcyxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBDcmV3IE1lbWJlclxyXG4gICAgICAgICAgICAgICAgbGV0IHRpdGxlID0gJ0NyZXdNZW1iZXJJRCdcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdNZW1iZXJGaWVsZCA9XHJcbiAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlckNvbXBvbmVudC5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzLm5vZGVzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChpdGVtOiBhbnkpID0+IGl0ZW0gJiYgaXRlbS5maWVsZE5hbWUgPT09IHRpdGxlLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBXZSBhbHJlYWR5IGhhdmUgYSBkZWZhdWx0IHZhbHVlIHNldCBpbiB0aGUgdXNlU3RhdGUsIHNvIHdlIG9ubHkgbmVlZCB0byB1cGRhdGUgaWYgd2UgaGF2ZSBhIHZhbGlkIHZhbHVlXHJcbiAgICAgICAgICAgICAgICBpZiAoY3Jld01lbWJlckZpZWxkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gV2UgZG9uJ3QgbmVlZCB0byBzZXQgY3JldyBtZW1iZXIgbGFiZWwgYW55bW9yZSBhcyBpdCdzIG5vdCB1c2VkXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gS2VlcGluZyB0aGUgY29kZSBzdHJ1Y3R1cmUgZm9yIGZ1dHVyZSByZWZlcmVuY2VcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAvLyBQcmltYXJ5IER1dHlcclxuICAgICAgICAgICAgICAgIHRpdGxlID0gJ0R1dHlQZXJmb3JtZWRJRCdcclxuICAgICAgICAgICAgICAgIGNvbnN0IHByaW1hcnlEdXR5RmllbGQgPVxyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJDb21wb25lbnQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkcy5ub2Rlcy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoaXRlbTogYW55KSA9PiBpdGVtICYmIGl0ZW0uZmllbGROYW1lID09PSB0aXRsZSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gV2UgYWxyZWFkeSBoYXZlIGEgZGVmYXVsdCB2YWx1ZSBzZXQgaW4gdGhlIHVzZVN0YXRlLCBzbyB3ZSBvbmx5IG5lZWQgdG8gdXBkYXRlIGlmIHdlIGhhdmUgYSB2YWxpZCB2YWx1ZVxyXG4gICAgICAgICAgICAgICAgaWYgKHByaW1hcnlEdXR5RmllbGQpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBXZSBkb24ndCBuZWVkIHRvIHNldCBwcmltYXJ5IGR1dHkgbGFiZWwgYW55bW9yZSBhcyBpdCdzIG5vdCB1c2VkXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gS2VlcGluZyB0aGUgY29kZSBzdHJ1Y3R1cmUgZm9yIGZ1dHVyZSByZWZlcmVuY2VcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAvLyBQdW5jaCBpblxyXG4gICAgICAgICAgICAgICAgdGl0bGUgPSAnUHVuY2hJbidcclxuICAgICAgICAgICAgICAgIGNvbnN0IHB1bmNoSW5Db25maWcgPSBjcmV3Q29uZmlnLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgKGNvbmZpZzogYW55KSA9PiBjb25maWcgJiYgY29uZmlnLnRpdGxlID09PSB0aXRsZSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIHNldFB1bmNoSW5TdGF0dXMocHVuY2hJbkNvbmZpZz8uc3RhdHVzIHx8ICdPbicpXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgcHVuY2hJbkZpZWxkID1cclxuICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyQ29tcG9uZW50LmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZHMubm9kZXMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGl0ZW06IGFueSkgPT4gaXRlbSAmJiBpdGVtLmZpZWxkTmFtZSA9PT0gdGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIFdlIGFscmVhZHkgaGF2ZSBhIGRlZmF1bHQgdmFsdWUgc2V0IGluIHRoZSB1c2VTdGF0ZSwgc28gd2Ugb25seSBuZWVkIHRvIHVwZGF0ZSBpZiB3ZSBoYXZlIGEgdmFsaWQgdmFsdWVcclxuICAgICAgICAgICAgICAgIGlmIChwdW5jaEluRmllbGQpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXN0b21UaXRsZSA9IHB1bmNoSW5GaWVsZC5jdXN0b21pc2VkRmllbGRUaXRsZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkTmFtZVZhbHVlID0gZ2V0RmllbGROYW1lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwdW5jaEluRmllbGQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFNMQUxMX0xvZ0Jvb2tGaWVsZHMsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBPbmx5IHVwZGF0ZSBpZiB3ZSBoYXZlIGEgdmFsaWQgdmFsdWVcclxuICAgICAgICAgICAgICAgICAgICBpZiAoY3VzdG9tVGl0bGUgJiYgY3VzdG9tVGl0bGUudHJpbSgpICE9PSAnJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRQdW5jaEluTGFiZWwoY3VzdG9tVGl0bGUpXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChmaWVsZE5hbWVWYWx1ZSAmJiBmaWVsZE5hbWVWYWx1ZS50cmltKCkgIT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFB1bmNoSW5MYWJlbChmaWVsZE5hbWVWYWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gT3RoZXJ3aXNlIGtlZXAgdGhlIGRlZmF1bHQgJ1NpZ24gSW4nXHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gUHVuY2ggb3V0XHJcbiAgICAgICAgICAgICAgICB0aXRsZSA9ICdQdW5jaE91dCdcclxuICAgICAgICAgICAgICAgIGNvbnN0IHB1bmNoT3V0Q29uZmlnID0gY3Jld0NvbmZpZy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgIChjb25maWc6IGFueSkgPT4gY29uZmlnICYmIGNvbmZpZy50aXRsZSA9PT0gdGl0bGUsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICBzZXRQdW5jaE91dFN0YXR1cyhwdW5jaE91dENvbmZpZz8uc3RhdHVzIHx8ICdPbicpXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgcHVuY2hPdXRGaWVsZCA9XHJcbiAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlckNvbXBvbmVudC5jdXN0b21pc2VkQ29tcG9uZW50RmllbGRzLm5vZGVzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChpdGVtOiBhbnkpID0+IGl0ZW0gJiYgaXRlbS5maWVsZE5hbWUgPT09IHRpdGxlLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBXZSBhbHJlYWR5IGhhdmUgYSBkZWZhdWx0IHZhbHVlIHNldCBpbiB0aGUgdXNlU3RhdGUsIHNvIHdlIG9ubHkgbmVlZCB0byB1cGRhdGUgaWYgd2UgaGF2ZSBhIHZhbGlkIHZhbHVlXHJcbiAgICAgICAgICAgICAgICBpZiAocHVuY2hPdXRGaWVsZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGN1c3RvbVRpdGxlID0gcHVuY2hPdXRGaWVsZC5jdXN0b21pc2VkRmllbGRUaXRsZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkTmFtZVZhbHVlID0gZ2V0RmllbGROYW1lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dEZpZWxkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBTTEFMTF9Mb2dCb29rRmllbGRzLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gT25seSB1cGRhdGUgaWYgd2UgaGF2ZSBhIHZhbGlkIHZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGN1c3RvbVRpdGxlICYmIGN1c3RvbVRpdGxlLnRyaW0oKSAhPT0gJycpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0UHVuY2hPdXRMYWJlbChjdXN0b21UaXRsZSlcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpZWxkTmFtZVZhbHVlICYmIGZpZWxkTmFtZVZhbHVlLnRyaW0oKSAhPT0gJycpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0UHVuY2hPdXRMYWJlbChmaWVsZE5hbWVWYWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gT3RoZXJ3aXNlIGtlZXAgdGhlIGRlZmF1bHQgJ1NpZ24gT3V0J1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIFdvcmsgZGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgdGl0bGUgPSAnV29ya0RldGFpbHMnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB3b3JrRGV0YWlsc0NvbmZpZyA9IGNyZXdDb25maWcuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAoY29uZmlnOiBhbnkpID0+IGNvbmZpZyAmJiBjb25maWcudGl0bGUgPT09IHRpdGxlLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgc2V0V29ya0RldGFpbHNTdGF0dXMod29ya0RldGFpbHNDb25maWc/LnN0YXR1cyB8fCAnT24nKVxyXG5cclxuICAgICAgICAgICAgICAgIGNvbnN0IHdvcmtEZXRhaWxzRmllbGQgPVxyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJDb21wb25lbnQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkcy5ub2Rlcy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoaXRlbTogYW55KSA9PiBpdGVtICYmIGl0ZW0uZmllbGROYW1lID09PSB0aXRsZSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gV2UgYWxyZWFkeSBoYXZlIGEgZGVmYXVsdCB2YWx1ZSBzZXQgaW4gdGhlIHVzZVN0YXRlLCBzbyB3ZSBvbmx5IG5lZWQgdG8gdXBkYXRlIGlmIHdlIGhhdmUgYSB2YWxpZCB2YWx1ZVxyXG4gICAgICAgICAgICAgICAgaWYgKHdvcmtEZXRhaWxzRmllbGQpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXN0b21UaXRsZSA9IHdvcmtEZXRhaWxzRmllbGQuY3VzdG9taXNlZEZpZWxkVGl0bGVcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWVsZE5hbWVWYWx1ZSA9IGdldEZpZWxkTmFtZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHNGaWVsZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgU0xBTExfTG9nQm9va0ZpZWxkcyxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIE9ubHkgdXBkYXRlIGlmIHdlIGhhdmUgYSB2YWxpZCB2YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChjdXN0b21UaXRsZSAmJiBjdXN0b21UaXRsZS50cmltKCkgIT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFdvcmtEZXRhaWxzTGFiZWwoY3VzdG9tVGl0bGUpXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChmaWVsZE5hbWVWYWx1ZSAmJiBmaWVsZE5hbWVWYWx1ZS50cmltKCkgIT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFdvcmtEZXRhaWxzTGFiZWwoZmllbGROYW1lVmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIE90aGVyd2lzZSBrZWVwIHRoZSBkZWZhdWx0ICdXb3JrIERldGFpbHMnXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyBTZXQgZGVmYXVsdCB2YWx1ZXMgaWYgY3Jld0NvbmZpZyBpcyBub3QgdmFsaWRcclxuICAgICAgICAgICAgc2V0UHVuY2hJblN0YXR1cygnT24nKVxyXG4gICAgICAgICAgICBzZXRQdW5jaEluTGFiZWwoJ1NpZ24gSW4nKVxyXG4gICAgICAgICAgICBzZXRQdW5jaE91dFN0YXR1cygnT24nKVxyXG4gICAgICAgICAgICBzZXRQdW5jaE91dExhYmVsKCdTaWduIE91dCcpXHJcbiAgICAgICAgICAgIHNldFdvcmtEZXRhaWxzU3RhdHVzKCdPbicpXHJcbiAgICAgICAgICAgIHNldFdvcmtEZXRhaWxzTGFiZWwoJ1dvcmsgRGV0YWlscycpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeUR1dGllc10gPSB1c2VMYXp5UXVlcnkoQ1JFV19EVVRZLCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkQ3Jld0R1dGllcy5ub2Rlc1xyXG4gICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgYWN0aXZlRHV0aWVzID0gZGF0YS5maWx0ZXIoKGR1dHk6IGFueSkgPT4gIWR1dHkuYXJjaGl2ZWQpXHJcbiAgICAgICAgICAgICAgICBzZXREdXRpZXMoYWN0aXZlRHV0aWVzKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdxdWVyeUR1dGllc0VudHJ5IGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgaGFuZGxlTG9naW4gPSAoZGF0ZTogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKCFkYXRlKSB7XHJcbiAgICAgICAgICAgIC8vIEhhbmRsZSB0aGUgY2FzZSB3aGVuIGRhdGUgaXMgbnVsbCBvciB1bmRlZmluZWQgKHVuc2VsZWN0ZWQpXHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gbmV3IERhdGUoKVxyXG4gICAgICAgICAgICBzZXRMb2dpblRpbWUoY3VycmVudFRpbWUpXHJcbiAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHtcclxuICAgICAgICAgICAgICAgIC4uLmNyZXdNYW5pZmVzdEVudHJ5LFxyXG4gICAgICAgICAgICAgICAgcHVuY2hJbjogZm9ybWF0REJEYXRlVGltZShjdXJyZW50VGltZSksXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgLy8gRW5zdXJlIHdlIGhhdmUgYSB2YWxpZCBkYXRlIGJ5IGNyZWF0aW5nIGEgbmV3IERhdGUgb2JqZWN0XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkRGF0ZSA9IG5ldyBEYXRlKGRhdGUpXHJcblxyXG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgZGF0ZSBpcyB2YWxpZFxyXG4gICAgICAgICAgICBpZiAoaXNOYU4odmFsaWREYXRlLmdldFRpbWUoKSkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ludmFsaWQgZGF0ZSBwcm92aWRlZCB0byBoYW5kbGVMb2dpbjonLCBkYXRlKVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHNldExvZ2luVGltZSh2YWxpZERhdGUpXHJcbiAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHtcclxuICAgICAgICAgICAgICAgIC4uLmNyZXdNYW5pZmVzdEVudHJ5LFxyXG4gICAgICAgICAgICAgICAgcHVuY2hJbjogZm9ybWF0REJEYXRlVGltZSh2YWxpZERhdGUpLFxyXG4gICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgLy8gSWYgbG9nb3V0IHRpbWUgaXMgc2V0IGFuZCBpcyBiZWZvcmUgdGhlIG5ldyBsb2dpbiB0aW1lLCByZXNldCBpdFxyXG4gICAgICAgICAgICBpZiAobG9nb3V0VGltZSAmJiB2YWxpZERhdGUuZ2V0VGltZSgpID4gbG9nb3V0VGltZS5nZXRUaW1lKCkpIHtcclxuICAgICAgICAgICAgICAgIHNldExvZ291dFRpbWUobnVsbClcclxuICAgICAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KChwcmV2OiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogbnVsbCxcclxuICAgICAgICAgICAgICAgIH0pKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gaGFuZGxlTG9naW46JywgZXJyb3IpXHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICdBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBzZXR0aW5nIHRoZSBzaWduIGluIHRpbWUnLFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlTG9nb3V0ID0gKGRhdGU6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmICghZGF0ZSkge1xyXG4gICAgICAgICAgICAvLyBIYW5kbGUgdGhlIGNhc2Ugd2hlbiBkYXRlIGlzIG51bGwgb3IgdW5kZWZpbmVkICh1bnNlbGVjdGVkKVxyXG4gICAgICAgICAgICBzZXRMb2dvdXRUaW1lKG51bGwpXHJcbiAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHtcclxuICAgICAgICAgICAgICAgIC4uLmNyZXdNYW5pZmVzdEVudHJ5LFxyXG4gICAgICAgICAgICAgICAgcHVuY2hPdXQ6IG51bGwsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgLy8gRW5zdXJlIHdlIGhhdmUgYSB2YWxpZCBkYXRlIGJ5IGNyZWF0aW5nIGEgbmV3IERhdGUgb2JqZWN0XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkRGF0ZSA9IG5ldyBEYXRlKGRhdGUpXHJcblxyXG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgZGF0ZSBpcyB2YWxpZFxyXG4gICAgICAgICAgICBpZiAoaXNOYU4odmFsaWREYXRlLmdldFRpbWUoKSkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ludmFsaWQgZGF0ZSBwcm92aWRlZCB0byBoYW5kbGVMb2dvdXQ6JywgZGF0ZSlcclxuICAgICAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBJZiB0aGUgZGF0ZSBkb2Vzbid0IGhhdmUgdGltZSBzZXQgKGhvdXJzIGFuZCBtaW51dGVzIGFyZSAwKSxcclxuICAgICAgICAgICAgLy8gc2V0IHRoZSBjdXJyZW50IHRpbWVcclxuICAgICAgICAgICAgaWYgKHZhbGlkRGF0ZS5nZXRIb3VycygpID09PSAwICYmIHZhbGlkRGF0ZS5nZXRNaW51dGVzKCkgPT09IDApIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcclxuICAgICAgICAgICAgICAgIHZhbGlkRGF0ZS5zZXRIb3Vycyhub3cuZ2V0SG91cnMoKSlcclxuICAgICAgICAgICAgICAgIHZhbGlkRGF0ZS5zZXRNaW51dGVzKG5vdy5nZXRNaW51dGVzKCkpXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIENvbnZlcnQgdG8gZGF5anMgZm9yIGVhc2llciBjb21wYXJpc29uXHJcbiAgICAgICAgICAgIGNvbnN0IGRheWpzRGF0ZSA9IGRheWpzKHZhbGlkRGF0ZSlcclxuXHJcbiAgICAgICAgICAgIC8vIEVuc3VyZSBsb2dvdXQgdGltZSBpcyBhZnRlciBsb2dpbiB0aW1lXHJcbiAgICAgICAgICAgIGlmIChsb2dpblRpbWUgJiYgZGF5anNEYXRlLmlzQmVmb3JlKGxvZ2luVGltZSkpIHtcclxuICAgICAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ0ludmFsaWQgdGltZScsXHJcbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICdTaWduIG91dCB0aW1lIG11c3QgYmUgYWZ0ZXIgc2lnbiBpbiB0aW1lJyxcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBTdG9yZSB0aGUgZGF0ZSBhcyBhIHN0YW5kYXJkIERhdGUgb2JqZWN0IHRvIGF2b2lkIGFueSBpc3N1ZXMgd2l0aCBkYXlqc1xyXG4gICAgICAgICAgICBzZXRMb2dvdXRUaW1lKHZhbGlkRGF0ZSlcclxuXHJcbiAgICAgICAgICAgIC8vIFVwZGF0ZSBjcmV3IG1hbmlmZXN0IGVudHJ5IHdpdGggZm9ybWF0dGVkIGRhdGUgc3RyaW5nXHJcbiAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHtcclxuICAgICAgICAgICAgICAgIC4uLmNyZXdNYW5pZmVzdEVudHJ5LFxyXG4gICAgICAgICAgICAgICAgcHVuY2hPdXQ6IGZvcm1hdERCRGF0ZVRpbWUodmFsaWREYXRlKSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBoYW5kbGVMb2dvdXQ6JywgZXJyb3IpXHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246XHJcbiAgICAgICAgICAgICAgICAgICAgJ0FuIGVycm9yIG9jY3VycmVkIHdoaWxlIHNldHRpbmcgdGhlIHNpZ24gb3V0IHRpbWUnLFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlQWRkTWFuaWZlc3QgPSAoKSA9PiB7XHJcbiAgICAgICAgLy8gQ2hlY2sgcGVybWlzc2lvbnMgZmlyc3RcclxuICAgICAgICBpZiAoIWVkaXRfbG9nQm9va0VudHJ5KSB7XHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246XHJcbiAgICAgICAgICAgICAgICAgICAgJ1lvdSBkbyBub3QgaGF2ZSBwZXJtaXNzaW9uIHRvIGVkaXQgdGhpcyBsb2cgZW50cnknLFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBGaWx0ZXIgY3JldyBtZW1iZXIgb3B0aW9ucyB0byBvbmx5IHNob3cgYXZhaWxhYmxlIGNyZXcgbWVtYmVyc1xyXG4gICAgICAgIGlmIChhbGxNZW1iZXJzICYmIEFycmF5LmlzQXJyYXkoYWxsTWVtYmVycykpIHtcclxuICAgICAgICAgICAgLy8gR2V0IGEgbGlzdCBvZiBjcmV3IG1lbWJlcnMgd2hvIGFyZSBhbHJlYWR5IHNpZ25lZCBpbiAod2l0aG91dCBzaWduLW91dCB0aW1lKVxyXG4gICAgICAgICAgICBjb25zdCBzaWduZWRJbkNyZXdNZW1iZXJJRHMgPSBuZXcgU2V0KClcclxuXHJcbiAgICAgICAgICAgIGlmIChjcmV3ICYmIEFycmF5LmlzQXJyYXkoY3JldykpIHtcclxuICAgICAgICAgICAgICAgIGNyZXcuZm9yRWFjaCgobWVtYmVyOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBPbmx5IGNvbnNpZGVyIG1lbWJlcnMgd2hvIGFyZSBzaWduZWQgaW4gd2l0aG91dCBhIHNpZ24tb3V0IHRpbWVcclxuICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlciAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZHV0aWVzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFycmF5LmlzQXJyYXkobWVtYmVyLmR1dGllcylcclxuICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgYW55IGR1dHkgaGFzIG5vIHB1bmNoIG91dCB0aW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc0FjdGl2ZVNoaWZ0ID0gbWVtYmVyLmR1dGllcy5zb21lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKGR1dHk6IGFueSkgPT4gZHV0eSAmJiBkdXR5LnB1bmNoT3V0ID09PSBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChoYXNBY3RpdmVTaGlmdCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnbmVkSW5DcmV3TWVtYmVySURzLmFkZChtZW1iZXIuY3Jld01lbWJlcklEKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChtZW1iZXIgJiYgbWVtYmVyLnB1bmNoT3V0ID09PSBudWxsKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25lZEluQ3Jld01lbWJlcklEcy5hZGQobWVtYmVyLmNyZXdNZW1iZXJJRClcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBGaWx0ZXIgb3V0IGNyZXcgbWVtYmVycyB3aG8gYXJlIGFscmVhZHkgc2lnbmVkIGluXHJcbiAgICAgICAgICAgIGNvbnN0IGF2YWlsYWJsZUNyZXdNZW1iZXJzID0gYWxsTWVtYmVycy5maWx0ZXIoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoIW1lbWJlcikgcmV0dXJuIGZhbHNlXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gIXNpZ25lZEluQ3Jld01lbWJlcklEcy5oYXMobWVtYmVyLnZhbHVlKVxyXG4gICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgLy8gRnVydGhlciBmaWx0ZXIgb3V0IGNyZXcgbWVtYmVycyB3aG8gYXJlIGluIHRoZSBjcmV3TWVtYmVyc0xpc3QgKGlmIGFwcGxpY2FibGUpXHJcbiAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkQ3Jld09wdGlvbnMgPSBhdmFpbGFibGVDcmV3TWVtYmVycy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgIW1lbWJlciB8fFxyXG4gICAgICAgICAgICAgICAgICAgICFjcmV3TWVtYmVyc0xpc3QgfHxcclxuICAgICAgICAgICAgICAgICAgICAhQXJyYXkuaXNBcnJheShjcmV3TWVtYmVyc0xpc3QpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgIWNyZXdNZW1iZXJzTGlzdC5pbmNsdWRlcygrbWVtYmVyLnZhbHVlKSxcclxuICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgc2V0Q3Jld01lbWJlck9wdGlvbnMoZmlsdGVyZWRDcmV3T3B0aW9ucylcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyBJZiBhbGxNZW1iZXJzIGlzIG5vdCB2YWxpZCwganVzdCBwcm9jZWVkIHdpdGggZW1wdHkgb3B0aW9uc1xyXG4gICAgICAgICAgICBzZXRDcmV3TWVtYmVyT3B0aW9ucyhbXSlcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFNldCB1cCB0aGUgbmV3IGNyZXcgbWFuaWZlc3QgZW50cnkgd2l0aCBjdXJyZW50IHRpbWVcclxuICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IG5ldyBEYXRlKClcclxuICAgICAgICBjb25zdCBjcmV3TWFuaWZlc3RFbnRyeTogYW55ID0ge1xyXG4gICAgICAgICAgICBpZDogMCxcclxuICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6ICtsb2dCb29rRW50cnlJRCxcclxuICAgICAgICAgICAgY3Jld01lbWJlcklEOiAwLFxyXG4gICAgICAgICAgICBkdXR5UGVyZm9ybWVkSUQ6IDAsXHJcbiAgICAgICAgICAgIHB1bmNoSW46IGZvcm1hdERCRGF0ZVRpbWUoY3VycmVudFRpbWUpLFxyXG4gICAgICAgICAgICBwdW5jaE91dDogbnVsbCxcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KGNyZXdNYW5pZmVzdEVudHJ5KVxyXG4gICAgICAgIHNldExvZ2luVGltZShjdXJyZW50VGltZSlcclxuICAgICAgICBzZXRMb2dvdXRUaW1lKG51bGwpXHJcbiAgICAgICAgc2V0Q3Jld01lbWJlcihudWxsKVxyXG4gICAgICAgIHNldER1dHkobnVsbClcclxuICAgICAgICBzZXRvcGVuQWRkQ3Jld01lbWJlckRpYWxvZyh0cnVlKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZUVkaXRNYW5pZmVzdCA9IChtZW1iZXJEYXRhOiBhbnkpID0+IHtcclxuICAgICAgICBpZiAoIWVkaXRfbG9nQm9va0VudHJ5KSB7XHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246XHJcbiAgICAgICAgICAgICAgICAgICAgJ1lvdSBkbyBub3QgaGF2ZSBwZXJtaXNzaW9uIHRvIGVkaXQgdGhpcyBsb2cgZW50cnknLFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBJZiB0aGlzIGlzIGEgZ3JvdXBlZCBjcmV3IG1lbWJlciB3aXRoIG11bHRpcGxlIGR1dGllcywgdXNlIHRoZSBmaXJzdCBkdXR5XHJcbiAgICAgICAgY29uc3QgZHV0eVRvRWRpdCA9XHJcbiAgICAgICAgICAgIG1lbWJlckRhdGEuZHV0aWVzICYmIG1lbWJlckRhdGEuZHV0aWVzLmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgID8gbWVtYmVyRGF0YS5kdXRpZXNbMF1cclxuICAgICAgICAgICAgICAgIDogbWVtYmVyRGF0YVxyXG5cclxuICAgICAgICBzZXRDcmV3TWFuaWZlc3RFbnRyeSh7XHJcbiAgICAgICAgICAgIGlkOiBkdXR5VG9FZGl0Py5pZCxcclxuICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6IGR1dHlUb0VkaXQ/LmxvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICBjcmV3TWVtYmVySUQ6IG1lbWJlckRhdGE/LmNyZXdNZW1iZXJJRCxcclxuICAgICAgICAgICAgZHV0eVBlcmZvcm1lZElEOiBkdXR5VG9FZGl0Py5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgIHB1bmNoSW46IGR1dHlUb0VkaXQ/LnB1bmNoSW4sXHJcbiAgICAgICAgICAgIHB1bmNoT3V0OiBkdXR5VG9FZGl0Py5wdW5jaE91dCxcclxuICAgICAgICAgICAgd29ya0RldGFpbHM6IGR1dHlUb0VkaXQ/LndvcmtEZXRhaWxzLFxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIC8vIENyZWF0ZSBhIHByb3BlciBjcmV3IG1lbWJlciBvYmplY3Qgd2l0aCBwcm9maWxlIGRldGFpbHNcclxuICAgICAgICBjb25zdCBjcmV3TWVtYmVyV2l0aFByb2ZpbGUgPSB7XHJcbiAgICAgICAgICAgIGxhYmVsOiBgJHttZW1iZXJEYXRhLmNyZXdNZW1iZXIuZmlyc3ROYW1lIHx8ICcnfSAke21lbWJlckRhdGEuY3Jld01lbWJlci5zdXJuYW1lICE9PSBudWxsID8gbWVtYmVyRGF0YS5jcmV3TWVtYmVyLnN1cm5hbWUgOiAnJ31gLnRyaW0oKSxcclxuICAgICAgICAgICAgdmFsdWU6IG1lbWJlckRhdGEuY3Jld01lbWJlci5pZCxcclxuICAgICAgICAgICAgZGF0YTogbWVtYmVyRGF0YS5jcmV3TWVtYmVyLFxyXG4gICAgICAgICAgICBwcm9maWxlOiB7XHJcbiAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IG1lbWJlckRhdGEuY3Jld01lbWJlci5maXJzdE5hbWUsXHJcbiAgICAgICAgICAgICAgICBzdXJuYW1lOiBtZW1iZXJEYXRhLmNyZXdNZW1iZXIuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgIGF2YXRhcjogbWVtYmVyRGF0YS5jcmV3TWVtYmVyLnByb2ZpbGVJbWFnZSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHNldENyZXdNZW1iZXIoY3Jld01lbWJlcldpdGhQcm9maWxlKVxyXG5cclxuICAgICAgICAvLyBGaW5kIHRoZSBjb3JyZWN0IGR1dHkgaW4gdGhlIGR1dGllcyBhcnJheVxyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkRHV0eSA9IGR1dGllcy5maW5kKFxyXG4gICAgICAgICAgICAobWVtYmVyRHV0eTogYW55KSA9PiBtZW1iZXJEdXR5LmlkID09PSBkdXR5VG9FZGl0Py5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgKVxyXG5cclxuICAgICAgICBpZiAoc2VsZWN0ZWREdXR5KSB7XHJcbiAgICAgICAgICAgIHNldER1dHkoe1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6IHNlbGVjdGVkRHV0eS50aXRsZSxcclxuICAgICAgICAgICAgICAgIHZhbHVlOiBzZWxlY3RlZER1dHkuaWQsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRMb2dpblRpbWUoXHJcbiAgICAgICAgICAgIGR1dHlUb0VkaXQ/LnB1bmNoSW4gPyBuZXcgRGF0ZShkdXR5VG9FZGl0LnB1bmNoSW4pIDogbmV3IERhdGUoKSxcclxuICAgICAgICApXHJcbiAgICAgICAgc2V0TG9nb3V0VGltZShcclxuICAgICAgICAgICAgZHV0eVRvRWRpdD8ucHVuY2hPdXQgPyBuZXcgRGF0ZShkdXR5VG9FZGl0LnB1bmNoT3V0KSA6IG51bGwsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHNldG9wZW5BZGRDcmV3TWVtYmVyRGlhbG9nKHRydWUpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlU2lnbk91dFRpbWUgPSAobWVtYmVyRGF0YTogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKCFlZGl0X2xvZ0Jvb2tFbnRyeSkge1xyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOlxyXG4gICAgICAgICAgICAgICAgICAgICdZb3UgZG8gbm90IGhhdmUgcGVybWlzc2lvbiB0byBlZGl0IHRoaXMgbG9nIGVudHJ5JyxcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gRGV0ZXJtaW5lIGlmIHRoaXMgaXMgYSBuZXN0ZWQgZHV0eSBvciBhIG1haW4gY3JldyBtZW1iZXJcclxuICAgICAgICBjb25zdCBpc05lc3RlZER1dHkgPSAhbWVtYmVyRGF0YS5jcmV3TWVtYmVyXHJcblxyXG4gICAgICAgIGlmIChpc05lc3RlZER1dHkpIHtcclxuICAgICAgICAgICAgLy8gVGhpcyBpcyBhIG5lc3RlZCBkdXR5XHJcbiAgICAgICAgICAgIC8vIEZpbmQgdGhlIHBhcmVudCBjcmV3IG1lbWJlciBmb3IgdGhpcyBkdXR5XHJcbiAgICAgICAgICAgIGNvbnN0IHBhcmVudE1lbWJlciA9XHJcbiAgICAgICAgICAgICAgICBjcmV3ICYmIEFycmF5LmlzQXJyYXkoY3JldylcclxuICAgICAgICAgICAgICAgICAgICA/IGNyZXcuZmluZCgoYzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjLmR1dGllcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGMuZHV0aWVzKSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjLmR1dGllcy5zb21lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGQ6IGFueSkgPT4gZCAmJiBkLmlkID09PSBtZW1iZXJEYXRhLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICA6IG51bGxcclxuXHJcbiAgICAgICAgICAgIGlmIChwYXJlbnRNZW1iZXIpIHtcclxuICAgICAgICAgICAgICAgIC8vIFNldCBjcmV3IG1hbmlmZXN0IGVudHJ5IHdpdGggdGhlIHBhcmVudCBjcmV3IG1lbWJlciBJRFxyXG4gICAgICAgICAgICAgICAgc2V0Q3Jld01hbmlmZXN0RW50cnkoe1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiBtZW1iZXJEYXRhPy5pZCxcclxuICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogbWVtYmVyRGF0YT8ubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcklEOiBwYXJlbnRNZW1iZXIuY3Jld01lbWJlcklELCAvLyBBZGQgdGhlIHBhcmVudCBjcmV3IG1lbWJlciBJRFxyXG4gICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogbWVtYmVyRGF0YT8uZHV0eVBlcmZvcm1lZD8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogbWVtYmVyRGF0YT8ucHVuY2hJbixcclxuICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogbWVtYmVyRGF0YT8ucHVuY2hPdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IG1lbWJlckRhdGE/LndvcmtEZXRhaWxzLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBDcmVhdGUgYSBwcm9wZXIgY3JldyBtZW1iZXIgb2JqZWN0IHdpdGggcHJvZmlsZSBkZXRhaWxzXHJcbiAgICAgICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyV2l0aFByb2ZpbGUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGAke3BhcmVudE1lbWJlci5jcmV3TWVtYmVyLmZpcnN0TmFtZSB8fCAnJ30gJHtwYXJlbnRNZW1iZXIuY3Jld01lbWJlci5zdXJuYW1lICE9PSBudWxsID8gcGFyZW50TWVtYmVyLmNyZXdNZW1iZXIuc3VybmFtZSA6ICcnfWAudHJpbSgpLFxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBwYXJlbnRNZW1iZXIuY3Jld01lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICBkYXRhOiBwYXJlbnRNZW1iZXIuY3Jld01lbWJlcixcclxuICAgICAgICAgICAgICAgICAgICBwcm9maWxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpcnN0TmFtZTogcGFyZW50TWVtYmVyLmNyZXdNZW1iZXIuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdXJuYW1lOiBwYXJlbnRNZW1iZXIuY3Jld01lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhdmF0YXI6IHBhcmVudE1lbWJlci5jcmV3TWVtYmVyLnByb2ZpbGVJbWFnZSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXIoY3Jld01lbWJlcldpdGhQcm9maWxlKVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIFNldCBkdXR5XHJcbiAgICAgICAgICAgICAgICBpZiAobWVtYmVyRGF0YS5kdXR5UGVyZm9ybWVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWREdXR5ID0gZHV0aWVzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChtZW1iZXJEdXR5OiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJEdXR5LmlkID09PSBtZW1iZXJEYXRhPy5kdXR5UGVyZm9ybWVkPy5pZCxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGlmIChzZWxlY3RlZER1dHkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0RHV0eSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogc2VsZWN0ZWREdXR5LnRpdGxlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHNlbGVjdGVkRHV0eS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAvLyBJZiBwYXJlbnQgbWVtYmVyIG5vdCBmb3VuZCwgc2hvdyBhbiBlcnJvclxyXG4gICAgICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQ291bGQgbm90IGZpbmQgdGhlIGFzc29jaWF0ZWQgY3JldyBtZW1iZXInLFxyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyBUaGlzIGlzIGEgbWFpbiBjcmV3IG1lbWJlclxyXG4gICAgICAgICAgICBzZXRDcmV3TWFuaWZlc3RFbnRyeSh7XHJcbiAgICAgICAgICAgICAgICBpZDogbWVtYmVyRGF0YT8uaWQsXHJcbiAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogbWVtYmVyRGF0YT8ubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICBjcmV3TWVtYmVySUQ6IG1lbWJlckRhdGE/LmNyZXdNZW1iZXJJRCxcclxuICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogbWVtYmVyRGF0YT8uZHV0eVBlcmZvcm1lZD8uaWQsXHJcbiAgICAgICAgICAgICAgICBwdW5jaEluOiBtZW1iZXJEYXRhPy5wdW5jaEluLFxyXG4gICAgICAgICAgICAgICAgcHVuY2hPdXQ6IG1lbWJlckRhdGE/LnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IG1lbWJlckRhdGE/LndvcmtEZXRhaWxzLFxyXG4gICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgLy8gQ3JlYXRlIGEgcHJvcGVyIGNyZXcgbWVtYmVyIG9iamVjdCB3aXRoIHByb2ZpbGUgZGV0YWlsc1xyXG4gICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyV2l0aFByb2ZpbGUgPSB7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogYCR7bWVtYmVyRGF0YS5jcmV3TWVtYmVyLmZpcnN0TmFtZSB8fCAnJ30gJHttZW1iZXJEYXRhLmNyZXdNZW1iZXIuc3VybmFtZSAhPT0gbnVsbCA/IG1lbWJlckRhdGEuY3Jld01lbWJlci5zdXJuYW1lIDogJyd9YC50cmltKCksXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogbWVtYmVyRGF0YS5jcmV3TWVtYmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgZGF0YTogbWVtYmVyRGF0YS5jcmV3TWVtYmVyLFxyXG4gICAgICAgICAgICAgICAgcHJvZmlsZToge1xyXG4gICAgICAgICAgICAgICAgICAgIGZpcnN0TmFtZTogbWVtYmVyRGF0YS5jcmV3TWVtYmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICBzdXJuYW1lOiBtZW1iZXJEYXRhLmNyZXdNZW1iZXIuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICBhdmF0YXI6IG1lbWJlckRhdGEuY3Jld01lbWJlci5wcm9maWxlSW1hZ2UsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBzZXRDcmV3TWVtYmVyKGNyZXdNZW1iZXJXaXRoUHJvZmlsZSlcclxuXHJcbiAgICAgICAgICAgIC8vIFNldCBkdXR5XHJcbiAgICAgICAgICAgIGlmIChtZW1iZXJEYXRhLmR1dHlQZXJmb3JtZWQpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkRHV0eSA9IGR1dGllcy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgIChtZW1iZXJEdXR5OiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlckR1dHkuaWQgPT09IG1lbWJlckRhdGE/LmR1dHlQZXJmb3JtZWQ/LmlkLFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIGlmIChzZWxlY3RlZER1dHkpIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXREdXR5KHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IHNlbGVjdGVkRHV0eS50aXRsZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHNlbGVjdGVkRHV0eS5pZCxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBTZXQgdGltZXNcclxuICAgICAgICBzZXRMb2dpblRpbWUoXHJcbiAgICAgICAgICAgIG1lbWJlckRhdGEucHVuY2hJbiA/IG5ldyBEYXRlKG1lbWJlckRhdGEucHVuY2hJbikgOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIClcclxuICAgICAgICBzZXRMb2dvdXRUaW1lKFxyXG4gICAgICAgICAgICBtZW1iZXJEYXRhLnB1bmNoT3V0ID8gbmV3IERhdGUobWVtYmVyRGF0YS5wdW5jaE91dCkgOiBudWxsLFxyXG4gICAgICAgIClcclxuXHJcbiAgICAgICAgc2V0T3BlbkVkaXRMb2dvdXRUaW1lRGlhbG9nKHRydWUpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlQ3Jld01lbWJlciA9IGFzeW5jIChzZWxlY3RlZDogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKCFzZWxlY3RlZCkgcmV0dXJuXHJcbiAgICAgICAgc2V0RHV0eSh7XHJcbiAgICAgICAgICAgIGxhYmVsOiAnLS0gU2VsZWN0IER1dHkgLS0nLFxyXG4gICAgICAgICAgICB2YWx1ZTogMCxcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICBjb25zdCB7IGRhdGEgfSA9IGF3YWl0IHF1ZXJ5Q3Jld0RldGFpbCh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgY3Jld01lbWJlcklEOiBzZWxlY3RlZC52YWx1ZSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICBjb25zdCBtZW1iZXIgPSBkYXRhLnJlYWRPbmVTZWFMb2dzTWVtYmVyXHJcbiAgICAgICAgY29uc3QgY3Jld1dpdGhUcmFpbmluZyA9IEdldENyZXdMaXN0V2l0aFRyYWluaW5nU3RhdHVzKFxyXG4gICAgICAgICAgICBbbWVtYmVyXSxcclxuICAgICAgICAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgKVswXVxyXG5cclxuICAgICAgICBjb25zdCB2YWx1ZSA9IHtcclxuICAgICAgICAgICAgLi4uc2VsZWN0ZWQsXHJcbiAgICAgICAgICAgIGRhdGE6IGNyZXdXaXRoVHJhaW5pbmcsXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRDcmV3TWVtYmVyKHZhbHVlKVxyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB0aGUgY3JldyBoYXMgYSB0cmFpbmluZyBkdWVcclxuICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgIHZhbHVlLmRhdGEgJiZcclxuICAgICAgICAgICAgdmFsdWUuZGF0YS50cmFpbmluZ1N0YXR1cyAmJlxyXG4gICAgICAgICAgICB2YWx1ZS5kYXRhLnRyYWluaW5nU3RhdHVzLmxhYmVsICE9PSAnR29vZCdcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgc2V0T3BlbkNyZXdUcmFpbmluZ0R1ZURpYWxvZyh0cnVlKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gU2V0IGRlZmF1bHQgZHV0eVxyXG4gICAgICAgIGlmIChhbGxDcmV3ICYmIEFycmF5LmlzQXJyYXkoYWxsQ3JldykpIHtcclxuICAgICAgICAgICAgY29uc3QgY3Jld01lbWJlciA9IGFsbENyZXcuZmluZChcclxuICAgICAgICAgICAgICAgIChtZW1iZXI6IGFueSkgPT4gbWVtYmVyICYmIG1lbWJlci5pZCA9PT0gdmFsdWUudmFsdWUsXHJcbiAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgIGlmIChjcmV3TWVtYmVyICYmIGNyZXdNZW1iZXIucHJpbWFyeUR1dHlJRCkge1xyXG4gICAgICAgICAgICAgICAgaWYgKGR1dGllcyAmJiBBcnJheS5pc0FycmF5KGR1dGllcykpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3RHV0eSA9IGR1dGllcy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoZDogYW55KSA9PiBkICYmIGQuaWQgPT09IGNyZXdNZW1iZXIucHJpbWFyeUR1dHlJRCxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNyZXdEdXR5KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0R1dHkgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogY3Jld0R1dHkudGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogY3Jld0R1dHkuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0RHV0eShuZXdEdXR5KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDcmV3TWFuaWZlc3RFbnRyeSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5jcmV3TWFuaWZlc3RFbnRyeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJJRDogY3Jld01lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogY3Jld0R1dHkuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld01hbmlmZXN0RW50cnkoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uY3Jld01hbmlmZXN0RW50cnksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVySUQ6IGNyZXdNZW1iZXIuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3TWFuaWZlc3RFbnRyeSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmNyZXdNYW5pZmVzdEVudHJ5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVySUQ6IGNyZXdNZW1iZXIuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChjcmV3TWVtYmVyKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRDcmV3TWFuaWZlc3RFbnRyeSh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uY3Jld01hbmlmZXN0RW50cnksXHJcbiAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcklEOiBjcmV3TWVtYmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVEdXR5ID0gKHZhbHVlOiBhbnkpID0+IHtcclxuICAgICAgICBzZXREdXR5KHZhbHVlKVxyXG4gICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHtcclxuICAgICAgICAgICAgLi4uY3Jld01hbmlmZXN0RW50cnksXHJcbiAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogdmFsdWU/LnZhbHVlIHx8IDAsXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVDYW5jZWwgPSAoKSA9PiB7XHJcbiAgICAgICAgc2V0Q3Jld01hbmlmZXN0RW50cnkoe30gYXMgTG9nQm9va0VudHJ5Q3Jld1NlY3Rpb24pXHJcbiAgICAgICAgc2V0Q3Jld01lbWJlcihudWxsKVxyXG4gICAgICAgIHNldER1dHkobnVsbClcclxuICAgICAgICBzZXRMb2dpblRpbWUobmV3IERhdGUoKSlcclxuICAgICAgICBzZXRMb2dvdXRUaW1lKG51bGwpXHJcbiAgICAgICAgc2V0b3BlbkFkZENyZXdNZW1iZXJEaWFsb2coZmFsc2UpXHJcbiAgICAgICAgc2V0T3BlbkVkaXRMb2dvdXRUaW1lRGlhbG9nKGZhbHNlKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoY2FsbEJ5Pzogc3RyaW5nKSA9PiB7XHJcbiAgICAgICAgLy8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAhY3Jld01hbmlmZXN0RW50cnkuY3Jld01lbWJlcklEIHx8XHJcbiAgICAgICAgICAgIGNyZXdNYW5pZmVzdEVudHJ5LmNyZXdNZW1iZXJJRCA9PT0gMFxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnUGxlYXNlIHNlbGVjdCBhIGNyZXcgbWVtYmVyJyxcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAhY3Jld01hbmlmZXN0RW50cnkuZHV0eVBlcmZvcm1lZElEIHx8XHJcbiAgICAgICAgICAgIGNyZXdNYW5pZmVzdEVudHJ5LmR1dHlQZXJmb3JtZWRJRCA9PT0gMFxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnUGxlYXNlIHNlbGVjdCBhIGR1dHknLFxyXG4gICAgICAgICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBHZXQgd29yayBkZXRhaWxzIGZyb20gdGhlIHRleHRhcmVhXHJcbiAgICAgICAgY29uc3Qgd29ya0RldGFpbHNFbGVtZW50ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICd3b3JrLWRldGFpbHMnLFxyXG4gICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgIGNvbnN0IHdvcmtEZXRhaWxzID0gd29ya0RldGFpbHNFbGVtZW50Py52YWx1ZSB8fCAnJ1xyXG5cclxuICAgICAgICBjb25zdCB2YXJpYWJsZXMgPSB7XHJcbiAgICAgICAgICAgIGlkOiBjcmV3TWFuaWZlc3RFbnRyeS5pZCxcclxuICAgICAgICAgICAgY3Jld01lbWJlcklEOiBjcmV3TWFuaWZlc3RFbnRyeS5jcmV3TWVtYmVySUQsXHJcbiAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogK2NyZXdNYW5pZmVzdEVudHJ5Py5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgIGxvZ0Jvb2tFbnRyeUlEOiArbG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgIHB1bmNoSW46IGxvZ2luVGltZSA/IGZvcm1hdERCRGF0ZVRpbWUobG9naW5UaW1lKSA6IG51bGwsXHJcbiAgICAgICAgICAgIHB1bmNoT3V0OiBsb2dvdXRUaW1lID8gZm9ybWF0REJEYXRlVGltZShsb2dvdXRUaW1lKSA6IG51bGwsXHJcbiAgICAgICAgICAgIHdvcmtEZXRhaWxzOiB3b3JrRGV0YWlscyxcclxuICAgICAgICB9XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgLy8gQ2FzZSAxOiBVcGRhdGluZyBhbiBleGlzdGluZyBjcmV3IGVudHJ5XHJcbiAgICAgICAgICAgIGlmIChjcmV3TWFuaWZlc3RFbnRyeS5pZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gU2F2ZSB0aGUgdXBkYXRlZCBjcmV3IG1lbWJlciB0byB0aGUgZGF0YWJhc2VcclxuICAgICAgICAgICAgICAgICAgICBhd2FpdCBsYkNyZXdNb2RlbC5zYXZlKHZhcmlhYmxlcylcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gR2V0IGFsbCBjcmV3IElEcyB0byBmZXRjaCB1cGRhdGVkIGRhdGFcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3SWRzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY3JldyAmJiBBcnJheS5pc0FycmF5KGNyZXcpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGNyZXcubWFwKChjOiBhbnkpID0+IGMuaWQpLmZpbHRlcihCb29sZWFuKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBbXVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBSZXNldCB0aGUgZm9ybVxyXG4gICAgICAgICAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHt9KVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBGZXRjaCB0aGUgdXBkYXRlZCBjcmV3IGRhdGFcclxuICAgICAgICAgICAgICAgICAgICBsZXQgY3Jld0RhdGEgPSBhd2FpdCBsYkNyZXdNb2RlbC5nZXRCeUlkcyhjcmV3SWRzKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAoY3Jld0RhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUHJvY2VzcyBjcmV3IG1lbWJlcnMgd2l0aCB0cmFpbmluZyBzdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvY2Vzc2VkRGF0YSA9IGNyZXdEYXRhLm1hcCgoc2VjdGlvbjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2VjdGlvbiAmJiBzZWN0aW9uLmNyZXdNZW1iZXIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyV2l0aFRyYWluaW5nU3RhdHVzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbc2VjdGlvbi5jcmV3TWVtYmVyXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFt2ZXNzZWxdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApWzBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uc2VjdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcjogY3Jld01lbWJlcldpdGhUcmFpbmluZ1N0YXR1cyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gR3JvdXAgY3JldyBkdXRpZXMgYnkgY3JldyBtZW1iZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZ3JvdXBlZERhdGEgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZ3JvdXBDcmV3RHV0aWVzQnlNZW1iZXIocHJvY2Vzc2VkRGF0YSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBzdGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDcmV3KGdyb3VwZWREYXRhKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDcmV3TWVtYmVycyhncm91cGVkRGF0YSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIE9ubGluZSBtb2RlIC0gdXNlIEdyYXBoUUwgbXV0YXRpb25cclxuICAgICAgICAgICAgICAgICAgICB1cGRhdGVDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7IGlucHV0OiB2YXJpYWJsZXMgfSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIENsb3NlIGRpYWxvZ3NcclxuICAgICAgICAgICAgICAgIHNldG9wZW5BZGRDcmV3TWVtYmVyRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgaWYgKGNhbGxCeSA9PT0gJ3VwZGF0ZScpIHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRPcGVuRWRpdExvZ291dFRpbWVEaWFsb2coZmFsc2UpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoY3Jld01hbmlmZXN0RW50cnkuY3Jld01lbWJlcklEID4gMCkge1xyXG4gICAgICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBHZW5lcmF0ZSBhIHVuaXF1ZSBJRCBmb3IgdGhlIG5ldyBlbnRyeVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHVuaXF1ZUlkID0gZ2VuZXJhdGVVbmlxdWVJZCgpXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4udmFyaWFibGVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZDogdW5pcXVlSWQsXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBTYXZlIHRoZSBuZXcgY3JldyBtZW1iZXIgdG8gdGhlIGRhdGFiYXNlXHJcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgbGJDcmV3TW9kZWwuc2F2ZShkYXRhKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBHZXQgdGhlIHNlbGVjdGVkIGNyZXcgbWVtYmVyIGFuZCBkdXR5IGluZm9ybWF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRNZW1iZXIgPSBhbGxWZXNzZWxDcmV3cy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoYzogYW55KSA9PiBjLmlkID09PSBjcmV3TWFuaWZlc3RFbnRyeS5jcmV3TWVtYmVySUQsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkRHV0eSA9IGFsbER1dGllcy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoZDogYW55KSA9PiBkLmlkID09PSBjcmV3TWFuaWZlc3RFbnRyeS5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAoIXNlbGVjdGVkTWVtYmVyIHx8ICFzZWxlY3RlZER1dHkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnQ291bGQgbm90IGZpbmQgY3JldyBtZW1iZXIgb3IgZHV0eSBpbmZvcm1hdGlvbicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm5cclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIENyZWF0ZSBhIG5ldyBjcmV3IGVudHJ5IHdpdGggdGhlIG5lY2Vzc2FyeSBkYXRhIGZvciBpbW1lZGlhdGUgZGlzcGxheVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0NyZXdFbnRyeSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uZGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcjogR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbc2VsZWN0ZWRNZW1iZXJdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClbMF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IHNlbGVjdGVkRHV0eSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIEdldCBleGlzdGluZyBjcmV3IGRhdGEgb3IgaW5pdGlhbGl6ZSBlbXB0eSBhcnJheVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nQ3JldyA9IEFycmF5LmlzQXJyYXkoY3JldykgPyBbLi4uY3Jld10gOiBbXVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGNyZXcgbWVtYmVyIGFscmVhZHkgZXhpc3RzIGluIHRoZSBsaXN0XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdDcmV3TWVtYmVySW5kZXggPSBleGlzdGluZ0NyZXcuZmluZEluZGV4KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoYzogYW55KSA9PiBjICYmIGMuY3Jld01lbWJlcklEID09PSBkYXRhLmNyZXdNZW1iZXJJRCxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGxldCB1cGRhdGVkQ3Jld0RhdGEgPSBbLi4uZXhpc3RpbmdDcmV3XVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAoZXhpc3RpbmdDcmV3TWVtYmVySW5kZXggIT09IC0xKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSBjcmV3IG1lbWJlciBhbHJlYWR5IGV4aXN0cywgYWRkIHRoaXMgZHV0eSB0byB0aGVpciBkdXRpZXMgYXJyYXlcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdDcmV3TWVtYmVyID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udXBkYXRlZENyZXdEYXRhW2V4aXN0aW5nQ3Jld01lbWJlckluZGV4XSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdDcmV3TWVtYmVyLmR1dGllcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShleGlzdGluZ0NyZXdNZW1iZXIuZHV0aWVzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEFkZCB0aGUgbmV3IGR1dHkgdG8gdGhlIGV4aXN0aW5nIGR1dGllcyBhcnJheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdDcmV3TWVtYmVyLmR1dGllcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5UGVyZm9ybWVkOiBzZWxlY3RlZER1dHksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogZGF0YS5wdW5jaEluLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoT3V0OiBkYXRhLnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdvcmtEZXRhaWxzOiBkYXRhLndvcmtEZXRhaWxzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogZGF0YS5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6IGRhdGEubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ3JlYXRlIGEgZHV0aWVzIGFycmF5IGlmIGl0IGRvZXNuJ3QgZXhpc3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nQ3Jld01lbWJlci5kdXRpZXMgPSBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eVBlcmZvcm1lZDogc2VsZWN0ZWREdXR5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaEluOiBkYXRhLnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoT3V0OiBkYXRhLnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3b3JrRGV0YWlsczogZGF0YS53b3JrRGV0YWlscyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eVBlcmZvcm1lZElEOiBkYXRhLmR1dHlQZXJmb3JtZWRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6IGRhdGEubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIHRoZSBjcmV3IG1lbWJlciBpbiB0aGUgYXJyYXlcclxuICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlZENyZXdEYXRhW2V4aXN0aW5nQ3Jld01lbWJlckluZGV4XSA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0NyZXdNZW1iZXJcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGlzIGlzIGEgbmV3IGNyZXcgbWVtYmVyLCBhZGQgdGhlbSB0byB0aGUgbGlzdCB3aXRoIHRoZWlyIGZpcnN0IGR1dHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlZENyZXdEYXRhLnB1c2goe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ubmV3Q3Jld0VudHJ5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0aWVzOiBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eVBlcmZvcm1lZDogc2VsZWN0ZWREdXR5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaEluOiBkYXRhLnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoT3V0OiBkYXRhLnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3b3JrRGV0YWlsczogZGF0YS53b3JrRGV0YWlscyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eVBlcmZvcm1lZElEOiBkYXRhLmR1dHlQZXJmb3JtZWRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6IGRhdGEubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBHcm91cCB0aGUgdXBkYXRlZCBjcmV3IGRhdGFcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBncm91cGVkRGF0YSA9IGdyb3VwQ3Jld0R1dGllc0J5TWVtYmVyKHVwZGF0ZWRDcmV3RGF0YSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIHN0YXRlIHdpdGggdGhlIG5ldyBncm91cGVkIGRhdGFcclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3KGdyb3VwZWREYXRhKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXJzKGdyb3VwZWREYXRhKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHt9KVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBBbHNvIGZldGNoIHRoZSBsYXRlc3QgZGF0YSBmcm9tIHRoZSBkYXRhYmFzZSB0byBlbnN1cmUgY29uc2lzdGVuY3lcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3SWRzID0gdXBkYXRlZENyZXdEYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKGM6IGFueSkgPT4gYyAmJiBjLmlkKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5jb25jYXQoW3VuaXF1ZUlkXSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgbGV0IGNyZXdEYXRhID0gYXdhaXQgbGJDcmV3TW9kZWwuZ2V0QnlJZHMoY3Jld0lkcylcclxuICAgICAgICAgICAgICAgICAgICBpZiAoY3Jld0RhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUHJvY2VzcyBjcmV3IG1lbWJlcnMgd2l0aCB0cmFpbmluZyBzdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvY2Vzc2VkRGJEYXRhID0gY3Jld0RhdGEubWFwKChzZWN0aW9uOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzZWN0aW9uICYmIHNlY3Rpb24uY3Jld01lbWJlcikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdNZW1iZXJXaXRoVHJhaW5pbmdTdGF0dXMgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtzZWN0aW9uLmNyZXdNZW1iZXJdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5zZWN0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyOiBjcmV3TWVtYmVyV2l0aFRyYWluaW5nU3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBzZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBHcm91cCBjcmV3IGR1dGllcyBieSBjcmV3IG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBncm91cGVkRGJEYXRhID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyb3VwQ3Jld0R1dGllc0J5TWVtYmVyKHByb2Nlc3NlZERiRGF0YSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB3aXRoIHRoZSBkYXRhYmFzZSBkYXRhIHRvIGVuc3VyZSBjb25zaXN0ZW5jeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDcmV3KGdyb3VwZWREYkRhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXJzKGdyb3VwZWREYkRhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBPbmxpbmUgbW9kZSAtIHVzZSBHcmFwaFFMIG11dGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgY3JlYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczogeyBpbnB1dDogdmFyaWFibGVzIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAvLyBDbG9zZSBkaWFsb2dcclxuICAgICAgICAgICAgICAgIHNldG9wZW5BZGRDcmV3TWVtYmVyRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgLy8gTm8gdmFsaWQgY3JldyBtZW1iZXIgc2VsZWN0ZWQsIGp1c3QgY2FuY2VsXHJcbiAgICAgICAgICAgICAgICBoYW5kbGVDYW5jZWwoKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGNyZXcgbWVtYmVyOicsIGVycm9yKVxyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIHNhdmUgY3JldyBtZW1iZXIuIFBsZWFzZSB0cnkgYWdhaW4uJyxcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IFt1cGRhdGVDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uXSA9IHVzZU11dGF0aW9uKFxyXG4gICAgICAgIFVwZGF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gRmlyc3QsIHVwZGF0ZSB0aGUgVUkgaW1tZWRpYXRlbHkgd2l0aCB0aGUgdXBkYXRlZCBkYXRhXHJcbiAgICAgICAgICAgICAgICBpZiAoY3Jld01hbmlmZXN0RW50cnkuaWQgPiAwICYmIGNyZXcgJiYgQXJyYXkuaXNBcnJheShjcmV3KSkge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIENyZWF0ZSBhIGRlZXAgY29weSBvZiB0aGUgY3VycmVudCBjcmV3IGRhdGFcclxuICAgICAgICAgICAgICAgICAgICBsZXQgdXBkYXRlZENyZXdEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShjcmV3KSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gRmluZCB0aGUgY3JldyBtZW1iZXIgYW5kIGR1dHkgdGhhdCB3YXMgdXBkYXRlZFxyXG4gICAgICAgICAgICAgICAgICAgIGxldCBmb3VuZEFuZFVwZGF0ZWQgPSBmYWxzZVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBMb29wIHRocm91Z2ggYWxsIGNyZXcgbWVtYmVyc1xyXG4gICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdXBkYXRlZENyZXdEYXRhLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlciA9IHVwZGF0ZWRDcmV3RGF0YVtpXVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgdGhpcyBpcyB0aGUgbWFpbiBkdXR5IHRoYXQgd2FzIHVwZGF0ZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG1lbWJlci5pZCA9PT0gY3Jld01hbmlmZXN0RW50cnkuaWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgbWFpbiBkdXR5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVkQ3Jld0RhdGFbaV0gPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ubWVtYmVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoT3V0OiBjcmV3TWFuaWZlc3RFbnRyeS5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvdW5kQW5kVXBkYXRlZCA9IHRydWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgaXMgYSBuZXN0ZWQgZHV0eSB0aGF0IHdhcyB1cGRhdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtZW1iZXIuZHV0aWVzICYmIEFycmF5LmlzQXJyYXkobWVtYmVyLmR1dGllcykpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgbWVtYmVyLmR1dGllcy5sZW5ndGg7IGorKykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGR1dHkgPSBtZW1iZXIuZHV0aWVzW2pdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGR1dHkuaWQgPT09IGNyZXdNYW5pZmVzdEVudHJ5LmlkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgbmVzdGVkIGR1dHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmR1dGllc1tqXSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmR1dHksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogY3Jld01hbmlmZXN0RW50cnkucHVuY2hPdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm91bmRBbmRVcGRhdGVkID0gdHJ1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVha1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZm91bmRBbmRVcGRhdGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZvdW5kQW5kVXBkYXRlZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBHcm91cCB0aGUgdXBkYXRlZCBjcmV3IGRhdGFcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZ3JvdXBlZERhdGEgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZ3JvdXBDcmV3RHV0aWVzQnlNZW1iZXIodXBkYXRlZENyZXdEYXRhKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIHN0YXRlIHdpdGggdGhlIG5ldyBncm91cGVkIGRhdGFcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3Jldyhncm91cGVkRGF0YSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld01lbWJlcnMoZ3JvdXBlZERhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIFRoZW4sIGZldGNoIHRoZSBsYXRlc3QgZGF0YSBmcm9tIHRoZSBzZXJ2ZXIgdG8gZW5zdXJlIGNvbnNpc3RlbmN5XHJcbiAgICAgICAgICAgICAgICBjb25zdCBhcHBlbmREYXRhID0gWy4uLmNyZXcubWFwKChjOiBhbnkpID0+IGMuaWQpXVxyXG4gICAgICAgICAgICAgICAgc2V0Q3Jld01hbmlmZXN0RW50cnkoe30pXHJcbiAgICAgICAgICAgICAgICBjb25zdCBzZWFyY2hGaWx0ZXI6IFNlYXJjaEZpbHRlciA9IHt9XHJcbiAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIuaWQgPSB7IGluOiBhcHBlbmREYXRhIH1cclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5hcmNoaXZlZCA9IHsgZXE6IGZhbHNlIH1cclxuICAgICAgICAgICAgICAgIGdldFNlY3Rpb25DcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiBzZWFyY2hGaWx0ZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigndXBkYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgW2NyZWF0ZUNyZXdXZWxmYXJlQ2hlY2ssIHsgbG9hZGluZzogY3JlYXRlQ3Jld1dlbGZhcmVDaGVja0xvYWRpbmcgfV0gPVxyXG4gICAgICAgIHVzZU11dGF0aW9uKENyZWF0ZUNyZXdXZWxmYXJlX0xvZ0Jvb2tFbnRyeVNlY3Rpb24sIHtcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmNyZWF0ZUNyZXdXZWxmYXJlX0xvZ0Jvb2tFbnRyeVNlY3Rpb25cclxuICAgICAgICAgICAgICAgIHVwZGF0ZUNyZXdXZWxmYXJlKGRhdGEpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignY3JlYXRlQ3Jld1dlbGZhcmVDaGVjaycsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgY29uc3QgW2NyZWF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb25dID0gdXNlTXV0YXRpb24oXHJcbiAgICAgICAgQ3JlYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbixcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAoZGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gRmlyc3QsIHVwZGF0ZSB0aGUgVUkgaW1tZWRpYXRlbHkgd2l0aCB0aGUgbmV3IGNyZXcgbWVtYmVyXHJcbiAgICAgICAgICAgICAgICAvLyBHZXQgdGhlIHNlbGVjdGVkIGNyZXcgbWVtYmVyIGFuZCBkdXR5IGluZm9ybWF0aW9uXHJcbiAgICAgICAgICAgICAgICBjb25zdCBzZWxlY3RlZE1lbWJlciA9IGFsbE1lbWJlcnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAobTogYW55KSA9PiBtLnZhbHVlID09PSBjcmV3TWFuaWZlc3RFbnRyeS5jcmV3TWVtYmVySUQsXHJcbiAgICAgICAgICAgICAgICApPy5kYXRhXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWREdXR5ID0gZHV0aWVzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgKGQ6IGFueSkgPT4gZC5pZCA9PT0gY3Jld01hbmlmZXN0RW50cnkuZHV0eVBlcmZvcm1lZElELFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIGlmIChzZWxlY3RlZE1lbWJlciAmJiBzZWxlY3RlZER1dHkpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBDcmVhdGUgYSBuZXcgY3JldyBlbnRyeSB3aXRoIHRoZSBuZWNlc3NhcnkgZGF0YSBmb3IgaW1tZWRpYXRlIGRpc3BsYXlcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdDcmV3RW50cnkgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBkYXRhLmNyZWF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJJRDogY3Jld01hbmlmZXN0RW50cnkuY3Jld01lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkdXR5UGVyZm9ybWVkSUQ6IGNyZXdNYW5pZmVzdEVudHJ5LmR1dHlQZXJmb3JtZWRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9nQm9va0VudHJ5SUQ6ICtsb2dCb29rRW50cnlJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogbG9naW5UaW1lID8gZm9ybWF0REJEYXRlVGltZShsb2dpblRpbWUpIDogbnVsbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hPdXQ6IGxvZ291dFRpbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZm9ybWF0REJEYXRlVGltZShsb2dvdXRUaW1lKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB3b3JrRGV0YWlsczpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3dvcmstZGV0YWlscycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApPy52YWx1ZSB8fCAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcjogc2VsZWN0ZWRNZW1iZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IHNlbGVjdGVkRHV0eSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIENyZWF0ZSBhIG5ldyBhcnJheSB3aXRoIGV4aXN0aW5nIGNyZXcgZGF0YSBwbHVzIHRoZSBuZXcgZW50cnlcclxuICAgICAgICAgICAgICAgICAgICBsZXQgdXBkYXRlZENyZXdEYXRhID0gY3JldyA/IFsuLi5jcmV3XSA6IFtdXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgY3JldyBtZW1iZXIgYWxyZWFkeSBleGlzdHMgaW4gdGhlIGxpc3RcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ0NyZXdNZW1iZXJJbmRleCA9IHVwZGF0ZWRDcmV3RGF0YS5maW5kSW5kZXgoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChjOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjLmNyZXdNZW1iZXJJRCA9PT0gY3Jld01hbmlmZXN0RW50cnkuY3Jld01lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV4aXN0aW5nQ3Jld01lbWJlckluZGV4ID49IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gSWYgdGhlIGNyZXcgbWVtYmVyIGFscmVhZHkgZXhpc3RzLCBhZGQgdGhpcyBkdXR5IHRvIHRoZWlyIGR1dGllcyBhcnJheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ0NyZXdNZW1iZXIgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlZENyZXdEYXRhW2V4aXN0aW5nQ3Jld01lbWJlckluZGV4XVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkRHV0aWVzID0gZXhpc3RpbmdDcmV3TWVtYmVyLmR1dGllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBbLi4uZXhpc3RpbmdDcmV3TWVtYmVyLmR1dGllc11cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogW11cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZWREdXRpZXMucHVzaCh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogZGF0YS5jcmVhdGVDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eVBlcmZvcm1lZDogc2VsZWN0ZWREdXR5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcHVuY2hJbjogbmV3Q3Jld0VudHJ5LnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogbmV3Q3Jld0VudHJ5LnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IG5ld0NyZXdFbnRyeS53b3JrRGV0YWlscyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWRJRDogbmV3Q3Jld0VudHJ5LmR1dHlQZXJmb3JtZWRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvZ0Jvb2tFbnRyeUlEOiBuZXdDcmV3RW50cnkubG9nQm9va0VudHJ5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIGNyZXcgbWVtYmVyIHdpdGggdGhlIG5ldyBkdXRpZXMgYXJyYXlcclxuICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlZENyZXdEYXRhW2V4aXN0aW5nQ3Jld01lbWJlckluZGV4XSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmV4aXN0aW5nQ3Jld01lbWJlcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogdXBkYXRlZER1dGllcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBuZXcgY3JldyBtZW1iZXIsIGFkZCB0aGVtIHRvIHRoZSBsaXN0IHdpdGggdGhlaXIgZmlyc3QgZHV0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVkQ3Jld0RhdGEucHVzaCh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5uZXdDcmV3RW50cnksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXRpZXM6IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBkYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuY3JlYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5UGVyZm9ybWVkOiBzZWxlY3RlZER1dHksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoSW46IG5ld0NyZXdFbnRyeS5wdW5jaEluLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogbmV3Q3Jld0VudHJ5LnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3b3JrRGV0YWlsczogbmV3Q3Jld0VudHJ5LndvcmtEZXRhaWxzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5UGVyZm9ybWVkSUQ6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdDcmV3RW50cnkuZHV0eVBlcmZvcm1lZElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogbmV3Q3Jld0VudHJ5LmxvZ0Jvb2tFbnRyeUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gR3JvdXAgdGhlIHVwZGF0ZWQgY3JldyBkYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZ3JvdXBlZERhdGEgPSBncm91cENyZXdEdXRpZXNCeU1lbWJlcih1cGRhdGVkQ3Jld0RhdGEpXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBzdGF0ZSB3aXRoIHRoZSBuZXcgZ3JvdXBlZCBkYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jldyhncm91cGVkRGF0YSlcclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3TWVtYmVycyhncm91cGVkRGF0YSlcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAvLyBUaGVuLCBmZXRjaCB0aGUgbGF0ZXN0IGRhdGEgZnJvbSB0aGUgc2VydmVyIHRvIGVuc3VyZSBjb25zaXN0ZW5jeVxyXG4gICAgICAgICAgICAgICAgY29uc3QgYXBwZW5kRGF0YSA9IGNyZXdcclxuICAgICAgICAgICAgICAgICAgICA/IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5jcmV3Py5tYXAoKGM6IGFueSkgPT4gYy5pZCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS5jcmVhdGVDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgXVxyXG4gICAgICAgICAgICAgICAgICAgIDogW2RhdGEuY3JlYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbi5pZF1cclxuICAgICAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHt9KVxyXG4gICAgICAgICAgICAgICAgY29uc3Qgc2VhcmNoRmlsdGVyOiBTZWFyY2hGaWx0ZXIgPSB7fVxyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLmlkID0geyBpbjogYXBwZW5kRGF0YSB9XHJcbiAgICAgICAgICAgICAgICBnZXRTZWN0aW9uQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogc2VhcmNoRmlsdGVyLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2NyZWF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24nLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG5cclxuICAgIGNvbnN0IFtnZXRTZWN0aW9uQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbl0gPSB1c2VMYXp5UXVlcnkoXHJcbiAgICAgICAgQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbixcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbnMubm9kZXNcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gUHJvY2VzcyBjcmV3IG1lbWJlcnMgd2l0aCB0cmFpbmluZyBzdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcm9jZXNzZWREYXRhID0gZGF0YS5tYXAoKHNlY3Rpb246IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2VjdGlvbi5jcmV3TWVtYmVyKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyV2l0aFRyYWluaW5nU3RhdHVzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW3NlY3Rpb24uY3Jld01lbWJlcl0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFt2ZXNzZWxdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uc2VjdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyOiBjcmV3TWVtYmVyV2l0aFRyYWluaW5nU3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBzZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gUHJlc2VydmUgZXhpc3RpbmcgZHV0aWVzIGlmIHRoZXkgZXhpc3RcclxuICAgICAgICAgICAgICAgICAgICBsZXQgdXBkYXRlZERhdGEgPSBwcm9jZXNzZWREYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNyZXcgJiYgY3Jldy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIENyZWF0ZSBhIG1hcCBvZiBleGlzdGluZyBjcmV3IG1lbWJlcnMgd2l0aCB0aGVpciBkdXRpZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdDcmV3TWFwID0gY3Jldy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWFwOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LCBtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtZW1iZXIuY3Jld01lbWJlcklEKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1hcFttZW1iZXIuY3Jld01lbWJlcklEXSA9IG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbWFwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge30sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBwcm9jZXNzZWQgZGF0YSB3aXRoIGV4aXN0aW5nIGR1dGllcyB3aGVyZSBhcHBsaWNhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZWREYXRhID0gcHJvY2Vzc2VkRGF0YS5tYXAoKHNlY3Rpb246IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNZW1iZXIgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nQ3Jld01hcFtzZWN0aW9uLmNyZXdNZW1iZXJJRF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlciAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmR1dGllcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmR1dGllcy5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBGaW5kIHRoZSBtYXRjaGluZyBkdXR5IGluIHRoZSBleGlzdGluZyBkdXRpZXMgYXJyYXlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ0R1dHlJbmRleCA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmR1dGllcy5maW5kSW5kZXgoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZHV0eTogYW55KSA9PiBkdXR5LmlkID09PSBzZWN0aW9uLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ0R1dHlJbmRleCA+PSAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRoaXMgc2VjdGlvbiBpcyBhbHJlYWR5IGluIHRoZSBkdXRpZXMsIHVwZGF0ZSBpdCB3aXRoIHRoZSBsYXRlc3QgZGF0YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkRHV0aWVzID0gW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZXhpc3RpbmdNZW1iZXIuZHV0aWVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZWREdXRpZXNbZXhpc3RpbmdEdXR5SW5kZXhdID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udXBkYXRlZER1dGllc1tleGlzdGluZ0R1dHlJbmRleF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgd2l0aCB0aGUgbGF0ZXN0IGRhdGEgZnJvbSB0aGUgc2VydmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaEluOiBzZWN0aW9uLnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdW5jaE91dDogc2VjdGlvbi5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdvcmtEZXRhaWxzOiBzZWN0aW9uLndvcmtEZXRhaWxzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uc2VjdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogdXBkYXRlZER1dGllcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgYSBuZXcgZHV0eSBmb3IgdGhpcyBjcmV3IG1lbWJlciwgYWRkIGl0IHRvIHRoZWlyIGR1dGllc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkRHV0aWVzID0gW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZXhpc3RpbmdNZW1iZXIuZHV0aWVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZWREdXRpZXMucHVzaCh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogc2VjdGlvbi5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IHNlY3Rpb24uZHV0eVBlcmZvcm1lZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoSW46IHNlY3Rpb24ucHVuY2hJbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoT3V0OiBzZWN0aW9uLnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IHNlY3Rpb24ud29ya0RldGFpbHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5UGVyZm9ybWVkSUQ6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VjdGlvbi5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogc2VjdGlvbi5sb2dCb29rRW50cnlJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5zZWN0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0aWVzOiB1cGRhdGVkRHV0aWVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIE5vIGV4aXN0aW5nIGR1dGllcyBmb3IgdGhpcyBjcmV3IG1lbWJlciwgY3JlYXRlIGEgbmV3IGR1dGllcyBhcnJheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5zZWN0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dGllczogW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogc2VjdGlvbi5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHlQZXJmb3JtZWQ6IHNlY3Rpb24uZHV0eVBlcmZvcm1lZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoSW46IHNlY3Rpb24ucHVuY2hJbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1bmNoT3V0OiBzZWN0aW9uLnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd29ya0RldGFpbHM6IHNlY3Rpb24ud29ya0RldGFpbHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5UGVyZm9ybWVkSUQ6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VjdGlvbi5kdXR5UGVyZm9ybWVkSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlJRDogc2VjdGlvbi5sb2dCb29rRW50cnlJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gR3JvdXAgY3JldyBkdXRpZXMgYnkgY3JldyBtZW1iZXJcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBncm91cGVkRGF0YSA9IGdyb3VwQ3Jld0R1dGllc0J5TWVtYmVyKHVwZGF0ZWREYXRhKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3KGdyb3VwZWREYXRhKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXJzKGdyb3VwZWREYXRhKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtZW1iZXJzID0gYWxsTWVtYmVycy5maWx0ZXIoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gIWRhdGEuc29tZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChzZWN0aW9uOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VjdGlvbi5jcmV3TWVtYmVyLmlkID09PSBtZW1iZXIudmFsdWUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLnB1bmNoT3V0ID09PSBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3TWVtYmVyT3B0aW9ucyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVycy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIWNyZXdNZW1iZXJzTGlzdCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICFjcmV3TWVtYmVyc0xpc3QuaW5jbHVkZXMoK21lbWJlci52YWx1ZSksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICAnZ2V0U2VjdGlvbkNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24nLFxyXG4gICAgICAgICAgICAgICAgICAgIGVycm9yLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgaGFuZGxlQXJjaGl2ZSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBzZXRPcGVuQ29uZmlybUNyZXdEZWxldGVEaWFsb2coZmFsc2UpXHJcblxyXG4gICAgICAgIGlmICghY3Jld01hbmlmZXN0RW50cnkuaWQpIHtcclxuICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ05vIGNyZXcgbWVtYmVyIHNlbGVjdGVkIHRvIGRlbGV0ZScsXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICAvLyBGaXJzdCB0cnkgdG8gZGVsZXRlIHRoZSByZWNvcmRcclxuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGxiQ3Jld01vZGVsLmRlbGV0ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGNyZXdNYW5pZmVzdEVudHJ5LmlkLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAoIXJlc3VsdCkge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIElmIGRlbGV0ZSBmYWlscywgbWFyayBhcyBhcmNoaXZlZFxyXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IGxiQ3Jld01vZGVsLnNhdmUoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZDogY3Jld01hbmlmZXN0RW50cnkuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyY2hpdmVkOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgYXBwZW5kRGF0YSA9IFsuLi5jcmV3Lm1hcCgoYzogYW55KSA9PiBjLmlkKV1cclxuICAgICAgICAgICAgICAgIHNldENyZXdNYW5pZmVzdEVudHJ5KHt9KVxyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGxiQ3Jld01vZGVsLmdldEJ5SWRzKGFwcGVuZERhdGEpXHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBQcm9jZXNzIGNyZXcgbWVtYmVycyB3aXRoIHRyYWluaW5nIHN0YXR1c1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb2Nlc3NlZERhdGEgPSBkYXRhLm1hcCgoc2VjdGlvbjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzZWN0aW9uLmNyZXdNZW1iZXIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdNZW1iZXJXaXRoVHJhaW5pbmdTdGF0dXMgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEdldENyZXdMaXN0V2l0aFRyYWluaW5nU3RhdHVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbc2VjdGlvbi5jcmV3TWVtYmVyXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVswXVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5zZWN0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXI6IGNyZXdNZW1iZXJXaXRoVHJhaW5pbmdTdGF0dXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBHcm91cCBjcmV3IGR1dGllcyBieSBjcmV3IG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGdyb3VwZWREYXRhID0gZ3JvdXBDcmV3RHV0aWVzQnlNZW1iZXIocHJvY2Vzc2VkRGF0YSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jldyhncm91cGVkRGF0YSlcclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3TWVtYmVycyhncm91cGVkRGF0YSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIGNyZXcgbWVtYmVyOicsIGVycm9yKVxyXG4gICAgICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIGRlbGV0ZSBjcmV3IG1lbWJlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgLy8gVXNlIHRoZSBkZWxldGUgbXV0YXRpb24gaW5zdGVhZCBvZiB1cGRhdGVcclxuICAgICAgICAgICAgICAgIGF3YWl0IGRlbGV0ZUNyZXdNZW1iZXJzTG9nQm9va0VudHJ5U2VjdGlvbnMoe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZHM6IFtjcmV3TWFuaWZlc3RFbnRyeS5pZF0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBjcmV3IG1lbWJlcjonLCBlcnJvcilcclxuICAgICAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ0ZhaWxlZCB0byBkZWxldGUgY3JldyBtZW1iZXInLFxyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRvcGVuQWRkQ3Jld01lbWJlckRpYWxvZyhmYWxzZSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBGdW5jdGlvbiByZW1vdmVkIGFzIHdlJ3JlIGRpcmVjdGx5IHVzaW5nIHNldE9wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZ1xyXG4gICAgY29uc3QgW2RlbGV0ZUNyZXdNZW1iZXJzTG9nQm9va0VudHJ5U2VjdGlvbnNdID0gdXNlTXV0YXRpb24oXHJcbiAgICAgICAgRGVsZXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbnMsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgYXBwZW5kRGF0YSA9IFsuLi5jcmV3Lm1hcCgoYzogYW55KSA9PiBjLmlkKV1cclxuICAgICAgICAgICAgICAgIGNvbnN0IHNlYXJjaEZpbHRlcjogU2VhcmNoRmlsdGVyID0ge31cclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5pZCA9IHsgaW46IGFwcGVuZERhdGEgfVxyXG4gICAgICAgICAgICAgICAgZ2V0U2VjdGlvbkNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24oe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXI6IHNlYXJjaEZpbHRlcixcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIHNldE9wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIHNldG9wZW5BZGRDcmV3TWVtYmVyRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgJ2RlbGV0ZUNyZXdNZW1iZXJzTG9nQm9va0VudHJ5U2VjdGlvbnMgZXJyb3I6JyxcclxuICAgICAgICAgICAgICAgICAgICBlcnJvcixcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG4gICAgLy8gRnVuY3Rpb24gcmVtb3ZlZCBhcyB3ZSdyZSB1c2luZyBoYW5kbGVBcmNoaXZlIGluc3RlYWRcclxuICAgIGNvbnN0IGNyZXdDb3VudCA9ICgpID0+IHtcclxuICAgICAgICBpZiAoIWNyZXcgfHwgIUFycmF5LmlzQXJyYXkoY3JldykpIHJldHVybiAwXHJcblxyXG4gICAgICAgIGNvbnN0IGNvdW50ID0gY3Jldy5maWx0ZXIoXHJcbiAgICAgICAgICAgIChtZW1iZXI6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgIG1lbWJlciAmJiBtZW1iZXIuY3Jld01lbWJlcklEID4gMCAmJiBtZW1iZXIucHVuY2hPdXQgPT09IG51bGwsXHJcbiAgICAgICAgKS5sZW5ndGhcclxuICAgICAgICByZXR1cm4gY291bnRcclxuICAgIH1cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGNyZXdDb25maWcpIHtcclxuICAgICAgICAgICAgaGFuZGxlU2V0U3RhdHVzKClcclxuICAgICAgICB9XHJcbiAgICB9LCBbY3Jld0NvbmZpZ10pXHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAoIWlzRW1wdHkobG9nQm9va0NvbmZpZykpIHtcclxuICAgICAgICAgICAgaGFuZGxlU2V0Q3Jld0NvbmZpZygpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2xvZ0Jvb2tDb25maWddKVxyXG4gICAgLy8gUmVtb3ZlZCB1bnVzZWQgb3ZlcmR1ZVRleHRXYXJuaW5nIHZhcmlhYmxlXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbGc6Z3JpZC1jb2xzLTggZ2FwLTM2IGxnOmdhcC02IHhsOmdhcC04XCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi01IHNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxIMj5DcmV3PC9IMj5cclxuICAgICAgICAgICAgICAgICAgICB7Y3JldyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQgY2xhc3NOYW1lPVwicGwtMi41IHRleHQtbGVmdCBhbGlnbi1ib3R0b20gc3RhbmRhcmQ6YWxpZ24tdG9wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3Jld1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInB4LVs1cHhdIHRleHQtbGVmdCBhbGlnbi1ib3R0b20gc3RhbmRhcmQ6YWxpZ24tdG9wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRHV0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2JwLnN0YW5kYXJkID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwdW5jaEluU3RhdHVzICE9PSAnT2ZmJyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInB4LVs1cHhdIHRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hJbkxhYmVsIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdTaWduIEluJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoT3V0U3RhdHVzICE9PSAnT2ZmJyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInBsLVs1cHhdIHByLTIuNSB0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoT3V0TGFiZWwgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1NpZ24gT3V0J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInRleHQtd3JhcCBzdGFuZGFyZDp0ZXh0LW5vd3JhcCBwci0wIHRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwdW5jaEluU3RhdHVzICE9PSAnT2ZmJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcHVuY2hJbkxhYmVsIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnU2lnbiBJbidcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgL1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJyIGNsYXNzTmFtZT1cInN0YW5kYXJkOmhpZGRlblwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hPdXRTdGF0dXMgIT09ICdPZmYnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBwdW5jaE91dExhYmVsIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnU2lnbiBPdXQnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG1lbWJlcjogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICArbWVtYmVyLmNyZXdNZW1iZXJJRCA+IDAgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmFyY2hpdmVkID09PSBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgbWVtYmVyIGhhcyBtdWx0aXBsZSBkdXRpZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBoYXNNdWx0aXBsZUR1dGllcyA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5kdXRpZXMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShtZW1iZXIuZHV0aWVzKSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZHV0aWVzLmxlbmd0aCA+IDFcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gR2V0IGFkZGl0aW9uYWwgZHV0aWVzIChpZiBhbnkpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYWRkaXRpb25hbER1dGllcyA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhc011bHRpcGxlRHV0aWVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IG1lbWJlci5kdXRpZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zbGljZSgxKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZHV0eTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEdldCB0aGUgZmlyc3QgZHV0eSdzIHRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpcnN0RHV0eVRpdGxlID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5kdXRpZXNbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZHV0eVBlcmZvcm1lZCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZHV0aWVzWzBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5kdXR5UGVyZm9ybWVkIGFzIGFueVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50aXRsZVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gR2V0IGN1cnJlbnQgZHV0eSdzIHRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnREdXR5VGl0bGUgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eS5kdXR5UGVyZm9ybWVkICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eS5kdXR5UGVyZm9ybWVkIGFzIGFueVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50aXRsZVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gT25seSBpbmNsdWRlIGR1dGllcyB3aXRoIGRpZmZlcmVudCB0aXRsZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnREdXR5VGl0bGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpcnN0RHV0eVRpdGxlICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnREdXR5VGl0bGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFtdXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSZWFjdC5GcmFnbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtgY3Jldy0ke21lbWJlci5pZH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBNYWluIGNyZXcgbWVtYmVyIHJvdyAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvd1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17bWVtYmVyLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtZGlzYWJsZWQ9e2xvY2tlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cCAke2hhc011bHRpcGxlRHV0aWVzID8gJ2JvcmRlci1iLTAnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBEb24ndCBkbyBhbnl0aGluZyBpZiBsb2NrZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGxvY2tlZCkgcmV0dXJuXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBQcmV2ZW50IHJvdyBjbGljayBpZiB0aGUgZXZlbnQgb3JpZ2luYXRlZCBmcm9tIGEgYnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0IGluc3RhbmNlb2ZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBIVE1MRWxlbWVudCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGUudGFyZ2V0LmNsb3Nlc3QoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2J1dHRvbicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuY2xvc2VzdChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1tyb2xlPVwiYnV0dG9uXCJdJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUVkaXRNYW5pZmVzdChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWxlZnQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkaXRpb25hbER1dGllcy5sZW5ndGggPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnIHRleHQtZm9yZWdyb3VuZCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuY3Jld01lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRyYWluaW5nU3RhdHVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/LmxhYmVsICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnR29vZCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2Rlc3RydWN0aXZlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnc3VjY2VzcydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5jcmV3TWVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5jcmV3TWVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnN1cm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGVhZGluZy1ub25lIHNtOmZsZXggZmxleC1jb2wganVzdGlmeS1jZW50ZXIgbWwtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggZ2FwLTIuNSBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5jcmV3TWVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5maXJzdE5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH17JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuY3Jld01lbWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuc3VybmFtZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXIud29ya0RldGFpbHMgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTAgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbmZvSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1saWdodC1ibHVlLXZpdmlkLTkwMCBmaWxsLWxpZ2h0LWJsdWUtdml2aWQtNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMjRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudCBjbGFzc05hbWU9XCJ3LTgwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci53b3JrRGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtbGVmdCBncmlkIGl0ZW1zLWNlbnRlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRpdGlvbmFsRHV0aWVzLmxlbmd0aCA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWZvcmVncm91bmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlci5kdXR5UGVyZm9ybWVkICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmR1dHlQZXJmb3JtZWQgYXMgYW55XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmR1dHlQZXJmb3JtZWQgYXMgYW55XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnTm90IGFzc2lnbmVkJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHticC5zdGFuZGFyZCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1yaWdodCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkZGl0aW9uYWxEdXRpZXMubGVuZ3RoID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWZvcmVncm91bmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwdW5jaEluU3RhdHVzICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnT2ZmJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyPy5wdW5jaEluXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGZvcm1hdERhdGVUaW1lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ05vdCBBdmFpbGFibGUnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1yaWdodCBwaGFibGV0OnByLTIuNSByZWxhaXZlJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkaXRpb25hbER1dGllcy5sZW5ndGggPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtaW5wdXQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwdW5jaE91dFN0YXR1cyAhPT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ09mZicgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKCFtZW1iZXIucHVuY2hPdXQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWZpbGwtaW5oZXJpdCBmb250LW5vcm1hbCBoLWZpdCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2tlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2lnbk91dFRpbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hPdXRMYWJlbCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnU2lnbiBPdXQnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd3aGl0ZXNwYWNlLW5vd3JhcCBoLTggZmxleCBpdGVtcy1jZW50ZXIgdy1mdWxsIGp1c3RpZnktZW5kJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZVRpbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LXJpZ2h0IHJlbGFpdmUgZmxleCBmbGV4LWNvbCBwaGFibGV0OnByLTIuNSBqdXN0aWZ5LWNlbnRlciBpdGVtcy1lbmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRpdGlvbmFsRHV0aWVzLmxlbmd0aCA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1pbnB1dCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoSW5TdGF0dXMgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPZmYnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChtZW1iZXI/LnB1bmNoSW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZm9ybWF0RGF0ZVRpbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIucHVuY2hJbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnTm90IEF2YWlsYWJsZScpfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hPdXRTdGF0dXMgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPZmYnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICghbWVtYmVyLnB1bmNoT3V0ID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1maWxsLWluaGVyaXQgZm9udC1ub3JtYWwgaC1maXQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NrZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVNpZ25PdXRUaW1lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoT3V0TGFiZWwgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1NpZ24gT3V0J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnd2hpdGVzcGFjZS1ub3dyYXAgaC04IGZsZXggaXRlbXMtY2VudGVyIHctZnVsbCBqdXN0aWZ5LWVuZCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGVUaW1lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIucHVuY2hPdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFJlbmRlciBhZGRpdGlvbmFsIGR1dGllcyBhcyBzZXBhcmF0ZSByb3dzICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FkZGl0aW9uYWxEdXRpZXMubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eTogYW55LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleDogbnVtYmVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3dcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17YGR1dHktJHtkdXR5LmlkfS0ke2luZGV4fWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcmlhLWRpc2FibGVkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NrZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2dyb3VwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkaXRpb25hbER1dGllcy5sZW5ndGggLVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1iLTAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRG9uJ3QgZG8gYW55dGhpbmcgaWYgbG9ja2VkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGxvY2tlZClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFByZXZlbnQgcm93IGNsaWNrIGlmIHRoZSBldmVudCBvcmlnaW5hdGVkIGZyb20gYSBidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0IGluc3RhbmNlb2ZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEhUTUxFbGVtZW50ICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlLnRhcmdldC5jbG9zZXN0KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2J1dHRvbicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LmNsb3Nlc3QoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1tyb2xlPVwiYnV0dG9uXCJdJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVFZGl0TWFuaWZlc3QoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnIHRleHQtaW5wdXQgcHktMiB0ZXh0LWxlZnQgcmVsYXRpdmUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBhYnNvbHV0ZSAtdG9wLVs0MiVdIGl0ZW1zLWNlbnRlciB3LTggaC1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LVsxcHhdIGZsZXgtMSBib3JkZXItbCBib3JkZXItZGFzaGVkIGJvcmRlci1uZXV0cmFsLTQwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2l6ZS1bNXB4XSByb3VuZGVkLWZ1bGwgYmctYmFja2dyb3VuZCBib3JkZXIgYm9yZGVyLW5ldXRyYWwtNDAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnIHRleHQtaW5wdXQgZ3JpZCBpdGVtcy1jZW50ZXIgdGV4dC1sZWZ0JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRydW5jYXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkdXR5LmR1dHlQZXJmb3JtZWQgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eS5kdXR5UGVyZm9ybWVkIGFzIGFueVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHkuZHV0eVBlcmZvcm1lZCBhcyBhbnlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdOb3QgYXNzaWduZWQnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YnAuc3RhbmRhcmQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnIHRleHQtaW5wdXQgcHktMiB0ZXh0LXJpZ2h0JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoSW5TdGF0dXMgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ09mZicgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZHV0eT8ucHVuY2hJblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGZvcm1hdERhdGVUaW1lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5LnB1bmNoSW4sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdOb3QgQXZhaWxhYmxlJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcgdGV4dC1pbnB1dCBwaGFibGV0OnByLTIuNSBweS0yIHRleHQtcmlnaHQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hPdXRTdGF0dXMgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ09mZicgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoIWR1dHkucHVuY2hPdXQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtZmlsbC1pbmhlcml0IGZvbnQtbm9ybWFsIGgtZml0JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9ja2VkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTaWduT3V0VGltZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwdW5jaE91dExhYmVsIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1NpZ24gT3V0J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3doaXRlc3BhY2Utbm93cmFwIGZsZXggaXRlbXMtY2VudGVyIHctZnVsbCBqdXN0aWZ5LWVuZCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlVGltZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXR5LnB1bmNoT3V0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHBoYWJsZXQ6cHItMi41IHJlbGFpdmUgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1lbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwdW5jaEluU3RhdHVzICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPZmYnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGR1dHk/LnB1bmNoSW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBmb3JtYXREYXRlVGltZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eS5wdW5jaEluLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnTm90IEF2YWlsYWJsZScpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoT3V0U3RhdHVzICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPZmYnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKCFkdXR5LnB1bmNoT3V0ID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LWZpbGwtaW5oZXJpdCBmb250LW5vcm1hbCBoLWZpdCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2tlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2lnbk91dFRpbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1dHksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hPdXRMYWJlbCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdTaWduIE91dCd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd3aGl0ZXNwYWNlLW5vd3JhcCBmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwganVzdGlmeS1lbmQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZVRpbWUoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHV0eS5wdW5jaE91dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1JlYWN0LkZyYWdtZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZT5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUZvb3Rlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJtYi0wIGZvbnQtc2VtaWJvbGRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1pbmltdW0gY3JldzpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1Zlc3NlbCh2ZXNzZWwpICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld0NvdW50KCkgPiAodmVzc2VsPy5tYXhQT0IgPz8gMClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3N1Y2Nlc3MnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2l6ZS1bMjVweF1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc1Zlc3NlbCh2ZXNzZWwpID8gdmVzc2VsLm1pbkNyZXcgOiAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNWZXNzZWwodmVzc2VsKSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld0NvdW50KCkgPiAodmVzc2VsPy5tYXhQT0IgPz8gMCkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzbWFsbCBjbGFzc05hbWU9XCJ0ZXh0LWRlc3RydWN0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFlvdSBoYXZlIG1vcmUgcGVvcGxlIG9uIGJvYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYW4geW91ciB2ZXNzZWwgaXMgY29uZmlndXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0byBjYXJyeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc21hbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0aW55OnctZml0IHB4LTIuNVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2NrZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZE1hbmlmZXN0fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQWRkIGNyZXdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybUZvb3Rlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvY2tlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRNYW5pZmVzdH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQWRkIGNyZXcgbWVtYmVycyB0byB0aGlzIHRyaXBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgIHtjcmV3ICYmXHJcbiAgICAgICAgICAgICAgICAgICAgbG9nQm9va0NvbmZpZyAmJlxyXG4gICAgICAgICAgICAgICAgICAgIGxvZ0Jvb2tDb25maWc/LmN1c3RvbWlzZWRMb2dCb29rQ29tcG9uZW50cz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChjb25maWc6IHsgdGl0bGU6IHN0cmluZzsgYWN0aXZlOiBib29sZWFuIH0pID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25maWcudGl0bGUgPT09ICdDcmV3IFdlbGZhcmUnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25maWcuYWN0aXZlID09PSB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0zIHNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENyZXdXZWxmYXJlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb2ZmbGluZT17b2ZmbGluZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dCb29rQ29uZmlnPXtsb2dCb29rQ29uZmlnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2tlZD17bG9ja2VkIHx8ICFlZGl0X2xvZ0Jvb2tFbnRyeX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3V2VsZmFyZUNoZWNrPXtjcmV3V2VsZmFyZUNoZWNrfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNyZXdXZWxmYXJlPXt1cGRhdGVDcmV3V2VsZmFyZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxBbGVydERpYWxvZ05ld1xyXG4gICAgICAgICAgICAgICAgb3BlbkRpYWxvZz17b3BlbkFkZENyZXdNZW1iZXJEaWFsb2d9XHJcbiAgICAgICAgICAgICAgICBzZXRPcGVuRGlhbG9nPXtzZXRvcGVuQWRkQ3Jld01lbWJlckRpYWxvZ31cclxuICAgICAgICAgICAgICAgIGhhbmRsZUNyZWF0ZT17aGFuZGxlU2F2ZX1cclxuICAgICAgICAgICAgICAgIGhhbmRsZUNhbmNlbD17aGFuZGxlQ2FuY2VsfVxyXG4gICAgICAgICAgICAgICAgaGFuZGxlRGVzdHJ1Y3RpdmVBY3Rpb249e1xyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdNYW5pZmVzdEVudHJ5LmlkID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICgpID0+IHNldE9wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZyh0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgc2hvd0Rlc3RydWN0aXZlQWN0aW9uPXtjcmV3TWFuaWZlc3RFbnRyeS5pZCA+IDB9XHJcbiAgICAgICAgICAgICAgICBkZXN0cnVjdGl2ZUFjdGlvblRleHQ9XCJEZWxldGVcIlxyXG4gICAgICAgICAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdNYW5pZmVzdEVudHJ5LmlkID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdVcGRhdGUgY3JldyBtZW1iZXInXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ0FkZCBjcmV3IG1lbWJlcidcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGFjdGlvblRleHQ9e2NyZXdNYW5pZmVzdEVudHJ5LmlkID4gMCA/ICdVcGRhdGUnIDogJ0FkZCd9XHJcbiAgICAgICAgICAgICAgICBjYW5jZWxUZXh0PVwiQ2FuY2VsXCJcclxuICAgICAgICAgICAgICAgIGNvbnRlbnRDbGFzc05hbWU9XCJtYXgtdy0yeGxcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXRvcCBnYXAtNCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtjcmV3TWVtYmVyICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlci5kYXRhPy50cmFpbmluZ1N0YXR1cz8ubGFiZWwgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdHb29kJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdzdWNjZXNzJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzaXplLTEyIGJvcmRlci0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17Y3Jld01lbWJlci5wcm9maWxlPy5hdmF0YXJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17YCR7Y3Jld01lbWJlci5wcm9maWxlPy5maXJzdE5hbWUgfHwgJyd9ICR7Y3Jld01lbWJlci5wcm9maWxlPy5zdXJuYW1lIHx8ICcnfWAudHJpbSgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlci5wcm9maWxlPy5maXJzdE5hbWUgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyLmRhdGE/LmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXIucHJvZmlsZT8uc3VybmFtZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXIuZGF0YT8uc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEgzIGNsYXNzTmFtZT1cInRleHQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jld01lbWJlcj8ucHJvZmlsZT8uZmlyc3ROYW1lIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcj8uZGF0YT8uZmlyc3ROYW1lIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlcj8ucHJvZmlsZT8uc3VybmFtZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXI/LmRhdGE/LnN1cm5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtjcmV3TWVtYmVyLnByb2ZpbGU/LmZpcnN0TmFtZSB8fCBjcmV3TWVtYmVyLmRhdGE/LmZpcnN0TmFtZSB8fCAnJ30gJHtjcmV3TWVtYmVyLnByb2ZpbGU/LnN1cm5hbWUgfHwgY3Jld01lbWJlci5kYXRhPy5zdXJuYW1lIHx8ICcnfWAudHJpbSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdNZW1iZXIgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoY3Jld01lbWJlcj8uZGF0YT8udHJhaW5pbmdTdGF0dXM/LmxhYmVsICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdHb29kJyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1kZXN0cnVjdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyYWluaW5nIGlzIG92ZXJkdWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjcmV3TWVtYmVyPy5kYXRhPy50cmFpbmluZ1N0YXR1c1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8uZHVlcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdNZW1iZXIuZGF0YS50cmFpbmluZ1N0YXR1cy5kdWVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1kZXN0cnVjdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdNZW1iZXIuZGF0YS50cmFpbmluZ1N0YXR1cy5kdWVzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZTogYW55LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleDogbnVtYmVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1kZXN0cnVjdGl2ZSB0ZXh0LWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg4oCiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRyYWluaW5nVHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5sYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYnJpZ2h0LXR1cnF1b2lzZS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyYWluaW5nIHVwIHRvIGRhdGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLVszMXB4XVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q29tYm9ib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPXsnQ3JldyBtZW1iZXInfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbkNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtjcmV3TWVtYmVyT3B0aW9ucy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG9wdGlvbjogQ3Jld09wdGlvbikgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ub3B0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogU3RyaW5nKG9wdGlvbi52YWx1ZSksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NyZXdNZW1iZXJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ3Jld01lbWJlcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPENyZXdEdXR5RHJvcGRvd25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUHJpbWFyeSBkdXR5XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdEdXR5SUQ9e051bWJlcihkdXR5Py52YWx1ZSkgfHwgMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVEdXR5fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbXVsdGk9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9mZmxpbmU9e29mZmxpbmV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoaWRlQ3JlYXRlT3B0aW9uPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHhzOmdyaWQtY29scy0yIHByLXB4IGdhcC1bMzFweF1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3B1bmNoSW5TdGF0dXMgIT09ICdPZmYnICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzaWduaW4tZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bG9naW5UaW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVMb2dpbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17cHVuY2hJbkxhYmVsIHx8ICdTaWduIEluJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlRm9ybWF0PVwiZGQgTU1NLFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2Ake3B1bmNoSW5MYWJlbCB8fCAnU2lnbiBJbid9IFRpbWVgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vZGU9XCJzaW5nbGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRldGltZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2VPblNlbGVjdD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17Q2xvY2t9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cHVuY2hPdXRTdGF0dXMgIT09ICdPZmYnICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzaWdub3V0LWRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vZGFsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2xvZ291dFRpbWUgfHwgdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVMb2dvdXR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3B1bmNoT3V0TGFiZWwgfHwgJ1NpZ24gT3V0J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17YCR7cHVuY2hPdXRMYWJlbCB8fCAnU2lnbiBPdXQnfSBUaW1lYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlPVwic2luZ2xlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZXRpbWVcIiAvLyBLZWVwIGRhdGV0aW1lIHRvIGluY2x1ZGUgdGltZSBwaWNrZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlRm9ybWF0PVwiZGQgTU1NLFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGltZUZvcm1hdD1cIkhIOm1tXCIgLy8gRXhwbGljaXRseSBzZXQgdGltZSBmb3JtYXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9zZU9uU2VsZWN0PXtmYWxzZX0gLy8gQWxsb3cgdGltZSBzZWxlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGVhcmFibGU9e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17Q2xvY2t9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHt3b3JrRGV0YWlsc1N0YXR1cyAhPT0gJ09mZicgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJ3b3JrLWRldGFpbHNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3dvcmtEZXRhaWxzTGFiZWwgfHwgJ1dvcmsgRGV0YWlscyd9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ3b3JrLWRldGFpbHNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHJlc2l6ZS1ub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHdvcmsgZGV0YWlsc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlPXtjcmV3TWFuaWZlc3RFbnRyeT8ud29ya0RldGFpbHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9BbGVydERpYWxvZ05ldz5cclxuXHJcbiAgICAgICAgICAgIDxBbGVydERpYWxvZ05ld1xyXG4gICAgICAgICAgICAgICAgb3BlbkRpYWxvZz17b3BlbkVkaXRMb2dvdXRUaW1lRGlhbG9nfVxyXG4gICAgICAgICAgICAgICAgc2V0T3BlbkRpYWxvZz17c2V0T3BlbkVkaXRMb2dvdXRUaW1lRGlhbG9nfVxyXG4gICAgICAgICAgICAgICAgaGFuZGxlQ3JlYXRlPXsoKSA9PiBoYW5kbGVTYXZlKCd1cGRhdGUnKX1cclxuICAgICAgICAgICAgICAgIGhhbmRsZUNhbmNlbD17aGFuZGxlQ2FuY2VsfVxyXG4gICAgICAgICAgICAgICAgYWN0aW9uVGV4dD17cmVzcG9uc2l2ZUxhYmVsKCdVcGRhdGUnLCAnVXBkYXRlIFRpbWUnKX1cclxuICAgICAgICAgICAgICAgIGNhbmNlbFRleHQ9XCJDYW5jZWxcIlxyXG4gICAgICAgICAgICAgICAgY29udGVudENsYXNzTmFtZT0ndG9wLVszOHN2aF0nXHJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJVcGRhdGUgc2lnbiBvdXQgdGltZVwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGVQaWNrZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbW9kYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzaWdub3V0LWRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwic2lnbm91dC1kYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e2Ake3B1bmNoT3V0TGFiZWwgfHwgJ1NpZ24gT3V0J30gVGltZWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtsb2dvdXRUaW1lIHx8IHVuZGVmaW5lZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgbW9kZT1cInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRldGltZVwiIC8vIEtlZXAgZGF0ZXRpbWUgdG8gaW5jbHVkZSB0aW1lIHBpY2tlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTG9nb3V0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRlRm9ybWF0PVwiZGQgTU1NLFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpbWVGb3JtYXQ9XCJISDptbVwiIC8vIEV4cGxpY2l0bHkgc2V0IHRpbWUgZm9ybWF0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtgJHtwdW5jaE91dExhYmVsIHx8ICdTaWduIE91dCd9IFRpbWVgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbG9zZU9uU2VsZWN0PXtmYWxzZX0gLy8gQWxsb3cgdGltZSBzZWxlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpY29uPXtDbG9ja31cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQWxlcnREaWFsb2dOZXc+XHJcblxyXG4gICAgICAgICAgICA8QWxlcnREaWFsb2dOZXdcclxuICAgICAgICAgICAgICAgIG9wZW5EaWFsb2c9e29wZW5DcmV3VHJhaW5pbmdEdWVEaWFsb2d9XHJcbiAgICAgICAgICAgICAgICBzZXRPcGVuRGlhbG9nPXtzZXRPcGVuQ3Jld1RyYWluaW5nRHVlRGlhbG9nfVxyXG4gICAgICAgICAgICAgICAgY29udGVudENsYXNzTmFtZT1cIm1heC13LXhsXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiXHJcbiAgICAgICAgICAgICAgICBjYW5jZWxUZXh0PVwiQ2FuY2VsXCJcclxuICAgICAgICAgICAgICAgIGFjdGlvblRleHQ9XCJZZXMsIENvbnRpbnVlXCJcclxuICAgICAgICAgICAgICAgIGhhbmRsZUNyZWF0ZT17KCkgPT4gc2V0T3BlbkNyZXdUcmFpbmluZ0R1ZURpYWxvZyhmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVDYW5jZWw9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBzZXRPcGVuQ3Jld1RyYWluaW5nRHVlRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXIobnVsbClcclxuICAgICAgICAgICAgICAgICAgICBzZXREdXR5KG51bGwpXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJDcmV3IG1lbWJlciB0cmFpbmluZyBzdGF0dXNcIlxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIndhcm5pbmdcIlxyXG4gICAgICAgICAgICAgICAgc2hvd0ljb24+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cD5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdNZW1iZXI/LmRhdGE/LmZpcnN0TmFtZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyPy5kYXRhPy5zdXJuYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGAke2NyZXdNZW1iZXIuZGF0YS5maXJzdE5hbWUgfHwgJyd9ICR7Y3Jld01lbWJlci5kYXRhLnN1cm5hbWUgfHwgJyd9YC50cmltKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ1RoaXMgY3JldyBtZW1iZXInfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhcyBvdmVyZHVlIHRyYWluaW5nIHNlc3Npb25zIG9uIHRoaXMgdmVzc2VsLiBUaGVzZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXNzaW9ucyBhcmU6XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7Y3Jld01lbWJlcj8uZGF0YT8udHJhaW5pbmdTdGF0dXM/LmR1ZXM/Lm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGl0ZW06IGFueSwgZHVlSW5kZXg6IG51bWJlcikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAga2V5PXtkdWVJbmRleH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Ake2l0ZW0udHJhaW5pbmdUeXBlLnRpdGxlfSAtICR7aXRlbS5zdGF0dXMubGFiZWx9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N0cm9uZz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8cD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgRG8geW91IHN0aWxsIHdhbnQgdG8gYWRkIHRoaXMgY3JldyBtZW1iZXIgdG8gdGhpc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw/XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQWxlcnREaWFsb2dOZXc+XHJcblxyXG4gICAgICAgICAgICA8QWxlcnREaWFsb2dOZXdcclxuICAgICAgICAgICAgICAgIG9wZW5EaWFsb2c9e29wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZ31cclxuICAgICAgICAgICAgICAgIHNldE9wZW5EaWFsb2c9e3NldE9wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZ31cclxuICAgICAgICAgICAgICAgIGhhbmRsZUNyZWF0ZT17aGFuZGxlQXJjaGl2ZX1cclxuICAgICAgICAgICAgICAgIGhhbmRsZUNhbmNlbD17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldE9wZW5Db25maXJtQ3Jld0RlbGV0ZURpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgICAgICAvLyBEb24ndCByZXNldCBjcmV3IG1lbWJlciBoZXJlIGFzIGl0J3MgbmVlZGVkIGZvciB0aGUgcGFyZW50IGRpYWxvZ1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIGFjdGlvblRleHQ9XCJSZW1vdmVcIlxyXG4gICAgICAgICAgICAgICAgY2FuY2VsVGV4dD1cIkNhbmNlbFwiXHJcbiAgICAgICAgICAgICAgICBjb250ZW50Q2xhc3NOYW1lPVwibWF4LXctbWRcIlxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIndhcm5pbmdcIlxyXG4gICAgICAgICAgICAgICAgc2hvd0ljb25cclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiUmVtb3ZlIGNyZXcgbWVtYmVyXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gcmVtb3ZleycgJ31cclxuICAgICAgICAgICAgICAgICAgICB7Y3Jld01lbWJlcj8uZGF0YT8uZmlyc3ROYW1lIHx8IGNyZXdNZW1iZXI/LmRhdGE/LnN1cm5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtjcmV3TWVtYmVyLmRhdGEuZmlyc3ROYW1lIHx8ICcnfSAke2NyZXdNZW1iZXIuZGF0YS5zdXJuYW1lIHx8ICcnfWAudHJpbSgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RoaXMgY3JldyBtZW1iZXInfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgZnJvbSB0aGlzIHRyaXAgbWFuaWZlc3Q/XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9BbGVydERpYWxvZ05ldz5cclxuICAgICAgICA8Lz5cclxuICAgIClcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZUxhenlRdWVyeSIsInVzZU11dGF0aW9uIiwiVXBkYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbiIsIkNyZWF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24iLCJDcmVhdGVDcmV3V2VsZmFyZV9Mb2dCb29rRW50cnlTZWN0aW9uIiwiRGVsZXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbnMiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlSGVhZCIsIlRhYmxlSGVhZGVyIiwiVGFibGVSb3ciLCJkYXlqcyIsIkNSRVdfRFVUWSIsIkNSRVdfTElTVF9XSVRIT1VUX1RSQUlOSU5HX1NUQVRVUyIsIkNSRVdfTElTVCIsIkNSRVdfREVUQUlMX1dJVEhfVFJBSU5JTkdfU1RBVFVTIiwiQ1JFV19ERVRBSUxfQllfSUQiLCJDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uIiwiQ3Jld1dlbGZhcmUiLCJ1c2VTZWFyY2hQYXJhbXMiLCJHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyIsInVzZVRvYXN0IiwiZm9ybWF0REJEYXRlVGltZSIsIlNlYUxvZ3NNZW1iZXJNb2RlbCIsIkNyZXdEdXR5TW9kZWwiLCJDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uTW9kZWwiLCJnZW5lcmF0ZVVuaXF1ZUlkIiwiQ3Jld1dlbGZhcmVfTG9nQm9va0VudHJ5U2VjdGlvbk1vZGVsIiwiZ2V0UGVybWlzc2lvbnMiLCJoYXNQZXJtaXNzaW9uIiwiZ2V0RmllbGROYW1lIiwiU0xBTExfTG9nQm9va0ZpZWxkcyIsImlzRW1wdHkiLCJMYWJlbCIsIlRleHRhcmVhIiwiQnV0dG9uIiwiQ29tYm9ib3giLCJDcmV3RHV0eURyb3Bkb3duIiwiQ2xvY2siLCJJbmZvSWNvbiIsIlBvcG92ZXIiLCJQb3BvdmVyQ29udGVudCIsIlBvcG92ZXJUcmlnZ2VyIiwiRGF0ZVBpY2tlciIsIkFsZXJ0RGlhbG9nTmV3IiwiSDIiLCJIMyIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQXZhdGFySW1hZ2UiLCJnZXRDcmV3SW5pdGlhbHMiLCJCYWRnZSIsImNuIiwiaXNWZXNzZWwiLCJDYXJkIiwidXNlQnJlYWtwb2ludHMiLCJmb3JtYXREYXRlVGltZSIsIkZvcm1Gb290ZXIiLCJyZXNwb25zaXZlTGFiZWwiLCJDcmV3IiwiY3Jld1NlY3Rpb25zIiwiYWxsQ3JldyIsImxvZ0Jvb2tFbnRyeUlEIiwibG9ja2VkIiwibG9nQm9va0NvbmZpZyIsInNldENyZXdNZW1iZXJzIiwiY3Jld1dlbGZhcmVDaGVjayIsInVwZGF0ZUNyZXdXZWxmYXJlIiwidmVzc2VsIiwibWFzdGVySUQiLCJsb2dFbnRyeVNlY3Rpb25zIiwib2ZmbGluZSIsImNyZXdNZW1iZXJzTGlzdCIsImNyZXdNZW1iZXIiLCJzZWFMb2dzTWVtYmVyTW9kZWwiLCJjcmV3RHV0eU1vZGVsIiwibGJDcmV3TW9kZWwiLCJsYldlbGZhcmVNb2RlbCIsImFsbFZlc3NlbENyZXdzIiwic2V0QWxsVmVzc2VsQ3Jld3MiLCJhbGxEdXRpZXMiLCJzZXRBbGxEdXRpZXMiLCJzZWFyY2hQYXJhbXMiLCJ2ZXNzZWxJRCIsImdldCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImxvYWRlZCIsInNldExvYWRlZCIsInNldENyZXdNZW1iZXIiLCJkdXR5Iiwic2V0RHV0eSIsImxvZ2luVGltZSIsInNldExvZ2luVGltZSIsIkRhdGUiLCJsb2dvdXRUaW1lIiwic2V0TG9nb3V0VGltZSIsImR1dGllcyIsInNldER1dGllcyIsImNyZXciLCJzZXRDcmV3IiwiY3Jld0NvbmZpZyIsInNldENyZXdDb25maWciLCJwdW5jaEluU3RhdHVzIiwic2V0UHVuY2hJblN0YXR1cyIsInB1bmNoSW5MYWJlbCIsInNldFB1bmNoSW5MYWJlbCIsInB1bmNoT3V0U3RhdHVzIiwic2V0UHVuY2hPdXRTdGF0dXMiLCJwdW5jaE91dExhYmVsIiwic2V0UHVuY2hPdXRMYWJlbCIsIndvcmtEZXRhaWxzU3RhdHVzIiwic2V0V29ya0RldGFpbHNTdGF0dXMiLCJ3b3JrRGV0YWlsc0xhYmVsIiwic2V0V29ya0RldGFpbHNMYWJlbCIsImNyZXdNYW5pZmVzdEVudHJ5Iiwic2V0Q3Jld01hbmlmZXN0RW50cnkiLCJvcGVuQWRkQ3Jld01lbWJlckRpYWxvZyIsInNldG9wZW5BZGRDcmV3TWVtYmVyRGlhbG9nIiwidG9hc3QiLCJvcGVuRWRpdExvZ291dFRpbWVEaWFsb2ciLCJzZXRPcGVuRWRpdExvZ291dFRpbWVEaWFsb2ciLCJjcmV3TWVtYmVyT3B0aW9ucyIsInNldENyZXdNZW1iZXJPcHRpb25zIiwib3BlbkNyZXdUcmFpbmluZ0R1ZURpYWxvZyIsInNldE9wZW5DcmV3VHJhaW5pbmdEdWVEaWFsb2ciLCJvcGVuQ29uZmlybUNyZXdEZWxldGVEaWFsb2ciLCJzZXRPcGVuQ29uZmlybUNyZXdEZWxldGVEaWFsb2ciLCJwZXJtaXNzaW9ucyIsInNldFBlcm1pc3Npb25zIiwiZWRpdF9sb2dCb29rRW50cnkiLCJzZXRFZGl0X2xvZ0Jvb2tFbnRyeSIsImFsbE1lbWJlcnMiLCJzZXRBbGxNZW1iZXJzIiwiYnAiLCJpbml0X3Blcm1pc3Npb25zIiwicHJvY2VzcyIsImVudiIsIkVESVRfTE9HQk9PS0VOVFJZIiwiY3JlYXRlT2ZmbGluZUNyZXdXZWxmYXJlQ2hlY2siLCJkZWxheSIsIm1zIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiaWQiLCJkYXRhIiwic2F2ZSIsImZpdG5lc3MiLCJpbVNhZmUiLCJzYWZldHlBY3Rpb25zIiwid2F0ZXJRdWFsaXR5IiwiX190eXBlbmFtZSIsIkFycmF5IiwiaXNBcnJheSIsImhhc0NyZXdXZWxmYXJlIiwiZmlsdGVyIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImxlbmd0aCIsImNyZWF0ZUNyZXdXZWxmYXJlQ2hlY2tMb2FkaW5nIiwiY3JlYXRlQ3Jld1dlbGZhcmVDaGVjayIsInZhcmlhYmxlcyIsImlucHV0IiwicXVlcnlDcmV3RGV0YWlsIiwiZmV0Y2hQb2xpY3kiLCJvbkNvbXBsZXRlZCIsInJlc3BvbnNlIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsInF1ZXJ5VmVzc2VsQ3Jld3MiLCJyZWFkU2VhTG9nc01lbWJlcnMiLCJub2RlcyIsIml0ZW0iLCJtYXAiLCJtZW1iZXIiLCJsYWJlbCIsImZpcnN0TmFtZSIsInN1cm5hbWUiLCJ0cmltIiwidmFsdWUiLCJwcm9maWxlIiwiYXZhdGFyIiwicHJvZmlsZUltYWdlIiwibWVtYmVycyIsInNvbWUiLCJwdW5jaE91dCIsIm1lbWJlck9wdGlvbnMiLCJpbmNsdWRlcyIsImxvYWRWZXNzZWxDcmV3cyIsImdldEJ5VmVzc2VsSWQiLCJjcmV3V2l0aFRyYWluaW5nIiwidmVoaWNsZXMiLCJlcSIsImlzQXJjaGl2ZWQiLCJsb2FkRHV0aWVzIiwiZ3JvdXBDcmV3RHV0aWVzQnlNZW1iZXIiLCJjcmV3RGF0YSIsImdyb3VwZWRDcmV3Iiwic29ydGVkQ3Jld0RhdGEiLCJzb3J0IiwiYSIsImIiLCJ0aW1lQSIsInB1bmNoSW4iLCJnZXRUaW1lIiwidGltZUIiLCJhY3RpdmVDcmV3RGF0YSIsImFyY2hpdmVkIiwiZm9yRWFjaCIsImNyZXdNZW1iZXJJZCIsImNyZXdNZW1iZXJJRCIsImV4aXN0aW5nRHV0aWVzIiwiaXNEdXBsaWNhdGVCeUlkIiwiZXhpc3RpbmdEdXR5IiwicHVzaCIsImR1dHlQZXJmb3JtZWQiLCJ3b3JrRGV0YWlscyIsImR1dHlQZXJmb3JtZWRJRCIsImlzRHVwbGljYXRlRHV0eVR5cGUiLCJPYmplY3QiLCJ2YWx1ZXMiLCJwcm9jZXNzZWRDcmV3U2VjdGlvbnMiLCJjcmV3TWVtYmVyV2l0aFRyYWluaW5nU3RhdHVzIiwidXBkYXRlZERhdGEiLCJleGlzdGluZ0NyZXdNYXAiLCJyZWR1Y2UiLCJoYXNFeGlzdGluZ0R1dGllcyIsImV4aXN0aW5nTWVtYmVyIiwiZHV0eUV4aXN0cyIsInVwZGF0ZWREdXRpZXMiLCJncm91cGVkQ3Jld1NlY3Rpb25zIiwiZ2V0QWxsIiwiYWN0aXZlRHV0aWVzIiwicXVlcnlEdXRpZXMiLCJoYW5kbGVTZXRDcmV3Q29uZmlnIiwiY3VzdG9taXNlZExvZ0Jvb2tDb21wb25lbnRzIiwiY3Jld01lbWJlcnNDb25maWdzIiwiY29uZmlnIiwidGl0bGUiLCJjdXN0b21pc2VkQ29tcG9uZW50RmllbGRzIiwiZmllbGQiLCJmaWVsZE5hbWUiLCJzdGF0dXMiLCJzb3J0ZWRDb25maWdzIiwicGFyc2VJbnQiLCJoYW5kbGVTZXRTdGF0dXMiLCJjcmV3TWVtYmVyQ29tcG9uZW50cyIsImNyZXdNZW1iZXJDb21wb25lbnQiLCJjcmV3TWVtYmVyRmllbGQiLCJmaW5kIiwicHJpbWFyeUR1dHlGaWVsZCIsInB1bmNoSW5Db25maWciLCJwdW5jaEluRmllbGQiLCJjdXN0b21UaXRsZSIsImN1c3RvbWlzZWRGaWVsZFRpdGxlIiwiZmllbGROYW1lVmFsdWUiLCJwdW5jaE91dENvbmZpZyIsInB1bmNoT3V0RmllbGQiLCJ3b3JrRGV0YWlsc0NvbmZpZyIsIndvcmtEZXRhaWxzRmllbGQiLCJyZWFkQ3Jld0R1dGllcyIsImhhbmRsZUxvZ2luIiwiZGF0ZSIsImN1cnJlbnRUaW1lIiwidmFsaWREYXRlIiwiaXNOYU4iLCJwcmV2IiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiaGFuZGxlTG9nb3V0IiwiZ2V0SG91cnMiLCJnZXRNaW51dGVzIiwibm93Iiwic2V0SG91cnMiLCJzZXRNaW51dGVzIiwiZGF5anNEYXRlIiwiaXNCZWZvcmUiLCJoYW5kbGVBZGRNYW5pZmVzdCIsInNpZ25lZEluQ3Jld01lbWJlcklEcyIsIlNldCIsImhhc0FjdGl2ZVNoaWZ0IiwiYWRkIiwiYXZhaWxhYmxlQ3Jld01lbWJlcnMiLCJoYXMiLCJmaWx0ZXJlZENyZXdPcHRpb25zIiwiaGFuZGxlRWRpdE1hbmlmZXN0IiwibWVtYmVyRGF0YSIsImR1dHlUb0VkaXQiLCJjcmV3TWVtYmVyV2l0aFByb2ZpbGUiLCJzZWxlY3RlZER1dHkiLCJtZW1iZXJEdXR5IiwiaGFuZGxlU2lnbk91dFRpbWUiLCJpc05lc3RlZER1dHkiLCJwYXJlbnRNZW1iZXIiLCJjIiwiZCIsImhhbmRsZUNyZXdNZW1iZXIiLCJzZWxlY3RlZCIsInJlYWRPbmVTZWFMb2dzTWVtYmVyIiwidHJhaW5pbmdTdGF0dXMiLCJwcmltYXJ5RHV0eUlEIiwiY3Jld0R1dHkiLCJuZXdEdXR5IiwiaGFuZGxlRHV0eSIsImhhbmRsZUNhbmNlbCIsImhhbmRsZVNhdmUiLCJjYWxsQnkiLCJ3b3JrRGV0YWlsc0VsZW1lbnQiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwiY3Jld0lkcyIsIkJvb2xlYW4iLCJnZXRCeUlkcyIsInByb2Nlc3NlZERhdGEiLCJncm91cGVkRGF0YSIsInVwZGF0ZUNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24iLCJ1bmlxdWVJZCIsInNlbGVjdGVkTWVtYmVyIiwibmV3Q3Jld0VudHJ5IiwiZXhpc3RpbmdDcmV3IiwiZXhpc3RpbmdDcmV3TWVtYmVySW5kZXgiLCJmaW5kSW5kZXgiLCJ1cGRhdGVkQ3Jld0RhdGEiLCJleGlzdGluZ0NyZXdNZW1iZXIiLCJjb25jYXQiLCJwcm9jZXNzZWREYkRhdGEiLCJncm91cGVkRGJEYXRhIiwiY3JlYXRlQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbiIsIkpTT04iLCJwYXJzZSIsInN0cmluZ2lmeSIsImZvdW5kQW5kVXBkYXRlZCIsImkiLCJqIiwiYXBwZW5kRGF0YSIsInNlYXJjaEZpbHRlciIsImluIiwiZ2V0U2VjdGlvbkNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24iLCJsb2FkaW5nIiwiY3JlYXRlQ3Jld1dlbGZhcmVfTG9nQm9va0VudHJ5U2VjdGlvbiIsIm0iLCJyZWFkQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbnMiLCJleGlzdGluZ0R1dHlJbmRleCIsImhhbmRsZUFyY2hpdmUiLCJyZXN1bHQiLCJkZWxldGUiLCJkZWxldGVDcmV3TWVtYmVyc0xvZ0Jvb2tFbnRyeVNlY3Rpb25zIiwiaWRzIiwiY3Jld0NvdW50IiwiY291bnQiLCJkaXYiLCJzdGFuZGFyZCIsImJyIiwiaGFzTXVsdGlwbGVEdXRpZXMiLCJhZGRpdGlvbmFsRHV0aWVzIiwic2xpY2UiLCJmaXJzdER1dHlUaXRsZSIsImN1cnJlbnREdXR5VGl0bGUiLCJGcmFnbWVudCIsImFyaWEtZGlzYWJsZWQiLCJvbkNsaWNrIiwiZSIsInRhcmdldCIsIkhUTUxFbGVtZW50IiwiY2xvc2VzdCIsInNpemUiLCJzcGFuIiwic3RvcFByb3BhZ2F0aW9uIiwiZGlzYWJsZWQiLCJpbmRleCIsIm1heFBPQiIsIm1pbkNyZXciLCJzbWFsbCIsImFjdGl2ZSIsIm9wZW5EaWFsb2ciLCJzZXRPcGVuRGlhbG9nIiwiaGFuZGxlQ3JlYXRlIiwiaGFuZGxlRGVzdHJ1Y3RpdmVBY3Rpb24iLCJ1bmRlZmluZWQiLCJzaG93RGVzdHJ1Y3RpdmVBY3Rpb24iLCJkZXN0cnVjdGl2ZUFjdGlvblRleHQiLCJhY3Rpb25UZXh0IiwiY2FuY2VsVGV4dCIsImNvbnRlbnRDbGFzc05hbWUiLCJzcmMiLCJhbHQiLCJwIiwiZHVlcyIsInVsIiwiZHVlIiwibGkiLCJ0cmFpbmluZ1R5cGUiLCJtb2RhbCIsImJ1dHRvbkNsYXNzTmFtZSIsIm9wdGlvbnMiLCJvcHRpb24iLCJTdHJpbmciLCJvbkNoYW5nZSIsImNyZXdEdXR5SUQiLCJOdW1iZXIiLCJtdWx0aSIsImhpZGVDcmVhdGVPcHRpb24iLCJkYXRlRm9ybWF0IiwicGxhY2Vob2xkZXIiLCJtb2RlIiwidHlwZSIsImNsb3NlT25TZWxlY3QiLCJpY29uIiwidGltZUZvcm1hdCIsImNsZWFyYWJsZSIsImh0bWxGb3IiLCJyb3dzIiwiZGVmYXVsdFZhbHVlIiwibmFtZSIsInNob3dJY29uIiwiZHVlSW5kZXgiLCJzdHJvbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/crew.tsx\n"));

/***/ })

});