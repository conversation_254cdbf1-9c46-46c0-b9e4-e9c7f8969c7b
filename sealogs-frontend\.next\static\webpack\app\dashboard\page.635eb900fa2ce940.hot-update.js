"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/dashboard/overview-components/training/training.tsx":
/*!************************************************************************!*\
  !*** ./src/app/ui/dashboard/overview-components/training/training.tsx ***!
  \************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Training; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/css */ \"(app-pages-browser)/./node_modules/.pnpm/swiper@11.2.4/node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/css/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/swiper@11.2.4/node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/css/pagination */ \"(app-pages-browser)/./node_modules/.pnpm/swiper@11.2.4/node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_scrollbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/css/scrollbar */ \"(app-pages-browser)/./node_modules/.pnpm/swiper@11.2.4/node_modules/swiper/modules/scrollbar.css\");\n/* harmony import */ var swiper_css_free_mode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/free-mode */ \"(app-pages-browser)/./node_modules/.pnpm/swiper@11.2.4/node_modules/swiper/modules/free-mode.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _components_horizontal_bar_chart__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/horizontal-bar-chart */ \"(app-pages-browser)/./src/components/horizontal-bar-chart.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _crew_training_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../crew-training/hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/dashboard/overview-components/training/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Training() {\n    _s();\n    const [trainingSessionUpcomingDuesSummary, setTrainingSessionUpcomingDuesSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingDues, setDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [scheduledTrainingList, setScheduledTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    let memberID = 0;\n    if ( true && typeof window.localStorage !== \"undefined\") {\n        var _localStorage_getItem;\n        memberID = +((_localStorage_getItem = localStorage.getItem(\"userId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n    }\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium truncate hover:text-primary\",\n                                children: due.vessel.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[10px] text-curious-blue-400 uppercase\",\n                                children: due.trainingType.title\n                            }, due.trainingType.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"due\",\n            header: \"\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const due = row.original;\n                const dueDate = new Date(due.dueDate);\n                const today = new Date();\n                dueDate.setHours(0, 0, 0, 0);\n                today.setHours(0, 0, 0, 0);\n                const diffTime = today.getTime() - dueDate.getTime();\n                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end items-end text-nowrap\",\n                    children: diffDays > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert border rounded-md\",\n                        children: [\n                            diffDays + \" \",\n                            \"days ago\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-foreground\",\n                        children: [\n                            \"Due -\",\n                            \" \" + diffDays * -1 + \" \",\n                            \"days\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 21\n                }, this);\n            }\n        }\n    ]);\n    const [readTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_14__.ReadTrainingSessionDues, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const dueWithStatus = data.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_12__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        members: mergedMembers\n                    };\n                });\n                const overDueOnly = dueWithStatus.filter((item)=>{\n                    return item.status.isOverdue;\n                });\n                // setTrainingSessionUpcomingDues(mergedDues)\n                setTrainingSessionUpcomingDuesSummary(mergedDues.splice(0, 5));\n                setDues(overDueOnly);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n        const now = new Date();\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();\n        const startOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString();\n        await queryTrainingList({\n            variables: {\n                limit: 1000,\n                offset: 0,\n                filter: {\n                    date: {\n                        gte: startOfMonth,\n                        lt: startOfNextMonth\n                    }\n                }\n            }\n        });\n    };\n    const [queryTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_14__.ReadTrainingSessions, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            if (data) {\n                // setAllTrainingList(data)\n                const now = new Date();\n                const completed = data.filter((item)=>new Date(item.date) < now);\n                setCompletedTrainingList(completed);\n                setScheduledTrainingList(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * 100,\n                limit: 100\n            }\n        });\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_crew_training_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: setOverdueSwitcher\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingSessionDues(filter);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const chartData = [\n        {\n            title: \"scheduled\",\n            amount: scheduledTrainingList.length,\n            fill: \"var(--color-scheduled)\",\n            stroke: \"hsl(207, 86%, 39%)\"\n        },\n        {\n            title: \"completed\",\n            amount: completedTrainingList.length,\n            fill: \"var(--color-completed)\",\n            stroke: \"hsl(176, 97%, 26%)\"\n        },\n        {\n            title: \"overdue\",\n            amount: trainingDues.length,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 83%, 54%)\"\n        }\n    ];\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        scheduled: {\n            label: \"Scheduled\",\n            color: \"var(--chart-3)\"\n        },\n        completed: {\n            label: \"Completed\",\n            color: \"var(--chart-5)\"\n        },\n        overdue: {\n            label: \"Overdue\",\n            color: \"var(--chart-1)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex py-3 items-baseline gap;2 phablet:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_8__.SealogsTrainingIcon, {\n                        className: \"h-12 w-12 ring-1 p-1 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        href: \"/crew-training\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_11__.H1, {\n                            children: \"Training / drills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                lineNumber: 326,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_horizontal_bar_chart__WEBPACK_IMPORTED_MODULE_10__.HorizontalBarChartComponent, {\n                chartConfig: chartConfig,\n                chartData: chartData,\n                cardTitle: \"Training completed this month\",\n                cardInfo: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl font-black mb-1\",\n                            children: scheduledTrainingList.length === 0 ? \"0%\" : (completedTrainingList.length / scheduledTrainingList.length * 100).toFixed(2) + \"%\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 25\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-curious-blue-400 text-sm\",\n                            children: [\n                                scheduledTrainingList.length,\n                                \" training / drills due this month\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 25\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                lineNumber: 334,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-5\",\n                children: [\n                    trainingSessionUpcomingDuesSummary && trainingSessionUpcomingDuesSummary.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                        columns: columns,\n                        data: trainingSessionUpcomingDuesSummary,\n                        showToolbar: false,\n                        className: \"p-0 pt-8 border-0 shadow-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-between items-center gap-2 p-2 pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"!w-[75px] h-auto\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 147 147.01\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                            fill: \"#052350\",\n                                            fillRule: \"evenodd\",\n                                            opacity: \".97\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                            fill: \"#024450\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                            fill: \"#052451\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-foreground\",\n                                children: \"WOW! Look at that. You are ship-shaped and trained to the gills. Great job!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: \"/crew-training\",\n                            className: \"text-accent-foreground uppercase group hover:text-primary hover:text-curious-blue-400 text-xs\",\n                            children: [\n                                \"See all\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden md:inline-block group-hover:text-curious-blue-400\",\n                                    children: \"\\xa0crew\\xa0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 25\n                                }, this),\n                                \" \",\n                                \"training\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n                lineNumber: 356,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\training\\\\training.tsx\",\n        lineNumber: 325,\n        columnNumber: 9\n    }, this);\n}\n_s(Training, \"oKW4Or7dS8gvpOOc9dRoEOVX614=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useLazyQuery,\n        _crew_training_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters\n    ];\n});\n_c = Training;\nvar _c;\n$RefreshReg$(_c, \"Training\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/dashboard/overview-components/training/training.tsx\n"));

/***/ })

});