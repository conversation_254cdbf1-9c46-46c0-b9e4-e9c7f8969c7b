"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    var _this = undefined;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    // Helper function to reset date value based on mode\n    const getEmptyDateValue = ()=>mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined;\n    // Helper function to clear all date and time state\n    const clearAllState = ()=>{\n        setDateValue(getEmptyDateValue());\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Helper function to render icon consistently\n    const renderIcon = function() {\n        let className = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"text-muted-foreground\";\n        if (!icon) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"text-neutral-400\", className)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 223,\n            columnNumber: 17\n        }, _this);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon)) {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-5 h-5\", className)\n            });\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(className)\n        });\n    };\n    // Helper function to create Date from time object\n    const createDateFromTime = (timeObj)=>{\n        const date = new Date();\n        date.setHours(timeObj.hour, timeObj.minute, 0, 0);\n        return date;\n    };\n    // Helper function to get time picker value with confirmation logic\n    const getTimePickerValue = (currentTime, pendingDate)=>{\n        let resolvedTime = currentTime;\n        // Always preserve existing time when using confirmation\n        if (confirmSelection && pendingDate && !isNaN(pendingDate.getTime())) {\n            // If there's an existing time, always preserve it\n            if (currentTime) {\n                resolvedTime = currentTime;\n            } else {\n                // Only if there's no existing time, use time from pending date\n                const pendingHours = pendingDate.getHours();\n                const pendingMinutes = pendingDate.getMinutes();\n                if (pendingHours !== 0 || pendingMinutes !== 0) {\n                    resolvedTime = {\n                        hour: pendingHours,\n                        minute: pendingMinutes\n                    };\n                }\n            }\n        }\n        if (resolvedTime) {\n            return createDateFromTime(resolvedTime);\n        }\n        // Fallback to current time but DON'T modify existing time state\n        const now = new Date();\n        return now;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(getEmptyDateValue());\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_11__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(getEmptyDateValue());\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state with preserved time\n            if (confirmSelection) {\n                let preservedFrom = from;\n                let preservedTo = to;\n                // Preserve existing time when setting pending selection\n                if (from && shouldIncludeTime && time) {\n                    const fromWithTime = applyTime(from, time);\n                    if (fromWithTime) {\n                        preservedFrom = fromWithTime;\n                    }\n                }\n                if (to && shouldIncludeTime && toTime) {\n                    const toWithTime = applyTime(to, toTime);\n                    if (toWithTime) {\n                        preservedTo = toWithTime;\n                    }\n                }\n                setPendingSelection({\n                    from: preservedFrom,\n                    to: preservedTo\n                });\n            } else {\n                finalizeSelection({\n                    from,\n                    to\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state with preserved time\n            if (confirmSelection) {\n                let preservedDate = singleDate;\n                // Preserve existing time when setting pending selection\n                if (shouldIncludeTime && time) {\n                    const dateWithTime = applyTime(singleDate, time);\n                    if (dateWithTime) {\n                        preservedDate = dateWithTime;\n                    }\n                }\n                setPendingSelection(preservedDate);\n            } else {\n                finalizeSelection(singleDate, closeOnSelect && !shouldIncludeTime);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        clearAllState();\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        clearAllState();\n        setOpen(false) // Close the popover after clearing\n        ;\n    };\n    // Helper function to finalize selection (used by both direct selection and confirmation)\n    const finalizeSelection = function(selection) {\n        let closePopover = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (mode === \"range\") {\n            const { from, to } = selection;\n            setDateValue({\n                from,\n                to\n            });\n            // NEVER change existing time when selecting new dates - preserve exactly as is\n            const currentTime = time;\n            const currentToTime = toTime;\n            const rangeResult = {\n                startDate: shouldIncludeTime ? applyTime(from, currentTime) : from,\n                endDate: shouldIncludeTime ? applyTime(to, currentToTime) : to\n            };\n            console.log(\"DateRange finalizeSelection - range mode result:\", rangeResult);\n            onChange(rangeResult);\n        } else {\n            const singleDate = selection;\n            setDateValue(singleDate);\n            // NEVER change existing time when selecting new dates - preserve exactly as is\n            const currentTime = time;\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            console.log(\"DateRange finalizeSelection - single mode result:\", result);\n            onChange(result);\n        }\n        if (closePopover) {\n            setOpen(false);\n            setPendingSelection(undefined);\n        }\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        console.log(\"DateRange handleConfirm - pendingSelection:\", pendingSelection);\n        finalizeSelection(pendingSelection, true);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    // Detect mobile devices (below md breakpoint)\n    const isMobile = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_15__.useMediaQuery)(\"(max-width: 1023px)\");\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: renderIcon(\"mr-[8.5px] w-5 h-5 text-neutral-400\"),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 657,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 700,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 709,\n            columnNumber: 13\n        }, undefined);\n    };\n    // Shared content component for both Popover and Dialog\n    const renderDatePickerContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                        autoFocus: true,\n                        mode: \"range\",\n                        month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                        captionLayout: \"dropdown\",\n                        selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                        onSelect: (value)=>{\n                            // Ensure we maintain the date format when a date is unselected\n                            handleValueChange(value);\n                        },\n                        numberOfMonths: numberOfMonths,\n                        showWeekNumber: showWeekNumbers,\n                        disabled: validation ? (date)=>!validateDate(date) : undefined\n                    }, \"range\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 21\n                    }, undefined),\n                    shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-t border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_6__.TimePicker, {\n                                value: getTimePickerValue(time, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from),\n                                toValue: getTimePickerValue(toTime, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to),\n                                onChange: handleTimeChange,\n                                onToChange: (date)=>handleTimeChange(date, true),\n                                // Always enable time picker regardless of whether a date is selected\n                                disabled: false,\n                                className: \"w-full\",\n                                mode: timeMode,\n                                label: timeMode === \"range\" ? \"Time Range\" : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 747,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 25\n                    }, undefined),\n                    confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                className: \"my-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                children: [\n                                    clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-fit\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClearInPopover,\n                                            iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            className: \"w-fit\",\n                                            \"aria-label\": \"Clear date range\",\n                                            children: \"Clear\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleConfirm,\n                                        disabled: !pendingSelection,\n                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: confirmButtonText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                        autoFocus: true,\n                        mode: \"single\",\n                        month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                        selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                        onSelect: (value)=>{\n                            // Ensure we maintain the date format when a date is unselected\n                            handleValueChange(value);\n                        },\n                        captionLayout: \"dropdown\",\n                        numberOfMonths: numberOfMonths,\n                        showWeekNumber: showWeekNumbers,\n                        disabled: validation ? (date)=>!validateDate(date) : undefined\n                    }, \"single\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 802,\n                        columnNumber: 21\n                    }, undefined),\n                    shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-t border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_6__.TimePicker, {\n                                value: getTimePickerValue(time, pendingSelection instanceof Date ? pendingSelection : undefined),\n                                toValue: getTimePickerValue(toTime, undefined),\n                                onChange: handleTimeChange,\n                                onToChange: (date)=>handleTimeChange(date, true),\n                                // Always enable time picker regardless of whether a date is selected\n                                disabled: false,\n                                className: \"w-full\",\n                                mode: timeMode,\n                                label: timeMode === \"range\" ? \"Time Range\" : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 830,\n                        columnNumber: 25\n                    }, undefined),\n                    confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                className: \"my-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                children: [\n                                    clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-fit\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClearInPopover,\n                                            iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            \"aria-label\": \"Clear date\",\n                                            children: \"Clear\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleConfirm,\n                                        disabled: !pendingSelection,\n                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(clearable ? \"\" : \"w-full\"),\n                                        children: confirmButtonText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 801,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false);\n    // Shared trigger button component\n    const renderTriggerButton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    id: \"date\",\n                    variant: \"outline\",\n                    disabled: disabled,\n                    iconLeft: renderIcon(),\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                    ...buttonProps,\n                    type: \"button\",\n                    children: renderButtonLabel()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 892,\n                    columnNumber: 13\n                }, undefined),\n                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"button\",\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                    onClick: handleClear,\n                    \"aria-label\": \"Clear date\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 16,\n                        className: \"text-neutral-400 hover:text-background0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 915,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 908,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 891,\n            columnNumber: 9\n        }, undefined);\n    const handleOpenChange = (isOpen)=>{\n        // When opening, initialize pendingSelection with the current value\n        if (isOpen && !pendingSelection && dateValue) {\n            setPendingSelection(dateValue);\n        }\n        setOpen(isOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 935,\n                columnNumber: 17\n            }, undefined),\n            isMobile ? // Mobile: Use Dialog\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: open,\n                onOpenChange: handleOpenChange,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTrigger, {\n                        asChild: true,\n                        children: renderTriggerButton()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 942,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                        className: \"gap-0 p-0 w-fit flex flex-col rounded-md items-center\",\n                        children: renderDatePickerContent()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 945,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 941,\n                columnNumber: 17\n            }, undefined) : // Desktop: Use Popover\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: handleOpenChange,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: renderTriggerButton()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 955,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: renderDatePickerContent()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 958,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 951,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 933,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"uk5gxya5lBq7tVRFEk0QU0HD/+s=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_15__.useMediaQuery\n    ];\n});\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});