"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/dashboard/overview-components/vessels/vessels.tsx":
/*!**********************************************************************!*\
  !*** ./src/app/ui/dashboard/overview-components/vessels/vessels.tsx ***!
  \**********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Vessels; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _vessel_status_chart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../vessel-status-chart */ \"(app-pages-browser)/./src/app/ui/dashboard/overview-components/vessel-status-chart/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_subMonths_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=subMonths!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/subMonths.mjs\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _vessels_components_table_action_column__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../vessels/components/table-action-column */ \"(app-pages-browser)/./src/app/ui/vessels/components/table-action-column.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/dashboard/overview-components/vessels/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Vessels() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [vesselList, setVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredVesselList, setFilteredVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentDepartment, setCurrentDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [displayEditStatus, setDisplayEditStatus] = useState(false)\n    // const [vessel, setVessel] = useState<any>(false)\n    const [querySeaLogsMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_9__.ReadOneSeaLogsMember, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneSeaLogsMember;\n            if (data) {\n                localStorage.setItem(\"timezone\", data.client.hqAddress.timeZone || \"Pacific/Auckland\");\n                const departmentFlatMap = data.departments.nodes.flatMap((department)=>department.basicComponents.nodes);\n                setCurrentDepartment(departmentFlatMap);\n                if (departmentFlatMap.length === 0) {\n                    setCurrentDepartment(true);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembers error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        querySeaLogsMembers({\n            variables: {\n                filter: {\n                    id: {\n                        eq: +((_localStorage_getItem = localStorage.getItem(\"userId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0)\n                    }\n                }\n            }\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentDepartment && vesselList) {\n            if (currentDepartment === true || localStorage.getItem(\"useDepartment\") !== \"true\") {\n                setFilteredVesselList(vesselList);\n            } else {\n                setFilteredVesselList(vesselList.filter((vessel)=>currentDepartment.some((department)=>department.id === vessel.id)));\n                setFilteredVesselList(vesselList);\n            }\n        }\n    }, [\n        currentDepartment,\n        vesselList\n    ]);\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>vessel.showOnDashboard);\n        setVesselList(activeVessels);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_9__.ReadDashboardData, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: async (queryVesselResponse)=>{\n            if (queryVesselResponse.readDashboardData && \"object\" !== \"undefined\") {\n                var _response_data_readVessels, _response_data;\n                const vessels = queryVesselResponse.readDashboardData[0].vessels;\n                const vesselIDs = vessels.map((item)=>item.id);\n                let response = [];\n                if (vesselIDs.length > 0) {\n                // response = await getVessselsWithLatestStatus({\n                //     variables: {\n                //         vesselFilter: {\n                //             id: { in: vesselIDs },\n                //         },\n                //         filter: { archived: { eq: false } },\n                //     },\n                // })\n                }\n                var _response_data_readVessels_nodes;\n                const vesselsWithLatestStatus = (_response_data_readVessels_nodes = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_readVessels = _response_data.readVessels) === null || _response_data_readVessels === void 0 ? void 0 : _response_data_readVessels.nodes) !== null && _response_data_readVessels_nodes !== void 0 ? _response_data_readVessels_nodes : [];\n                // Loop through vessels and save the tasksDue and trainingsDue properties to localStorage with this format for the keys: 'tasksDue-id' and 'trainingsDue-id'\n                for(let i = 0; i < vessels.length; i++){\n                    const vessel = vessels[i];\n                    localStorage.setItem(\"tasksDue-\".concat(vessel.id), vessel.tasksDue);\n                    localStorage.setItem(\"trainingsDue-\".concat(vessel.id), vessel.trainingsDue);\n                }\n                const vesselsWithStatus = vessels.map(function(vessel) {\n                    // const vesselWithStatus = vesselsWithLatestStatus.find(\n                    //     (item: any) => item.id == vessel.id,\n                    // )\n                    // const statusHistory =\n                    //     vesselWithStatus?.statusHistory.nodes ?? []\n                    // const status =\n                    //     statusHistory.length > 0 ? statusHistory[0] : null\n                    return {\n                        ...vessel,\n                        status: vessel.vesselStatus.Status\n                    };\n                });\n                handleSetVessels(vesselsWithStatus);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // const [getVessselsWithLatestStatus] = useLazyQuery(ReadVessels, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onError: (error: any) => {\n    //         console.error('getVessselsWithLatestStatus error', error)\n    //     },\n    // })\n    const loadVessels = async ()=>{\n        await queryVessels();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const onChangeStatusSuccess = (vessel, status)=>{\n        setVesselList((previousList)=>{\n            const dataIndex = previousList.findIndex((item)=>item.id == vessel.id);\n            if (dataIndex === -1) {\n                return previousList;\n            }\n            const newList = [\n                ...previousList\n            ];\n            vessel.status = status;\n            newList[dataIndex] = vessel;\n            return newList;\n        });\n    };\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_3__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                const vessel = row.original;\n                const vesselStatus = vessel.status;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: vesselStatus && vesselStatus.status !== \"OutOfService\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \" inline-flex flex-row gap-2 whitespace-nowrap items-center\",\n                        href: \"/vessel/info?id=\".concat(vessel.id, \"&name=\").concat(vessel.title),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-2 shrink-0 border-border rounded-full hidden small:inline-block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    vessel: vessel\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium truncate hover:text-curious-blue-400\",\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 37\n                                    }, this),\n                                    vessel.logentryID !== 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-curious-blue-400 text-[10px]\",\n                                        children: \"ON VOYAGE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 41\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-curious-blue-400 text-[10px]\",\n                                        children: \"READY FOR VOYAGE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"flex flex-row gap-2 whitespace-nowrap items-center\",\n                        href: \"/vessel/info?id=\".concat(vessel.id, \"&name=\").concat(vessel.title),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative hidden small:inline-block shrink-0 overflow-hidden border-2 border-destructive rounded-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        vessel: vessel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-destructive opacity-50 rounded-full\",\n                                        children: \"        \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium opacity-50 truncate\",\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block text-[10px] text-destructive\",\n                                        children: \"OUT OF SERVICE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"trainingsDue\",\n            header: \"Training\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const vessel = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 justify-center\",\n                    children: vessel.trainingsDue > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert !rounded-full flex w-8 h-8\",\n                        children: vessel.trainingsDue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"#00a396\",\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"tasksDue\",\n            header: \"Tasks\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const vessel = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 justify-center\",\n                    children: vessel.tasksDue > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert !rounded-full flex flex-shrink-0 w-8 h-8\",\n                        children: vessel.tasksDue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"#27AB83\",\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            enableHiding: false,\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const vessel = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_components_table_action_column__WEBPACK_IMPORTED_MODULE_8__.TableActionColumn, {\n                    vessel: vessel,\n                    onChangeStatusSuccess: (newStatus)=>onChangeStatusSuccess(vessel, newStatus)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 21\n                }, this);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex py-3 items-baseline gap-2 phablet:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_5__.SealogsVesselsIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/vessel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_7__.H1, {\n                            children: \"Vessels\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                lineNumber: 323,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessel_status_chart__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                startMonth: (0,_barrel_optimize_names_subMonths_date_fns__WEBPACK_IMPORTED_MODULE_11__.subMonths)(new Date(), 5),\n                endMonth: new Date()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                lineNumber: 329,\n                columnNumber: 13\n            }, this),\n            filteredVesselList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_3__.DataTable, {\n                        columns: columns,\n                        data: filteredVesselList.map((vessel)=>({\n                                ...vessel,\n                                trainingsDue: vessel.trainingsDue || 0,\n                                tasksDue: vessel.tasksDue || 0\n                            })),\n                        showToolbar: false,\n                        className: \"p-0 border-0 shadow-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/vessel\",\n                            className: \"text-accent-foreground uppercase hover:text-curious-blue-400 text-xs\",\n                            children: \"See all vessels\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n                lineNumber: 334,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\dashboard\\\\overview-components\\\\vessels\\\\vessels.tsx\",\n        lineNumber: 322,\n        columnNumber: 9\n    }, this);\n}\n_s(Vessels, \"doxjPODYEProbVuOr597zUlPQxI=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery\n    ];\n});\n_c = Vessels;\nvar _c;\n$RefreshReg$(_c, \"Vessels\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/dashboard/overview-components/vessels/vessels.tsx\n"));

/***/ })

});