"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/components/DateRange.tsx":
/*!**************************************!*\
  !*** ./src/components/DateRange.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,set!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/set.mjs\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst DatePicker = (param)=>{\n    let { onChange, className, placeholder = \"Select date\", mode = \"range\", type = \"date\", disabled = false, value, dateFormat = \"dd LLLL, y\", validation, numberOfMonths = mode === \"range\" ? 2 : 1, closeOnSelect = true, showWeekNumbers = false, includeTime = false, timeMode = \"single\", timeFormat = \"HH:mm\", timeInterval = 30, label, labelPosition = \"top\", clearable = false, icon, confirmSelection = true, confirmButtonText = \"Confirm\", modal = false, wrapperClassName = \"\", ...buttonProps } = param;\n    var _this = undefined;\n    _s();\n    const [dateValue, setDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || (mode === \"range\" ? {\n        from: undefined,\n        to: undefined\n    } : undefined));\n    // We'll use buttonProps directly but ensure we don't pass our custom type prop to the Button component\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toTime, setToTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State to track pending selection when confirmation button is enabled\n    const [pendingSelection, setPendingSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || undefined);\n    // Set placeholder based on mode\n    const actualPlaceholder = mode === \"range\" ? \"Select date range\" : \"Select date\";\n    // Optimized deep equality check for dates\n    const isDateEqual = (a, b)=>{\n        if (a === b) return true;\n        if (!a || !b) return false;\n        return a instanceof Date && b instanceof Date && a.getTime() === b.getTime();\n    };\n    const isValueEqual = (a, b)=>{\n        if (a === b) return true;\n        if (a instanceof Date && b instanceof Date) return isDateEqual(a, b);\n        if (a && b && typeof a === \"object\" && typeof b === \"object\" && \"from\" in a && \"to\" in a && \"from\" in b && \"to\" in b) {\n            return isDateEqual(a.from, b.from) && isDateEqual(a.to, b.to);\n        }\n        return false;\n    };\n    // Helper functions for time initialization\n    const getCurrentTime = ()=>{\n        const now = new Date();\n        return {\n            hour: now.getHours(),\n            minute: now.getMinutes()\n        };\n    };\n    const getTimeFromDate = (date)=>({\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        });\n    const initializeTime = (existingTime, date)=>{\n        if (existingTime) return existingTime;\n        if (date && (date.getHours() || date.getMinutes())) return getTimeFromDate(date);\n        return shouldIncludeTime ? getCurrentTime() : null;\n    };\n    // Helper function to reset date value based on mode\n    const getEmptyDateValue = ()=>mode === \"range\" ? {\n            from: undefined,\n            to: undefined\n        } : undefined;\n    // Helper function to clear all date and time state\n    const clearAllState = ()=>{\n        setDateValue(getEmptyDateValue());\n        setPendingSelection(undefined);\n        setTime(null);\n        setToTime(null);\n        onChange(null);\n    };\n    // Helper function to render icon consistently\n    const renderIcon = function() {\n        let className = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"text-muted-foreground\";\n        if (!icon) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"text-neutral-400\", className)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 224,\n            columnNumber: 17\n        }, _this);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(icon)) {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(icon, {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-5 h-5\", className)\n            });\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(icon, {\n            size: 20,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(className)\n        });\n    };\n    // Helper function to create Date from time object\n    const createDateFromTime = (timeObj)=>{\n        const date = new Date();\n        date.setHours(timeObj.hour, timeObj.minute, 0, 0);\n        return date;\n    };\n    // Helper function to get time picker value with confirmation logic\n    const getTimePickerValue = (currentTime, pendingDate)=>{\n        let resolvedTime = currentTime;\n        // Always preserve existing time when using confirmation\n        if (confirmSelection && pendingDate && !isNaN(pendingDate.getTime())) {\n            // If there's an existing time, always preserve it\n            if (currentTime) {\n                resolvedTime = currentTime;\n            } else {\n                // Only if there's no existing time, use time from pending date\n                const pendingHours = pendingDate.getHours();\n                const pendingMinutes = pendingDate.getMinutes();\n                if (pendingHours !== 0 || pendingMinutes !== 0) {\n                    resolvedTime = {\n                        hour: pendingHours,\n                        minute: pendingMinutes\n                    };\n                }\n            }\n        }\n        if (resolvedTime) {\n            return createDateFromTime(resolvedTime);\n        }\n        // Fallback to current time but DON'T modify existing time state\n        const now = new Date();\n        return now;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update if the value has actually changed\n        if (!isValueEqual(value, dateValue)) {\n            if (value) {\n                setDateValue(value);\n                // Also update pendingSelection with the value\n                setPendingSelection(value);\n                if (value instanceof Date) {\n                    const timeFromDate = initializeTime(null, value);\n                    if (timeFromDate) setTime(timeFromDate);\n                } else if (\"from\" in value && value.from instanceof Date) {\n                    const { from, to } = value;\n                    const fromTime = initializeTime(null, from);\n                    if (fromTime) setTime(fromTime);\n                    if (to instanceof Date) {\n                        const toTimeFromDate = initializeTime(null, to);\n                        if (toTimeFromDate) setToTime(toTimeFromDate);\n                    }\n                }\n            } else {\n                // When value is null/undefined, reset to initial state but maintain format\n                setDateValue(getEmptyDateValue());\n                setPendingSelection(undefined);\n            }\n        }\n    }, [\n        value,\n        mode\n    ]);\n    const validateDate = (date)=>{\n        if (!validation) return true;\n        const { minDate, maxDate, disabledDates, disabledDaysOfWeek } = validation;\n        if (minDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_12__.isBefore)(date, minDate)) return false;\n        if (maxDate && (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_13__.isAfter)(date, maxDate)) return false;\n        if (disabledDates === null || disabledDates === void 0 ? void 0 : disabledDates.some((disabledDate)=>(0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(disabledDate, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(date, \"yyyy-MM-dd\"))) return false;\n        if (disabledDaysOfWeek === null || disabledDaysOfWeek === void 0 ? void 0 : disabledDaysOfWeek.includes(date.getDay())) return false;\n        return true;\n    };\n    const applyTime = (date, t)=>date && t ? (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(date, {\n            hours: t.hour,\n            minutes: t.minute,\n            seconds: 0,\n            milliseconds: 0\n        }) : date;\n    const handleValueChange = (newValue)=>{\n        if (!newValue) {\n            // When a date is unselected, maintain the format consistency\n            setDateValue(getEmptyDateValue());\n            setPendingSelection(undefined);\n            onChange(null);\n            return;\n        }\n        if (mode === \"range\") {\n            const { from, to } = newValue;\n            if (from && !validateDate(from)) return;\n            if (to && !validateDate(to)) return;\n            // If confirmation is required, store the selection in pending state with preserved time\n            if (confirmSelection) {\n                let preservedFrom = from;\n                let preservedTo = to;\n                // Preserve existing time when setting pending selection\n                if (from && shouldIncludeTime && time) {\n                    const fromWithTime = applyTime(from, time);\n                    if (fromWithTime) {\n                        preservedFrom = fromWithTime;\n                    }\n                }\n                if (to && shouldIncludeTime && toTime) {\n                    const toWithTime = applyTime(to, toTime);\n                    if (toWithTime) {\n                        preservedTo = toWithTime;\n                    }\n                }\n                setPendingSelection({\n                    from: preservedFrom,\n                    to: preservedTo\n                });\n            } else {\n                finalizeSelection({\n                    from,\n                    to\n                });\n            }\n        } else {\n            const singleDate = newValue;\n            if (!validateDate(singleDate)) return;\n            // If confirmation is required, store the selection in pending state with preserved time\n            if (confirmSelection) {\n                let preservedDate = singleDate;\n                // Preserve existing time when setting pending selection\n                if (shouldIncludeTime && time) {\n                    const dateWithTime = applyTime(singleDate, time);\n                    if (dateWithTime) {\n                        preservedDate = dateWithTime;\n                    }\n                }\n                setPendingSelection(preservedDate);\n            } else {\n                finalizeSelection(singleDate, closeOnSelect && !shouldIncludeTime);\n            }\n        }\n    };\n    const handleTimeChange = function(date) {\n        let isToTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!date) return;\n        const newTime = {\n            hour: date.getHours(),\n            minute: date.getMinutes()\n        };\n        // Check if the time has actually changed before updating state\n        if (isToTime) {\n            const currentToTime = toTime;\n            if (!currentToTime || currentToTime.hour !== newTime.hour || currentToTime.minute !== newTime.minute) {\n                setToTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection && (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to)) {\n                    const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(pendingSelection.to, {\n                        hours: newTime.hour,\n                        minutes: newTime.minute,\n                        seconds: 0,\n                        milliseconds: 0\n                    });\n                    setPendingSelection({\n                        ...pendingSelection,\n                        to: newEndDate\n                    });\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.to) {\n                        const newEndDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(dateValue.to, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: dateValue.from,\n                            endDate: newEndDate\n                        });\n                    }\n                }\n            }\n        } else {\n            const currentTime = time;\n            if (!currentTime || currentTime.hour !== newTime.hour || currentTime.minute !== newTime.minute) {\n                setTime(newTime);\n                // Update pendingSelection with the new time\n                if (confirmSelection) {\n                    if (pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(pendingSelection.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection({\n                            ...pendingSelection,\n                            from: newStartDate\n                        });\n                    } else if (pendingSelection instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(pendingSelection, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        setPendingSelection(newDate);\n                    }\n                }\n                // If confirmation is not required, call onChange directly\n                if (!confirmSelection) {\n                    if (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) {\n                        const newStartDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(dateValue.from, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange({\n                            startDate: newStartDate,\n                            endDate: dateValue.to\n                        });\n                    } else if (dateValue instanceof Date) {\n                        const newDate = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_15__.set)(dateValue, {\n                            hours: newTime.hour,\n                            minutes: newTime.minute,\n                            seconds: 0,\n                            milliseconds: 0\n                        });\n                        onChange(newDate);\n                    }\n                }\n            }\n        }\n    };\n    const handleClear = (e)=>{\n        e.stopPropagation() // Prevent triggering the popover\n        ;\n        clearAllState();\n    };\n    // Function to handle clear button click inside the popover\n    const handleClearInPopover = ()=>{\n        clearAllState();\n        setOpen(false) // Close the popover after clearing\n        ;\n    };\n    // Helper function to finalize selection (used by both direct selection and confirmation)\n    const finalizeSelection = function(selection) {\n        let closePopover = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (mode === \"range\") {\n            const { from, to } = selection;\n            setDateValue({\n                from,\n                to\n            });\n            // NEVER change existing time when selecting new dates - preserve exactly as is\n            const currentTime = time;\n            const currentToTime = toTime;\n            const rangeResult = {\n                startDate: shouldIncludeTime ? applyTime(from, currentTime) : from,\n                endDate: shouldIncludeTime ? applyTime(to, currentToTime) : to\n            };\n            console.log(\"DateRange finalizeSelection - range mode result:\", rangeResult);\n            onChange(rangeResult);\n        } else {\n            const singleDate = selection;\n            setDateValue(singleDate);\n            // NEVER change existing time when selecting new dates - preserve exactly as is\n            const currentTime = time;\n            const result = shouldIncludeTime ? applyTime(singleDate, currentTime) : singleDate;\n            console.log(\"DateRange finalizeSelection - single mode result:\", result);\n            onChange(result);\n        }\n        if (closePopover) {\n            setOpen(false);\n            setPendingSelection(undefined);\n        }\n    };\n    // Function to handle confirmation button click\n    const handleConfirm = ()=>{\n        if (!pendingSelection) return;\n        console.log(\"DateRange handleConfirm - pendingSelection:\", pendingSelection);\n        finalizeSelection(pendingSelection, true);\n    };\n    // timeOptions removed as we're now using TimePicker component\n    // Determine if we should include time based on the type prop or the deprecated includeTime prop\n    const [shouldIncludeTime, setShouldIncludeTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(type === \"datetime\" || includeTime);\n    // Update shouldIncludeTime when type or includeTime changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShouldIncludeTime(type === \"datetime\" || includeTime);\n    }, [\n        type,\n        includeTime\n    ]);\n    // Detect mobile devices (below md breakpoint)\n    const bp = (0,_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const displayTimeFormat = shouldIncludeTime ? \"\".concat(dateFormat, \" \").concat(timeFormat) : dateFormat;\n    // Guard against invalid dates\n    const formatDateWithTime = (date)=>{\n        if (!date) return \"\";\n        const validDate = date instanceof Date ? date : new Date();\n        return isNaN(validDate.getTime()) ? \"\" : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(validDate, displayTimeFormat);\n    };\n    // Time picker implementation now uses the TimePicker component with mode option\n    if (disabled) {\n        // Use the provided value if available, otherwise use current date\n        const displayDate = (date)=>{\n            if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\n                return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(), dateFormat);\n            }\n            return (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(date, shouldIncludeTime ? displayTimeFormat : dateFormat);\n        };\n        // Format the date based on the mode and value\n        let displayValue;\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                displayValue = range.to ? \"\".concat(displayDate(range.from), \" - \").concat(displayDate(range.to)) : displayDate(range.from);\n            } else {\n                const currentFormatted = (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(), dateFormat);\n                displayValue = \"\".concat(currentFormatted, \" - \").concat(currentFormatted);\n            }\n        } else {\n            displayValue = dateValue instanceof Date ? displayDate(dateValue) : (0,_barrel_optimize_names_format_isAfter_isBefore_set_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(), dateFormat);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                    asChild: true,\n                    id: \"date\",\n                    position: labelPosition,\n                    disabled: disabled,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 660,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        id: \"date\",\n                        variant: \"outline\",\n                        disabled: disabled,\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"px-4 justify-start w-full\"),\n                        iconLeft: renderIcon(\"mr-[8.5px] w-5 h-5 text-neutral-400\"),\n                        ...buttonProps,\n                        type: \"button\",\n                        children: displayValue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 658,\n            columnNumber: 13\n        }, undefined);\n    }\n    const renderButtonLabel = ()=>{\n        if (mode === \"range\") {\n            const range = dateValue;\n            if (range === null || range === void 0 ? void 0 : range.from) {\n                return range.to ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        formatDateWithTime(range.from),\n                        \" -\",\n                        \" \",\n                        formatDateWithTime(range.to)\n                    ]\n                }, void 0, true) : formatDateWithTime(range.from);\n            }\n            // Use consistent date format for placeholder\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-muted-foreground\",\n                children: actualPlaceholder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 701,\n                columnNumber: 17\n            }, undefined);\n        } else if (dateValue) {\n            return formatDateWithTime(dateValue);\n        }\n        // Use consistent date format for placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-muted-foreground\",\n            children: actualPlaceholder\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 710,\n            columnNumber: 13\n        }, undefined);\n    };\n    // Shared content component for both Popover and Dialog\n    const renderDatePickerContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: mode === \"range\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                        autoFocus: true,\n                        mode: \"range\",\n                        month: (confirmSelection && pendingSelection ? pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from : dateValue === null || dateValue === void 0 ? void 0 : dateValue.from) || new Date(),\n                        captionLayout: \"dropdown\",\n                        selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                        onSelect: (value)=>{\n                            // Ensure we maintain the date format when a date is unselected\n                            handleValueChange(value);\n                        },\n                        numberOfMonths: numberOfMonths,\n                        showWeekNumber: showWeekNumbers,\n                        disabled: validation ? (date)=>!validateDate(date) : undefined\n                    }, \"range\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 21\n                    }, undefined),\n                    shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-t border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_6__.TimePicker, {\n                                value: getTimePickerValue(time, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.from),\n                                toValue: getTimePickerValue(toTime, pendingSelection === null || pendingSelection === void 0 ? void 0 : pendingSelection.to),\n                                onChange: handleTimeChange,\n                                onToChange: (date)=>handleTimeChange(date, true),\n                                // Always enable time picker regardless of whether a date is selected\n                                disabled: false,\n                                className: \"w-full\",\n                                mode: timeMode,\n                                label: timeMode === \"range\" ? \"Time Range\" : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 748,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 747,\n                        columnNumber: 25\n                    }, undefined),\n                    confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                className: \"my-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                children: [\n                                    clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-fit\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClearInPopover,\n                                            iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            className: \"w-fit\",\n                                            \"aria-label\": \"Clear date range\",\n                                            children: \"Clear\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleConfirm,\n                                        disabled: !pendingSelection,\n                                        iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        children: confirmButtonText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 778,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_3__.Calendar, {\n                        autoFocus: true,\n                        mode: \"single\",\n                        month: (confirmSelection && pendingSelection ? pendingSelection : dateValue) || new Date(),\n                        selected: confirmSelection && pendingSelection ? pendingSelection : dateValue,\n                        onSelect: (value)=>{\n                            // Ensure we maintain the date format when a date is unselected\n                            handleValueChange(value);\n                        },\n                        captionLayout: \"dropdown\",\n                        numberOfMonths: numberOfMonths,\n                        showWeekNumber: showWeekNumbers,\n                        disabled: validation ? (date)=>!validateDate(date) : undefined\n                    }, \"single\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 21\n                    }, undefined),\n                    shouldIncludeTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-t border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_6__.TimePicker, {\n                                value: getTimePickerValue(time, pendingSelection instanceof Date ? pendingSelection : undefined),\n                                toValue: getTimePickerValue(toTime, undefined),\n                                onChange: handleTimeChange,\n                                onToChange: (date)=>handleTimeChange(date, true),\n                                // Always enable time picker regardless of whether a date is selected\n                                disabled: false,\n                                className: \"w-full\",\n                                mode: timeMode,\n                                label: timeMode === \"range\" ? \"Time Range\" : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 25\n                    }, undefined),\n                    confirmSelection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                className: \"my-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"p-3 flex gap-3 justify-end\"),\n                                children: [\n                                    clearable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-fit\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClearInPopover,\n                                            iconLeft: _barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            \"aria-label\": \"Clear date\",\n                                            children: \"Clear\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                            lineNumber: 867,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleConfirm,\n                                        disabled: !pendingSelection,\n                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(clearable ? \"\" : \"w-full\"),\n                                        children: confirmButtonText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 802,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false);\n    // Shared trigger button component\n    const renderTriggerButton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    id: \"date\",\n                    variant: \"outline\",\n                    disabled: disabled,\n                    iconLeft: renderIcon(),\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"px-4 justify-start w-full\", clearable && \"pr-10\"),\n                    ...buttonProps,\n                    type: \"button\",\n                    children: renderButtonLabel()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 893,\n                    columnNumber: 13\n                }, undefined),\n                clearable && (dateValue instanceof Date || (dateValue === null || dateValue === void 0 ? void 0 : dateValue.from)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"button\",\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    className: \"absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0\",\n                    onClick: handleClear,\n                    \"aria-label\": \"Clear date\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 16,\n                        className: \"text-neutral-400 hover:text-background0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                    lineNumber: 909,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n            lineNumber: 892,\n            columnNumber: 9\n        }, undefined);\n    const handleOpenChange = (isOpen)=>{\n        // When opening, initialize pendingSelection with the current value\n        if (isOpen && !pendingSelection && dateValue) {\n            setPendingSelection(dateValue);\n        }\n        setOpen(isOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(wrapperClassName),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                id: \"date\",\n                position: labelPosition,\n                disabled: disabled,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 936,\n                columnNumber: 17\n            }, undefined),\n            bp.phablet ? // Mobile: Use Dialog\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: open,\n                onOpenChange: handleOpenChange,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTrigger, {\n                        asChild: true,\n                        children: renderTriggerButton()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 943,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                        className: \"gap-0 p-0 xs:w-fit flex flex-col overflow-auto rounded-md items-center\",\n                        children: renderDatePickerContent()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 946,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 942,\n                columnNumber: 17\n            }, undefined) : // Desktop: Use Popover\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.Popover, {\n                modal: modal,\n                open: open,\n                onOpenChange: handleOpenChange,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverTrigger, {\n                        asChild: true,\n                        children: renderTriggerButton()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 956,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_4__.PopoverContent, {\n                        className: \"w-auto p-0\",\n                        align: \"start\",\n                        children: renderDatePickerContent()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n                lineNumber: 952,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\DateRange.tsx\",\n        lineNumber: 934,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DatePicker, \"ZYw1VQK45WlfDj8pDEPfWcCokBc=\", false, function() {\n    return [\n        _hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = DatePicker;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DatePicker);\nvar _c;\n$RefreshReg$(_c, \"DatePicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DateRange.tsx\n"));

/***/ })

});